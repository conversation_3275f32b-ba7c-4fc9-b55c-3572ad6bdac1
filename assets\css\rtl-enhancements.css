/*
 * تحسينات إضافية للتصميم RTL
 * RTL Enhancements Stylesheet
 */

/* Enhanced Arabic Typography */
.arabic-text {
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
    line-height: 1.8;
    letter-spacing: 0.5px;
}

/* Enhanced Navbar RTL */
.enhanced-navbar .navbar-nav {
    direction: rtl;
}

.enhanced-navbar .nav-link {
    text-align: right;
}

.enhanced-navbar .navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.enhanced-navbar .navbar-collapse {
    text-align: right;
}

/* Enhanced Hero Section RTL */
.enhanced-hero .hero-content {
    text-align: right;
}

.enhanced-hero .hero-buttons {
    justify-content: flex-start;
}

/* Enhanced Cards RTL */
.goal-card .card-body,
.space-card .card-body {
    text-align: center;
    direction: rtl;
}

/* Enhanced News Slider RTL */
.news-slide {
    direction: rtl;
    text-align: center;
}

.news-icon {
    margin-right: 1rem;
    margin-left: 0;
}

/* Enhanced Animations for RTL */
.fade-in-right {
    transform: translateX(-30px);
}

@keyframes fadeInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تحسين عرض الأرقام العربية */
.arabic-numbers {
    font-feature-settings: "lnum" 1;
    direction: ltr;
    unicode-bidi: embed;
}

/* تحسين التنسيق للنصوص المختلطة */
.mixed-content {
    text-align: justify;
    text-justify: inter-word;
}

/* تحسين القوائم العربية */
.arabic-list {
    list-style-type: arabic-indic;
    padding-right: 2rem;
    padding-left: 0;
}

.arabic-list li {
    margin-bottom: 0.5rem;
    text-align: right;
}

/* تحسين الاقتباسات */
.quote-arabic {
    border-right: 4px solid var(--primary-color);
    border-left: none;
    padding-right: 1rem;
    padding-left: 0;
    margin: 1rem 0;
    font-style: italic;
    background-color: #f8f9fa;
    border-radius: 0 8px 8px 0;
}

/* تحسين التواريخ والأوقات */
.datetime-arabic {
    direction: rtl;
    text-align: right;
}

.datetime-arabic .time {
    direction: ltr;
    display: inline-block;
}

/* تحسين النماذج للغة العربية */
.form-arabic .form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.75rem;
}

.form-arabic .form-control {
    text-align: right;
    direction: rtl;
}

.form-arabic .form-control::placeholder {
    text-align: right;
    opacity: 0.7;
}

/* تحسين الجداول العربية */
.table-arabic {
    direction: rtl;
    text-align: right;
}

.table-arabic th,
.table-arabic td {
    text-align: right;
    padding: 1rem 0.75rem;
}

.table-arabic .table-actions {
    text-align: center;
    width: 120px;
}

/* تحسين البطاقات للمحتوى العربي */
.card-arabic {
    text-align: right;
    direction: rtl;
}

.card-arabic .card-title {
    font-weight: 700;
    color: var(--nibrass-blue);
    margin-bottom: 1rem;
}

.card-arabic .card-text {
    line-height: 1.8;
    color: #555;
}

/* تحسين التنقل والقوائم */
.nav-arabic .nav-link {
    text-align: right;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.nav-arabic .nav-link:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

.nav-arabic .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

/* تحسين الشارات والتسميات */
.badge-arabic {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.badge-success-arabic {
    background-color: var(--nibrass-green);
    color: white;
}

.badge-warning-arabic {
    background-color: var(--nibrass-orange);
    color: white;
}

.badge-info-arabic {
    background-color: var(--primary-color);
    color: white;
}

/* تحسين الأزرار للنصوص العربية */
.btn-arabic {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-arabic:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* تحسين التنبيهات للنصوص العربية */
.alert-arabic {
    text-align: right;
    direction: rtl;
    border-radius: 10px;
    padding: 1rem 1.5rem;
}

.alert-arabic .alert-heading {
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* تحسين المودال للنصوص العربية */
.modal-arabic .modal-header {
    text-align: right;
    direction: rtl;
    border-bottom: 2px solid var(--light-color);
}

.modal-arabic .modal-title {
    font-weight: 700;
    color: var(--nibrass-blue);
}

.modal-arabic .modal-body {
    text-align: right;
    direction: rtl;
    line-height: 1.8;
}

.modal-arabic .modal-footer {
    direction: rtl;
    justify-content: flex-start;
}

/* تحسين التبويبات */
.nav-tabs-arabic {
    border-bottom: 2px solid var(--light-color);
}

.nav-tabs-arabic .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    background: none;
    color: var(--dark-color);
    font-weight: 600;
    padding: 1rem 1.5rem;
}

.nav-tabs-arabic .nav-link.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
    background: none;
}

/* تحسين الأكورديون */
.accordion-arabic .accordion-header {
    text-align: right;
    direction: rtl;
}

.accordion-arabic .accordion-button {
    text-align: right;
    direction: rtl;
    font-weight: 600;
    background-color: var(--light-color);
    border: none;
    border-radius: 8px;
}

.accordion-arabic .accordion-button:not(.collapsed) {
    background-color: var(--primary-color);
    color: white;
}

.accordion-arabic .accordion-body {
    text-align: right;
    direction: rtl;
    line-height: 1.8;
    padding: 1.5rem;
}

/* تحسين البحث */
.search-arabic {
    position: relative;
}

.search-arabic .form-control {
    padding-right: 3rem;
    border-radius: 25px;
    border: 2px solid var(--light-color);
}

.search-arabic .search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
}

/* تحسين التقييمات والنجوم */
.rating-arabic {
    direction: ltr;
    text-align: left;
    display: inline-block;
}

.rating-arabic .star {
    color: #ffc107;
    font-size: 1.2rem;
    margin: 0 0.1rem;
}

.rating-arabic .star.empty {
    color: #e9ecef;
}

/* تحسين التقدم والشرائح */
.progress-arabic {
    direction: ltr;
    height: 8px;
    border-radius: 10px;
    background-color: var(--light-color);
}

.progress-arabic .progress-bar {
    border-radius: 10px;
    background: linear-gradient(90deg, var(--nibrass-green), var(--primary-color));
}

/* تحسين الخطوط للعناوين */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
    font-weight: 700;
    line-height: 1.4;
    color: var(--nibrass-blue);
}

/* تحسين الفقرات */
p {
    line-height: 1.8;
    margin-bottom: 1rem;
    color: #555;
}

/* تحسين الروابط */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--nibrass-blue);
    text-decoration: underline;
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--nibrass-green));
    border-radius: 10px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--nibrass-blue), var(--primary-color));
}

/* Enhanced RTL Specific Improvements */
.enhanced-navbar .navbar-brand {
    margin-left: 0;
    margin-right: auto;
}

.enhanced-navbar .navbar-nav .nav-link {
    padding-right: 1rem;
    padding-left: 1rem;
}

/* RTL Button Enhancements */
.btn-enhanced i {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* RTL Card Icon Positioning */
.card-icon {
    text-align: center;
    display: block;
}

/* RTL Footer Improvements */
.footer-content {
    text-align: right;
}

.footer ul {
    padding-right: 0;
    padding-left: 0;
}

/* RTL Responsive Improvements */
@media (max-width: 768px) {
    .enhanced-hero .hero-content {
        text-align: center;
    }

    .enhanced-hero .hero-buttons {
        justify-content: center;
    }

    .footer-content {
        text-align: center;
    }
}

/* RTL Animation Enhancements */
.typewriter {
    /* border-right: 3px solid var(--nibrass-orange); */
    border-left: none;
    padding-left: 10px;
    padding-right: 0;
}

/* Enhanced RTL Grid Layout */
.goals-grid {
    direction: rtl;
}

.goals-grid .goal-card {
    direction: ltr;
}

/* RTL News Slider Enhancements */
.carousel-control-prev i,
.carousel-control-next i {
    font-size: 1rem;
}

/* RTL Floating Elements */
.floating-elements {
    direction: ltr;
}

/* Enhanced RTL Typography */
h1, h2, h3, h4, h5, h6 {
    /* text-align: center; */
}

.section-title {
    text-align: center;
    position: relative;
    padding-bottom: 1rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--nibrass-orange));
    border-radius: 2px;
}
