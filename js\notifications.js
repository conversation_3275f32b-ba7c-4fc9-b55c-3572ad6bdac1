/**
 * Nibrass Real-time Notifications Client
 * Handles real-time notifications using Server-Sent Events
 */

class NibrassNotifications {
    constructor(options = {}) {
        this.options = {
            apiUrl: options.apiUrl || '/api/notifications.php',
            actionsUrl: options.actionsUrl || '/api/notification-actions.php',
            reconnectInterval: options.reconnectInterval || 5000,
            maxReconnectAttempts: options.maxReconnectAttempts || 10,
            showToasts: options.showToasts !== false,
            playSound: options.playSound !== false,
            ...options
        };
        
        this.eventSource = null;
        this.reconnectAttempts = 0;
        this.isConnected = false;
        this.notifications = [];
        this.unreadCount = 0;
        
        this.init();
    }
    
    init() {
        this.createNotificationContainer();
        this.connect();
        this.bindEvents();
    }
    
    // إنشاء حاوية الإشعارات
    createNotificationContainer() {
        if (document.getElementById('nibrass-notifications')) return;
        
        const container = document.createElement('div');
        container.id = 'nibrass-notifications';
        container.className = 'nibrass-notifications-container';
        container.innerHTML = `
            <div class="notifications-header">
                <h5><i class="fas fa-bell"></i> الإشعارات</h5>
                <div class="notifications-actions">
                    <button class="btn-mark-all-read" title="تحديد الكل كمقروء">
                        <i class="fas fa-check-double"></i>
                    </button>
                    <button class="btn-close-notifications" title="إغلاق">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="notifications-list" id="notifications-list">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                </div>
            </div>
        `;
        
        document.body.appendChild(container);
        
        // إضافة الأنماط
        this.addStyles();
    }
    
    // إضافة الأنماط المطلوبة
    addStyles() {
        if (document.getElementById('nibrass-notifications-styles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'nibrass-notifications-styles';
        styles.textContent = `
            .nibrass-notifications-container {
                position: fixed;
                top: 80px;
                right: 20px;
                width: 350px;
                max-height: 500px;
                background: white;
                border-radius: 15px;
                box-shadow: 0 20px 60px rgba(0,0,0,0.15);
                z-index: 10000;
                display: none;
                border: 2px solid #e9ecef;
                font-family: 'Cairo', 'Tajawal', sans-serif;
            }
            
            .notifications-header {
                padding: 1rem;
                border-bottom: 1px solid #e9ecef;
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: #4a90e2;
                color: white;
                border-radius: 13px 13px 0 0;
            }
            
            .notifications-header h5 {
                margin: 0;
                font-size: 1rem;
                font-weight: 600;
            }
            
            .notifications-actions {
                display: flex;
                gap: 0.5rem;
            }
            
            .notifications-actions button {
                background: rgba(255,255,255,0.2);
                border: none;
                color: white;
                padding: 0.5rem;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            
            .notifications-actions button:hover {
                background: rgba(255,255,255,0.3);
            }
            
            .notifications-list {
                max-height: 400px;
                overflow-y: auto;
                padding: 0;
            }
            
            .notification-item {
                padding: 1rem;
                border-bottom: 1px solid #f1f3f4;
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
            }
            
            .notification-item:hover {
                background: #f8f9fa;
            }
            
            .notification-item.unread {
                background: #e3f2fd;
                border-left: 4px solid #4a90e2;
            }
            
            .notification-item.unread::before {
                content: '';
                position: absolute;
                top: 1rem;
                right: 1rem;
                width: 8px;
                height: 8px;
                background: #4a90e2;
                border-radius: 50%;
            }
            
            .notification-title {
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
            }
            
            .notification-message {
                color: #6c757d;
                font-size: 0.85rem;
                line-height: 1.4;
                margin-bottom: 0.5rem;
            }
            
            .notification-time {
                color: #adb5bd;
                font-size: 0.75rem;
            }
            
            .notification-badge {
                position: absolute;
                top: -8px;
                right: -8px;
                background: #dc3545;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.7rem;
                font-weight: bold;
                min-width: 20px;
            }
            
            .notification-toast {
                position: fixed;
                top: 100px;
                right: 20px;
                background: white;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.15);
                padding: 1rem;
                max-width: 300px;
                z-index: 10001;
                border-left: 4px solid #4a90e2;
                animation: slideInRight 0.3s ease;
                font-family: 'Cairo', 'Tajawal', sans-serif;
            }
            
            .notification-toast.success { border-left-color: #28a745; }
            .notification-toast.warning { border-left-color: #ffc107; }
            .notification-toast.error { border-left-color: #dc3545; }
            
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            .loading {
                text-align: center;
                padding: 2rem;
                color: #6c757d;
            }
            
            .empty-notifications {
                text-align: center;
                padding: 2rem;
                color: #6c757d;
            }
            
            @media (max-width: 768px) {
                .nibrass-notifications-container {
                    right: 10px;
                    left: 10px;
                    width: auto;
                }
                
                .notification-toast {
                    right: 10px;
                    left: 10px;
                    max-width: none;
                }
            }
        `;
        
        document.head.appendChild(styles);
    }
    
    // الاتصال بـ Server-Sent Events
    connect() {
        if (this.eventSource) {
            this.eventSource.close();
        }
        
        this.eventSource = new EventSource(this.options.apiUrl);
        
        this.eventSource.onopen = () => {
            console.log('Notifications: Connected');
            this.isConnected = true;
            this.reconnectAttempts = 0;
        };
        
        this.eventSource.onerror = (error) => {
            console.error('Notifications: Connection error', error);
            this.isConnected = false;
            this.handleReconnect();
        };
        
        // معالجة الأحداث المختلفة
        this.eventSource.addEventListener('connected', (event) => {
            const data = JSON.parse(event.data);
            console.log('Notifications: Connected successfully', data);
        });
        
        this.eventSource.addEventListener('notification', (event) => {
            const notification = JSON.parse(event.data);
            this.handleNewNotification(notification);
        });
        
        this.eventSource.addEventListener('unread_count', (event) => {
            const data = JSON.parse(event.data);
            this.updateUnreadCount(data.count);
        });
        
        this.eventSource.addEventListener('heartbeat', (event) => {
            const data = JSON.parse(event.data);
            console.log('Notifications: Heartbeat', data.timestamp);
        });
        
        this.eventSource.addEventListener('test_notification', (event) => {
            const data = JSON.parse(event.data);
            console.log('Notifications: Test notification created', data);
        });
    }
    
    // معالجة إعادة الاتصال
    handleReconnect() {
        if (this.reconnectAttempts < this.options.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Notifications: Reconnecting... Attempt ${this.reconnectAttempts}`);
            
            setTimeout(() => {
                this.connect();
            }, this.options.reconnectInterval);
        } else {
            console.error('Notifications: Max reconnection attempts reached');
        }
    }
    
    // معالجة إشعار جديد
    handleNewNotification(notification) {
        this.notifications.unshift(notification);
        
        if (this.options.showToasts) {
            this.showToast(notification);
        }
        
        if (this.options.playSound) {
            this.playNotificationSound();
        }
        
        this.updateNotificationsList();
        this.triggerCallbacks('newNotification', notification);
    }
    
    // عرض Toast للإشعار
    showToast(notification) {
        const toast = document.createElement('div');
        toast.className = `notification-toast ${notification.type}`;
        toast.innerHTML = `
            <div class="notification-title">${notification.title}</div>
            <div class="notification-message">${notification.message}</div>
        `;
        
        document.body.appendChild(toast);
        
        // إزالة Toast بعد 5 ثوان
        setTimeout(() => {
            toast.style.animation = 'slideInRight 0.3s ease reverse';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 5000);
        
        // إضافة حدث النقر
        toast.addEventListener('click', () => {
            if (notification.action_url) {
                window.location.href = notification.action_url;
            }
            this.markAsRead(notification.id);
        });
    }
    
    // تشغيل صوت الإشعار
    playNotificationSound() {
        try {
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.volume = 0.3;
            audio.play().catch(() => {
                // تجاهل أخطاء تشغيل الصوت
            });
        } catch (error) {
            // تجاهل أخطاء إنشاء الصوت
        }
    }
    
    // تحديث عدد الإشعارات غير المقروءة
    updateUnreadCount(count) {
        this.unreadCount = count;
        
        // تحديث شارة الإشعارات في الواجهة
        const badges = document.querySelectorAll('.notification-badge');
        badges.forEach(badge => {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        });
        
        this.triggerCallbacks('unreadCountUpdate', count);
    }
    
    // تحديث قائمة الإشعارات
    updateNotificationsList() {
        const listElement = document.getElementById('notifications-list');
        if (!listElement) return;
        
        if (this.notifications.length === 0) {
            listElement.innerHTML = `
                <div class="empty-notifications">
                    <i class="fas fa-bell-slash fa-2x mb-2"></i>
                    <p>لا توجد إشعارات</p>
                </div>
            `;
            return;
        }
        
        listElement.innerHTML = this.notifications.map(notification => `
            <div class="notification-item ${!notification.is_read ? 'unread' : ''}" 
                 data-id="${notification.id}">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-message">${notification.message}</div>
                <div class="notification-time">${this.formatTime(notification.created_at)}</div>
            </div>
        `).join('');
        
        // إضافة أحداث النقر
        listElement.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', () => {
                const id = item.dataset.id;
                const notification = this.notifications.find(n => n.id == id);
                
                if (notification && !notification.is_read) {
                    this.markAsRead(id);
                }
                
                if (notification && notification.action_url) {
                    window.location.href = notification.action_url;
                }
            });
        });
    }
    
    // تنسيق الوقت
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // أقل من دقيقة
            return 'الآن';
        } else if (diff < 3600000) { // أقل من ساعة
            return `${Math.floor(diff / 60000)} د`;
        } else if (diff < 86400000) { // أقل من يوم
            return `${Math.floor(diff / 3600000)} س`;
        } else {
            return date.toLocaleDateString('ar-SA');
        }
    }
    
    // ربط الأحداث
    bindEvents() {
        // إغلاق قائمة الإشعارات
        document.addEventListener('click', (event) => {
            if (event.target.closest('.btn-close-notifications')) {
                this.hideNotifications();
            }
            
            if (event.target.closest('.btn-mark-all-read')) {
                this.markAllAsRead();
            }
        });
    }
    
    // إظهار قائمة الإشعارات
    showNotifications() {
        const container = document.getElementById('nibrass-notifications');
        if (container) {
            container.style.display = 'block';
            this.loadNotifications();
        }
    }
    
    // إخفاء قائمة الإشعارات
    hideNotifications() {
        const container = document.getElementById('nibrass-notifications');
        if (container) {
            container.style.display = 'none';
        }
    }
    
    // تبديل عرض قائمة الإشعارات
    toggleNotifications() {
        const container = document.getElementById('nibrass-notifications');
        if (container) {
            if (container.style.display === 'none' || !container.style.display) {
                this.showNotifications();
            } else {
                this.hideNotifications();
            }
        }
    }
    
    // تحميل الإشعارات من الخادم
    async loadNotifications() {
        try {
            const response = await fetch(`${this.options.actionsUrl}?action=recent&limit=20`);
            const data = await response.json();
            
            if (data.success) {
                this.notifications = data.data;
                this.updateNotificationsList();
            }
        } catch (error) {
            console.error('Error loading notifications:', error);
        }
    }
    
    // تحديد إشعار كمقروء
    async markAsRead(notificationId) {
        try {
            const response = await fetch(`${this.options.actionsUrl}?action=mark_read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: notificationId })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // تحديث الإشعار محلياً
                const notification = this.notifications.find(n => n.id == notificationId);
                if (notification) {
                    notification.is_read = 1;
                }
                
                this.updateNotificationsList();
                this.updateUnreadCount(this.unreadCount - 1);
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }
    
    // تحديد جميع الإشعارات كمقروءة
    async markAllAsRead() {
        try {
            const response = await fetch(`${this.options.actionsUrl}?action=mark_all_read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                // تحديث جميع الإشعارات محلياً
                this.notifications.forEach(notification => {
                    notification.is_read = 1;
                });
                
                this.updateNotificationsList();
                this.updateUnreadCount(0);
            }
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
        }
    }
    
    // إضافة callback للأحداث
    on(event, callback) {
        if (!this.callbacks) {
            this.callbacks = {};
        }
        
        if (!this.callbacks[event]) {
            this.callbacks[event] = [];
        }
        
        this.callbacks[event].push(callback);
    }
    
    // تشغيل callbacks
    triggerCallbacks(event, data) {
        if (this.callbacks && this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Notification callback error:', error);
                }
            });
        }
    }
    
    // إغلاق الاتصال
    disconnect() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        this.isConnected = false;
    }
    
    // إعادة الاتصال
    reconnect() {
        this.disconnect();
        this.reconnectAttempts = 0;
        this.connect();
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إنشاء مثيل عام للإشعارات
    window.nibrassNotifications = new NibrassNotifications();
    
    // إضافة زر الإشعارات للشريط العلوي إذا لم يكن موجوداً
    const navbar = document.querySelector('.navbar');
    if (navbar && !document.querySelector('.notifications-trigger')) {
        const notificationBtn = document.createElement('button');
        notificationBtn.className = 'btn btn-link text-white notifications-trigger position-relative';
        notificationBtn.innerHTML = `
            <i class="fas fa-bell"></i>
            <span class="notification-badge" style="display: none;">0</span>
        `;
        
        notificationBtn.addEventListener('click', () => {
            window.nibrassNotifications.toggleNotifications();
        });
        
        // إضافة الزر للشريط العلوي
        const navbarNav = navbar.querySelector('.navbar-nav');
        if (navbarNav) {
            const li = document.createElement('li');
            li.className = 'nav-item';
            li.appendChild(notificationBtn);
            navbarNav.appendChild(li);
        }
    }
});
