<?php
/**
 * ملف تشغيل إعداد الجداول المفقودة
 * Run Missing Tables Setup
 */

require_once 'config.php';
require_once 'db.php';

echo "بدء إعداد الجداول المفقودة...\n";

try {
    // قراءة ملف SQL
    $sql_file = 'database/create_missing_tables.sql';
    if (!file_exists($sql_file)) {
        throw new Exception('ملف SQL غير موجود: ' . $sql_file);
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // تقسيم الاستعلامات
    $statements = array_filter(
        array_map('trim', explode(';', $sql_content)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    $db->beginTransaction();
    
    $created_tables = [];
    $inserted_data = [];
    
    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $db->exec($statement);
                
                // استخراج اسم الجدول للرسالة
                if (preg_match('/CREATE TABLE.*?`?(\w+)`?\s*\(/i', $statement, $matches)) {
                    $created_tables[] = $matches[1];
                    echo "✅ تم إنشاء جدول: " . $matches[1] . "\n";
                } elseif (preg_match('/INSERT.*?INTO\s+`?(\w+)`?/i', $statement, $matches)) {
                    $inserted_data[] = $matches[1];
                    echo "📝 تم إدراج بيانات في جدول: " . $matches[1] . "\n";
                }
            } catch (PDOException $e) {
                // تجاهل إذا كان الجدول موجود بالفعل
                if (strpos($e->getMessage(), 'already exists') === false && 
                    strpos($e->getMessage(), 'Duplicate entry') === false) {
                    throw $e;
                }
            }
        }
    }
    
    $db->commit();
    
    echo "\n🎉 تم إنشاء جميع الجداول بنجاح!\n";
    echo "الجداول المنشأة: " . implode(', ', $created_tables) . "\n";
    echo "البيانات المدرجة: " . implode(', ', array_unique($inserted_data)) . "\n";
    
    // إنشاء ملف إكمال
    file_put_contents('tables_setup_complete.txt', date('Y-m-d H:i:s'));
    echo "✅ تم إنشاء ملف إكمال الإعداد\n";
    
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    echo "❌ خطأ في إنشاء الجداول: " . $e->getMessage() . "\n";
    exit(1);
}

// التحقق من الجداول الموجودة
echo "\n📋 التحقق من الجداول الموجودة:\n";
try {
    $result = $db->query("SHOW TABLES");
    $tables = $result->fetchAll(PDO::FETCH_COLUMN);
    
    $required_tables = [
        'platform_settings',
        'notifications', 
        'user_notifications',
        'student_progress',
        'achievements',
        'student_achievements',
        'activities',
        'educational_videos',
        'stories',
        'consultation_requests',
        'appointments',
        'educational_resources'
    ];
    
    foreach ($required_tables as $table) {
        if (in_array($table, $tables)) {
            echo "✅ $table - موجود\n";
        } else {
            echo "❌ $table - مفقود\n";
        }
    }
    
} catch (PDOException $e) {
    echo "خطأ في التحقق من الجداول: " . $e->getMessage() . "\n";
}

echo "\n🚀 انتهى إعداد قاعدة البيانات!\n";
?>
