<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كطالب
require_student();

$user = current_user();

// إعداد متغيرات الصفحة
$page_title = 'الرسم والتلوين';
$page_description = 'استوديو الرسم والتلوين التفاعلي';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
.drawing-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.drawing-header h1,
.drawing-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.drawing-studio {
    background: white;
    border-radius: 25px;
    padding: 2rem;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.canvas-container {
    background: #f8f9fa;
    border: 3px dashed #dee2e6;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.drawing-canvas {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    cursor: crosshair;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.tools-panel {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.color-palette {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
    gap: 0.5rem;
    margin: 1rem 0;
}

.color-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.color-btn:hover,
.color-btn.active {
    border-color: #333;
    transform: scale(1.1);
}

.brush-sizes {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin: 1rem 0;
}

.brush-size {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #dee2e6;
    background: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.brush-size:hover,
.brush-size.active {
    border-color: #4a90e2;
    background: #4a90e2;
    color: white;
}

.brush-size::after {
    content: '';
    background: currentColor;
    border-radius: 50%;
}

.brush-size.small::after { width: 4px; height: 4px; }
.brush-size.medium::after { width: 8px; height: 8px; }
.brush-size.large::after { width: 12px; height: 12px; }

.drawing-templates {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.template-item {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.template-item:hover {
    border-color: #4a90e2;
    background: #e3f2fd;
    transform: translateY(-3px);
}

.template-icon {
    font-size: 2rem;
    color: #4a90e2;
    margin-bottom: 0.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.btn-drawing {
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-clear {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.btn-save {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
}

.btn-load {
    background: linear-gradient(135deg, #45b7d1, #2980b9);
    color: white;
}

.btn-drawing:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* تأكيد ظهور الأيقونات */
.drawing-header i,
.tools-panel i,
.template-item i,
.btn i {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

@media (max-width: 768px) {
    .canvas-container {
        padding: 1rem;
        min-height: 300px;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-drawing {
        width: 100%;
        max-width: 200px;
    }
}
</style>

<!-- قسم العنوان -->
<section class="nibrass-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-paint-brush"></i> استوديو الرسم والتلوين</h1>
            <p class="lead">أطلق إبداعك وارسم أجمل اللوحات الفنية</p>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <div class="row">
            <!-- أدوات الرسم -->
            <div class="col-lg-3">
                <div class="tools-panel">
                    <h4><i class="fas fa-palette"></i> أدوات الرسم</h4>
                    
                    <!-- لوحة الألوان -->
                    <div class="mt-3">
                        <h6>الألوان</h6>
                        <div class="color-palette">
                            <div class="color-btn active" style="background: #000000" data-color="#000000"></div>
                            <div class="color-btn" style="background: #ff0000" data-color="#ff0000"></div>
                            <div class="color-btn" style="background: #00ff00" data-color="#00ff00"></div>
                            <div class="color-btn" style="background: #0000ff" data-color="#0000ff"></div>
                            <div class="color-btn" style="background: #ffff00" data-color="#ffff00"></div>
                            <div class="color-btn" style="background: #ff00ff" data-color="#ff00ff"></div>
                            <div class="color-btn" style="background: #00ffff" data-color="#00ffff"></div>
                            <div class="color-btn" style="background: #ffa500" data-color="#ffa500"></div>
                            <div class="color-btn" style="background: #800080" data-color="#800080"></div>
                            <div class="color-btn" style="background: #ffc0cb" data-color="#ffc0cb"></div>
                            <div class="color-btn" style="background: #a52a2a" data-color="#a52a2a"></div>
                            <div class="color-btn" style="background: #808080" data-color="#808080"></div>
                        </div>
                    </div>
                    
                    <!-- أحجام الفرشاة -->
                    <div class="mt-3">
                        <h6>حجم الفرشاة</h6>
                        <div class="brush-sizes">
                            <div class="brush-size small" data-size="2"></div>
                            <div class="brush-size medium active" data-size="5"></div>
                            <div class="brush-size large" data-size="10"></div>
                        </div>
                    </div>
                    
                    <!-- أدوات إضافية -->
                    <div class="mt-3">
                        <h6>الأدوات</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" id="penTool">
                                <i class="fas fa-pen"></i> قلم
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" id="eraserTool">
                                <i class="fas fa-eraser"></i> ممحاة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- منطقة الرسم -->
            <div class="col-lg-9">
                <div class="drawing-studio">
                    <div class="canvas-container">
                        <canvas id="drawingCanvas" class="drawing-canvas" width="600" height="400">
                            متصفحك لا يدعم عنصر Canvas
                        </canvas>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="btn-drawing btn-clear" id="clearCanvas">
                            <i class="fas fa-trash"></i> مسح الكل
                        </button>
                        <button class="btn-drawing btn-save" id="saveDrawing">
                            <i class="fas fa-save"></i> حفظ الرسمة
                        </button>
                        <button class="btn-drawing btn-load" id="loadTemplate">
                            <i class="fas fa-image"></i> تحميل قالب
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- قوالب الرسم -->
        <div class="drawing-templates">
            <h3><i class="fas fa-images"></i> قوالب للتلوين</h3>
            <p class="text-muted">اختر قالباً جاهزاً وابدأ بتلوينه</p>
            
            <div class="template-grid">
                <div class="template-item" data-template="flower">
                    <div class="template-icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <h6>زهرة</h6>
                </div>
                
                <div class="template-item" data-template="house">
                    <div class="template-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h6>منزل</h6>
                </div>
                
                <div class="template-item" data-template="car">
                    <div class="template-icon">
                        <i class="fas fa-car"></i>
                    </div>
                    <h6>سيارة</h6>
                </div>
                
                <div class="template-item" data-template="tree">
                    <div class="template-icon">
                        <i class="fas fa-tree"></i>
                    </div>
                    <h6>شجرة</h6>
                </div>
                
                <div class="template-item" data-template="sun">
                    <div class="template-icon">
                        <i class="fas fa-sun"></i>
                    </div>
                    <h6>شمس</h6>
                </div>
                
                <div class="template-item" data-template="animal">
                    <div class="template-icon">
                        <i class="fas fa-cat"></i>
                    </div>
                    <h6>حيوان</h6>
                </div>
            </div>
        </div>
        
        <!-- العودة للوحة التحكم -->
        <div class="text-center mt-4">
            <a href="dashboard.php" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    // متغيرات الرسم
    let canvas = document.getElementById('drawingCanvas');
    let ctx = canvas.getContext('2d');
    let isDrawing = false;
    let currentColor = '#000000';
    let currentSize = 5;
    let currentTool = 'pen';
    
    // إعداد Canvas
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;
    
    // أحداث الرسم
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);
    
    // أحداث اللمس للأجهزة المحمولة
    canvas.addEventListener('touchstart', handleTouch);
    canvas.addEventListener('touchmove', handleTouch);
    canvas.addEventListener('touchend', stopDrawing);
    
    function startDrawing(e) {
        isDrawing = true;
        draw(e);
    }
    
    function draw(e) {
        if (!isDrawing) return;
        
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        ctx.lineWidth = currentSize;
        ctx.lineCap = 'round';
        
        if (currentTool === 'pen') {
            ctx.globalCompositeOperation = 'source-over';
            ctx.strokeStyle = currentColor;
        } else if (currentTool === 'eraser') {
            ctx.globalCompositeOperation = 'destination-out';
        }
        
        ctx.lineTo(x, y);
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(x, y);
    }
    
    function stopDrawing() {
        if (isDrawing) {
            isDrawing = false;
            ctx.beginPath();
        }
    }
    
    function handleTouch(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 
                                         e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        canvas.dispatchEvent(mouseEvent);
    }
    
    // اختيار الألوان
    document.querySelectorAll('.color-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelector('.color-btn.active').classList.remove('active');
            this.classList.add('active');
            currentColor = this.dataset.color;
        });
    });
    
    // اختيار حجم الفرشاة
    document.querySelectorAll('.brush-size').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelector('.brush-size.active').classList.remove('active');
            this.classList.add('active');
            currentSize = parseInt(this.dataset.size);
        });
    });
    
    // أدوات الرسم
    document.getElementById('penTool').addEventListener('click', function() {
        currentTool = 'pen';
        this.classList.add('active');
        document.getElementById('eraserTool').classList.remove('active');
    });
    
    document.getElementById('eraserTool').addEventListener('click', function() {
        currentTool = 'eraser';
        this.classList.add('active');
        document.getElementById('penTool').classList.remove('active');
    });
    
    // مسح الكل
    document.getElementById('clearCanvas').addEventListener('click', function() {
        if (confirm('هل أنت متأكد من مسح الرسمة؟')) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
    });
    
    // حفظ الرسمة
    document.getElementById('saveDrawing').addEventListener('click', function() {
        const link = document.createElement('a');
        link.download = 'رسمتي_' + new Date().getTime() + '.png';
        link.href = canvas.toDataURL();
        link.click();
        
        // إظهار رسالة نجاح
        alert('تم حفظ رسمتك بنجاح!');
    });
    
    // تحميل القوالب
    document.querySelectorAll('.template-item').forEach(item => {
        item.addEventListener('click', function() {
            const template = this.dataset.template;
            loadTemplate(template);
        });
    });
    
    function loadTemplate(templateName) {
        // مسح الكانفاس أولاً
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // رسم قالب بسيط حسب النوع
        ctx.strokeStyle = '#cccccc';
        ctx.lineWidth = 2;
        
        switch(templateName) {
            case 'flower':
                drawFlower();
                break;
            case 'house':
                drawHouse();
                break;
            case 'car':
                drawCar();
                break;
            case 'tree':
                drawTree();
                break;
            case 'sun':
                drawSun();
                break;
            case 'animal':
                drawAnimal();
                break;
        }
    }
    
    function drawFlower() {
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        
        // رسم الساق
        ctx.beginPath();
        ctx.moveTo(centerX, centerY + 50);
        ctx.lineTo(centerX, centerY + 150);
        ctx.stroke();
        
        // رسم البتلات
        for (let i = 0; i < 6; i++) {
            ctx.beginPath();
            ctx.arc(centerX + Math.cos(i * Math.PI / 3) * 30, 
                   centerY + Math.sin(i * Math.PI / 3) * 30, 20, 0, 2 * Math.PI);
            ctx.stroke();
        }
        
        // رسم المركز
        ctx.beginPath();
        ctx.arc(centerX, centerY, 15, 0, 2 * Math.PI);
        ctx.stroke();
    }
    
    function drawHouse() {
        const x = canvas.width / 2 - 80;
        const y = canvas.height / 2 - 60;
        
        // رسم المنزل
        ctx.beginPath();
        ctx.rect(x, y, 160, 120);
        ctx.stroke();
        
        // رسم السقف
        ctx.beginPath();
        ctx.moveTo(x - 20, y);
        ctx.lineTo(x + 80, y - 60);
        ctx.lineTo(x + 180, y);
        ctx.closePath();
        ctx.stroke();
        
        // رسم الباب
        ctx.beginPath();
        ctx.rect(x + 60, y + 60, 40, 60);
        ctx.stroke();
        
        // رسم النوافذ
        ctx.beginPath();
        ctx.rect(x + 20, y + 30, 30, 30);
        ctx.rect(x + 110, y + 30, 30, 30);
        ctx.stroke();
    }
    
    function drawCar() {
        const x = canvas.width / 2 - 80;
        const y = canvas.height / 2;
        
        // رسم جسم السيارة
        ctx.beginPath();
        ctx.rect(x, y, 160, 60);
        ctx.stroke();
        
        // رسم النوافذ
        ctx.beginPath();
        ctx.rect(x + 20, y - 40, 120, 40);
        ctx.stroke();
        
        // رسم العجلات
        ctx.beginPath();
        ctx.arc(x + 30, y + 60, 20, 0, 2 * Math.PI);
        ctx.arc(x + 130, y + 60, 20, 0, 2 * Math.PI);
        ctx.stroke();
    }
    
    function drawTree() {
        const x = canvas.width / 2;
        const y = canvas.height / 2;
        
        // رسم الجذع
        ctx.beginPath();
        ctx.rect(x - 15, y, 30, 100);
        ctx.stroke();
        
        // رسم الأوراق
        ctx.beginPath();
        ctx.arc(x, y - 20, 60, 0, 2 * Math.PI);
        ctx.stroke();
    }
    
    function drawSun() {
        const x = canvas.width / 2;
        const y = canvas.height / 2;
        
        // رسم الشمس
        ctx.beginPath();
        ctx.arc(x, y, 50, 0, 2 * Math.PI);
        ctx.stroke();
        
        // رسم الأشعة
        for (let i = 0; i < 8; i++) {
            const angle = (i * Math.PI) / 4;
            ctx.beginPath();
            ctx.moveTo(x + Math.cos(angle) * 60, y + Math.sin(angle) * 60);
            ctx.lineTo(x + Math.cos(angle) * 80, y + Math.sin(angle) * 80);
            ctx.stroke();
        }
    }
    
    function drawAnimal() {
        const x = canvas.width / 2;
        const y = canvas.height / 2;
        
        // رسم الجسم
        ctx.beginPath();
        ctx.arc(x, y, 40, 0, 2 * Math.PI);
        ctx.stroke();
        
        // رسم الرأس
        ctx.beginPath();
        ctx.arc(x, y - 60, 30, 0, 2 * Math.PI);
        ctx.stroke();
        
        // رسم الأذنين
        ctx.beginPath();
        ctx.arc(x - 20, y - 80, 10, 0, 2 * Math.PI);
        ctx.arc(x + 20, y - 80, 10, 0, 2 * Math.PI);
        ctx.stroke();
        
        // رسم الأرجل
        ctx.beginPath();
        ctx.moveTo(x - 25, y + 40);
        ctx.lineTo(x - 25, y + 70);
        ctx.moveTo(x + 25, y + 40);
        ctx.lineTo(x + 25, y + 70);
        ctx.stroke();
    }
";

// تضمين الفوتر
include '../includes/footer.php';
?>
