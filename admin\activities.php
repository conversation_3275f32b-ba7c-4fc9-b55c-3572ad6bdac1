<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كمدير
require_admin();

$user = current_user();

// معالجة الإجراءات
$action = $_GET['action'] ?? '';
$success_message = '';
$error_message = '';

// معالجة إضافة نشاط جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_activity'])) {
    check_csrf();
    
    $title = clean_input($_POST['title']);
    $description = clean_input($_POST['description']);
    $category = clean_input($_POST['category']);
    $difficulty_level = clean_input($_POST['difficulty_level']);
    $target_age = (int)$_POST['target_age'];
    $duration_minutes = (int)$_POST['duration_minutes'];
    $points_reward = (int)$_POST['points_reward'];
    $instructions = clean_input($_POST['instructions']);
    
    if (empty($title) || empty($description) || empty($category)) {
        $error_message = "يرجى ملء جميع الحقول المطلوبة";
    } else {
        try {
            $insert_query = "INSERT INTO activities (title, description, category, difficulty_level, target_age, duration_minutes, points_reward, instructions) 
                            VALUES (:title, :description, :category, :difficulty_level, :target_age, :duration_minutes, :points_reward, :instructions)";
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':title', $title);
            $insert_stmt->bindParam(':description', $description);
            $insert_stmt->bindParam(':category', $category);
            $insert_stmt->bindParam(':difficulty_level', $difficulty_level);
            $insert_stmt->bindParam(':target_age', $target_age);
            $insert_stmt->bindParam(':duration_minutes', $duration_minutes);
            $insert_stmt->bindParam(':points_reward', $points_reward);
            $insert_stmt->bindParam(':instructions', $instructions);
            
            if ($insert_stmt->execute()) {
                $success_message = "تم إضافة النشاط بنجاح";
            } else {
                $error_message = "فشل في إضافة النشاط";
            }
        } catch (PDOException $e) {
            $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
        }
    }
}

// معالجة تفعيل/إلغاء تفعيل النشاط
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['toggle_activity'])) {
    check_csrf();
    
    $activity_id = (int)$_POST['activity_id'];
    $current_status = (int)$_POST['current_status'];
    $new_status = $current_status ? 0 : 1;
    
    try {
        $update_query = "UPDATE activities SET is_active = :status WHERE id = :activity_id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':status', $new_status);
        $update_stmt->bindParam(':activity_id', $activity_id);
        
        if ($update_stmt->execute()) {
            $action_text = $new_status ? 'تفعيل' : 'إلغاء تفعيل';
            $success_message = "تم {$action_text} النشاط بنجاح";
        } else {
            $error_message = "فشل في تحديث حالة النشاط";
        }
    } catch (PDOException $e) {
        $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
    }
}

// معالجة حذف النشاط
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_activity'])) {
    check_csrf();
    
    $activity_id = (int)$_POST['activity_id'];
    
    try {
        $delete_query = "DELETE FROM activities WHERE id = :activity_id";
        $delete_stmt = $db->prepare($delete_query);
        $delete_stmt->bindParam(':activity_id', $activity_id);
        
        if ($delete_stmt->execute()) {
            $success_message = "تم حذف النشاط بنجاح";
        } else {
            $error_message = "فشل في حذف النشاط";
        }
    } catch (PDOException $e) {
        $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
    }
}

// جلب الأنشطة مع الفلترة والبحث
$search = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';
$difficulty_filter = $_GET['difficulty'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$limit = 15;
$offset = ($page - 1) * $limit;

try {
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(title LIKE :search OR description LIKE :search)";
        $params[':search'] = "%{$search}%";
    }
    
    if (!empty($category_filter)) {
        $where_conditions[] = "category = :category";
        $params[':category'] = $category_filter;
    }
    
    if (!empty($difficulty_filter)) {
        $where_conditions[] = "difficulty_level = :difficulty";
        $params[':difficulty'] = $difficulty_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // جلب الأنشطة
    $activities_query = "SELECT * FROM activities {$where_clause} ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
    $activities_stmt = $db->prepare($activities_query);
    
    foreach ($params as $key => $value) {
        $activities_stmt->bindValue($key, $value);
    }
    $activities_stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $activities_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $activities_stmt->execute();
    $activities = $activities_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب العدد الإجمالي
    $count_query = "SELECT COUNT(*) as total FROM activities {$where_clause}";
    $count_stmt = $db->prepare($count_query);
    
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    
    $count_stmt->execute();
    $total_activities = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_activities / $limit);
    
} catch (PDOException $e) {
    $error_message = "خطأ في جلب الأنشطة: " . $e->getMessage();
    $activities = [];
    $total_activities = 0;
    $total_pages = 0;
}

// إعداد متغيرات الصفحة
$page_title = 'إدارة الأنشطة التعليمية';
$page_description = 'إدارة الأنشطة والألعاب التعليمية في منصة نبراس';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
.admin-container {
    /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
    min-height: 100vh;
    padding: 2rem 0;
}

.admin-header {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 5rem;
}

.add-activity-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.filters-section {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.activity-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 5px solid;
}

.activity-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.activity-card.math { border-left-color: #4a90e2; }
.activity-card.language { border-left-color: #28a745; }
.activity-card.science { border-left-color: #ffc107; }
.activity-card.art { border-left-color: #dc3545; }
.activity-card.general { border-left-color: #6c757d; }

.activity-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.activity-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.activity-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.activity-meta {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.85rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
}

.category-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: inline-block;
}

.category-badge.math {
    background: #e3f2fd;
    color: #1976d2;
}

.category-badge.language {
    background: #e8f5e8;
    color: #2e7d32;
}

.category-badge.science {
    background: #fff3e0;
    color: #f57c00;
}

.category-badge.art {
    background: #ffebee;
    color: #c62828;
}

.category-badge.general {
    background: #f5f5f5;
    color: #424242;
}

.difficulty-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.difficulty-badge.easy {
    background: #d4edda;
    color: #155724;
}

.difficulty-badge.medium {
    background: #fff3cd;
    color: #856404;
}

.difficulty-badge.hard {
    background: #f8d7da;
    color: #721c24;
}

.activity-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.status-indicator {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.status-indicator.active {
    background: #28a745;
}

.status-indicator.inactive {
    background: #dc3545;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.search-filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1rem;
    align-items: end;
}

@media (max-width: 768px) {
    .activities-grid {
        grid-template-columns: 1fr;
    }
    
    .search-filters {
        grid-template-columns: 1fr;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .activity-actions {
        flex-direction: column;
    }
}
</style>

<?php include 'includes/admin-nav.php'; ?>

<div class="admin-container">
    <div class="container admin-content mx-auto">
        <!-- رأس الصفحة -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-gamepad"></i> إدارة الأنشطة التعليمية</h1>
                    <p class="text-muted mb-0">إدارة الأنشطة والألعاب التعليمية في منصة نبراس</p>
                </div>
                <div>
                    <a href="dashboard.php" class="nibrass-btn nibrass-btn-outline-primary">
                        <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- إضافة نشاط جديد -->
        <div class="add-activity-section">
            <h3><i class="fas fa-plus-circle"></i> إضافة نشاط جديد</h3>
            
            <form method="POST">
                <?php echo csrf_field(); ?>
                
                <div class="form-grid">
                    <div>
                        <label class="form-label">عنوان النشاط *</label>
                        <input type="text" name="title" class="form-control" required>
                    </div>
                    
                    <div>
                        <label class="form-label">الفئة *</label>
                        <select name="category" class="form-select" required>
                            <option value="">اختر الفئة</option>
                            <option value="math">الرياضيات</option>
                            <option value="language">اللغة العربية</option>
                            <option value="science">العلوم</option>
                            <option value="art">الفنون</option>
                            <option value="general">عام</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="form-label">مستوى الصعوبة</label>
                        <select name="difficulty_level" class="form-select">
                            <option value="easy">سهل</option>
                            <option value="medium">متوسط</option>
                            <option value="hard">صعب</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="form-label">العمر المستهدف</label>
                        <input type="number" name="target_age" class="form-control" value="6" min="3" max="18">
                    </div>
                    
                    <div>
                        <label class="form-label">المدة (دقيقة)</label>
                        <input type="number" name="duration_minutes" class="form-control" value="15" min="5" max="120">
                    </div>
                    
                    <div>
                        <label class="form-label">نقاط المكافأة</label>
                        <input type="number" name="points_reward" class="form-control" value="10" min="1" max="100">
                    </div>
                </div>
                
                <div class="mt-3">
                    <label class="form-label">وصف النشاط *</label>
                    <textarea name="description" class="form-control" rows="3" required></textarea>
                </div>
                
                <div class="mt-3">
                    <label class="form-label">تعليمات النشاط</label>
                    <textarea name="instructions" class="form-control" rows="3"></textarea>
                </div>
                
                <div class="mt-3">
                    <button type="submit" name="add_activity" class="nibrass-btn nibrass-btn-success">
                        <i class="fas fa-plus"></i> إضافة النشاط
                    </button>
                </div>
            </form>
        </div>

        <!-- فلاتر البحث -->
        <div class="filters-section">
            <form method="GET" class="search-filters">
                <div>
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث في العنوان أو الوصف..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div>
                    <label class="form-label">الفئة</label>
                    <select name="category" class="form-select">
                        <option value="">جميع الفئات</option>
                        <option value="math" <?php echo $category_filter === 'math' ? 'selected' : ''; ?>>الرياضيات</option>
                        <option value="language" <?php echo $category_filter === 'language' ? 'selected' : ''; ?>>اللغة العربية</option>
                        <option value="science" <?php echo $category_filter === 'science' ? 'selected' : ''; ?>>العلوم</option>
                        <option value="art" <?php echo $category_filter === 'art' ? 'selected' : ''; ?>>الفنون</option>
                        <option value="general" <?php echo $category_filter === 'general' ? 'selected' : ''; ?>>عام</option>
                    </select>
                </div>
                
                <div>
                    <label class="form-label">الصعوبة</label>
                    <select name="difficulty" class="form-select">
                        <option value="">جميع المستويات</option>
                        <option value="easy" <?php echo $difficulty_filter === 'easy' ? 'selected' : ''; ?>>سهل</option>
                        <option value="medium" <?php echo $difficulty_filter === 'medium' ? 'selected' : ''; ?>>متوسط</option>
                        <option value="hard" <?php echo $difficulty_filter === 'hard' ? 'selected' : ''; ?>>صعب</option>
                    </select>
                </div>
                
                <div>
                    <button type="submit" class="nibrass-btn nibrass-btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>

        <!-- شبكة الأنشطة -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3><i class="fas fa-list"></i> قائمة الأنشطة</h3>
            <div class="text-muted">
                إجمالي النتائج: <?php echo number_format($total_activities); ?>
            </div>
        </div>

        <?php if (empty($activities)): ?>
            <div class="text-center py-5">
                <i class="fas fa-gamepad fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد أنشطة</h4>
                <p class="text-muted">لم يتم العثور على أنشطة مطابقة لمعايير البحث</p>
            </div>
        <?php else: ?>
            <div class="activities-grid">
                <?php foreach ($activities as $activity): ?>
                    <div class="activity-card <?php echo $activity['category']; ?>" style="position: relative;">
                        <div class="status-indicator <?php echo $activity['is_active'] ? 'active' : 'inactive'; ?>"></div>
                        
                        <div class="activity-header">
                            <div class="flex-grow-1">
                                <div class="activity-title"><?php echo htmlspecialchars($activity['title']); ?></div>
                                <span class="category-badge <?php echo $activity['category']; ?>">
                                    <?php 
                                    $categories = [
                                        'math' => 'الرياضيات',
                                        'language' => 'اللغة العربية',
                                        'science' => 'العلوم',
                                        'art' => 'الفنون',
                                        'general' => 'عام'
                                    ];
                                    echo $categories[$activity['category']] ?? $activity['category'];
                                    ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="activity-description">
                            <?php echo htmlspecialchars($activity['description']); ?>
                        </div>
                        
                        <div class="activity-meta">
                            <div class="meta-item">
                                <i class="fas fa-signal"></i>
                                <span class="difficulty-badge <?php echo $activity['difficulty_level']; ?>">
                                    <?php 
                                    $difficulties = ['easy' => 'سهل', 'medium' => 'متوسط', 'hard' => 'صعب'];
                                    echo $difficulties[$activity['difficulty_level']] ?? $activity['difficulty_level'];
                                    ?>
                                </span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-child"></i>
                                <span>عمر <?php echo $activity['target_age']; ?> سنة</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-clock"></i>
                                <span><?php echo $activity['duration_minutes']; ?> دقيقة</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-star"></i>
                                <span><?php echo $activity['points_reward']; ?> نقطة</span>
                            </div>
                        </div>
                        
                        <div class="activity-actions">
                            <!-- تفعيل/إلغاء تفعيل -->
                            <form method="POST" style="display: inline;">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="activity_id" value="<?php echo $activity['id']; ?>">
                                <input type="hidden" name="current_status" value="<?php echo $activity['is_active']; ?>">
                                <button type="submit" name="toggle_activity" 
                                        class="nibrass-btn <?php echo $activity['is_active'] ? 'nibrass-btn-warning' : 'nibrass-btn-success'; ?> btn-sm"
                                        onclick="return confirm('هل أنت متأكد من تغيير حالة هذا النشاط؟')">
                                    <i class="fas <?php echo $activity['is_active'] ? 'fa-pause' : 'fa-play'; ?>"></i>
                                </button>
                            </form>

                            <!-- حذف -->
                            <form method="POST" style="display: inline;">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="activity_id" value="<?php echo $activity['id']; ?>">
                                <button type="submit" name="delete_activity" 
                                        class="nibrass-btn nibrass-btn-danger btn-sm"
                                        onclick="return confirm('هل أنت متأكد من حذف هذا النشاط؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// تضمين الفوتر
include '../includes/footer.php';
?>
