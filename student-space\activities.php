<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كطالب
require_student();

$user = current_user();
$activity_type = isset($_GET['type']) ? clean_input($_GET['type']) : 'all';

// جلب معلومات الطالب
try {
    $student_info_query = "SELECT * FROM students WHERE user_id = :user_id";
    $student_info_stmt = $db->prepare($student_info_query);
    $student_info_stmt->bindParam(':user_id', $user['id']);
    $student_info_stmt->execute();
    $student_info = $student_info_stmt->fetch();
} catch (PDOException $e) {
    $student_info = null;
}

// جلب الأنشطة حسب النوع
try {
    if ($activity_type == 'all') {
        $activities_query = "SELECT * FROM activities WHERE is_active = 1 ORDER BY difficulty_level ASC, created_at DESC";
        $activities_stmt = $db->prepare($activities_query);
    } else {
        $type_param = $activity_type;
        $activities_query = "SELECT * FROM activities WHERE is_active = 1 AND category = :type ORDER BY difficulty_level ASC, created_at DESC";
        $activities_stmt = $db->prepare($activities_query);
        $activities_stmt->bindParam(':type', $type_param);
    }
    $activities_stmt->execute();
    $activities = $activities_stmt->fetchAll();
} catch (PDOException $e) {
    $activities = [];
}

// إضافة أنشطة افتراضية إذا لم توجد في قاعدة البيانات
if (empty($activities)) {
    $activities = [
        [
            'id' => 1,
            'title' => 'لعبة الرياضيات المرحة',
            'description' => 'تعلم الجمع والطرح بطريقة ممتعة وتفاعلية',
            'category' => 'math',
            'difficulty_level' => 'easy',
            'duration_minutes' => 15,
            'points_reward' => 50,
            'rating' => 5
        ],
        [
            'id' => 2,
            'title' => 'عالم الحروف والكلمات',
            'description' => 'تعلم الحروف والكلمات بطريقة شيقة',
            'category' => 'language',
            'difficulty_level' => 'easy',
            'duration_minutes' => 20,
            'points_reward' => 40,
            'rating' => 4
        ],
        [
            'id' => 3,
            'title' => 'الفيديوهات التعليمية',
            'description' => 'تعلم العلوم من خلال الفيديوهات الممتعة',
            'category' => 'science',
            'difficulty_level' => 'medium',
            'duration_minutes' => 25,
            'points_reward' => 30,
            'rating' => 5
        ],
        [
            'id' => 4,
            'title' => 'الرسم والتلوين',
            'description' => 'أطلق إبداعك في الرسم والتلوين',
            'category' => 'art',
            'difficulty_level' => 'easy',
            'duration_minutes' => 30,
            'points_reward' => 35,
            'rating' => 4
        ]
    ];
}

// إعداد متغيرات الصفحة
$page_title = 'الأنشطة التعليمية';
$page_description = 'أنشطة تعليمية تفاعلية ممتعة';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
/* Floating shapes animation for header */
.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.shape {
    position: absolute;
    color: rgba(255,255,255,0.1);
    animation: float 6s ease-in-out infinite;
}

.shape:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.shape:nth-child(4) {
    top: 40%;
    right: 30%;
    animation-delay: 6s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.activity-filter {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    background: transparent;
    color: #666;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
    margin: 0.25rem;
    display: inline-block;
}

.filter-btn.active,
.filter-btn:hover {
    background: #4a90e2;
    color: white;
    border-color: #4a90e2;
    transform: translateY(-2px);
}

.activity-item {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
    border: 2px solid transparent;
}

.activity-item:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: rgba(74, 144, 226, 0.3);
}

.activity-header {
    padding: 2rem;
    text-align: center;
}

.activity-icon-large {
    width: 100px;
    height: 100px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    margin: 0 auto 1.5rem auto;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.activity-icon-large:hover {
    transform: scale(1.1) rotate(5deg);
}

/* ألوان مختلفة لكل نوع نشاط */
.activity-icon-large.math { background: linear-gradient(135deg, #ff6b6b, #ee5a24); }
.activity-icon-large.language { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
.activity-icon-large.science { background: linear-gradient(135deg, #a8edea, #fed6e3); color: #333; }
.activity-icon-large.art { background: linear-gradient(135deg, #ffecd2, #fcb69f); color: #333; }
.activity-icon-large { background: linear-gradient(135deg, #667eea, #764ba2); } /* افتراضي */

.activity-icon-large.math { background: linear-gradient(135deg, #ff6b6b, #ee5a24); }
.activity-icon-large.language { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
.activity-icon-large.science { background: linear-gradient(135deg, #45b7d1, #2980b9); }
.activity-icon-large.art { background: linear-gradient(135deg, #96ceb4, #27ae60); }
.activity-icon-large.game { background: linear-gradient(135deg, #feca57, #ff9ff3); }

.difficulty-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.difficulty-easy {
    background: linear-gradient(135deg, #a8e6cf, #88d8a3);
    color: #2d5016;
    border-color: #88d8a3;
}
.difficulty-medium {
    background: linear-gradient(135deg, #ffd93d, #ffcd3c);
    color: #8b6914;
    border-color: #ffcd3c;
}
.difficulty-hard {
    background: linear-gradient(135deg, #ff8a80, #ff5722);
    color: white;
    border-color: #ff5722;
}

/* تأكيد ظهور الأيقونات */
.activities-header i,
.activity-item i,
.filter-btn i,
.btn i {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}
</style>

<!-- قسم العنوان -->
<section class="nibrass-header position-relative">
    <div class="floating-shapes">
        <div class="shape"><i class="fas fa-gamepad fa-3x"></i></div>
        <div class="shape"><i class="fas fa-puzzle-piece fa-3x"></i></div>
        <div class="shape"><i class="fas fa-star fa-3x"></i></div>
        <div class="shape"><i class="fas fa-trophy fa-3x"></i></div>
    </div>
    <div class="container">
        <div class="hero-content text-center">
            <h1><i class="fas fa-gamepad"></i> الأنشطة التعليمية</h1>
            <p class="lead">اختر النشاط المناسب لك وابدأ رحلة التعلم الممتعة</p>
            <div class="mt-4">
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-brain"></i> تطوير المهارات
                </span>
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-star"></i> اكسب النقاط
                </span>
                <span class="badge bg-light text-dark fs-6">
                    <i class="fas fa-trophy"></i> حقق الإنجازات
                </span>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <!-- فلترة الأنشطة -->
        <div class="activity-filter">
            <div class="text-center">
                <h4 class="mb-3">اختر نوع النشاط</h4>
                <div class="filter-buttons">
                    <a href="activities.php?type=all" class="nibrass-btn <?php echo $activity_type == 'all' ? 'nibrass-btn-primary' : 'nibrass-btn-outline-primary'; ?>">
                        <i class="fas fa-th"></i> جميع الأنشطة
                    </a>
                    <a href="activities.php?type=math" class="nibrass-btn <?php echo $activity_type == 'math' ? 'nibrass-btn-primary' : 'nibrass-btn-outline-primary'; ?>">
                        <i class="fas fa-calculator"></i> الرياضيات
                    </a>
                    <a href="activities.php?type=language" class="nibrass-btn <?php echo $activity_type == 'language' ? 'nibrass-btn-primary' : 'nibrass-btn-outline-primary'; ?>">
                        <i class="fas fa-book-open"></i> اللغة العربية
                    </a>
                    <a href="activities.php?type=science" class="nibrass-btn <?php echo $activity_type == 'science' ? 'nibrass-btn-primary' : 'nibrass-btn-outline-primary'; ?>">
                        <i class="fas fa-flask"></i> العلوم
                    </a>
                    <a href="activities.php?type=art" class="nibrass-btn <?php echo $activity_type == 'art' ? 'nibrass-btn-primary' : 'nibrass-btn-outline-primary'; ?>">
                        <i class="fas fa-palette"></i> الفنون
                    </a>
                </div>
            </div>
        </div>

        <!-- قائمة الأنشطة -->
        <?php if (!empty($activities)): ?>
            <div class="row">
                <?php foreach ($activities as $activity): ?>
                    <div class="col-lg-6 col-xl-4">
                        <div class="activity-item">
                            <div class="activity-header">
                                <div class="activity-icon-large <?php echo $activity['category']; ?>">
                                    <i class="fas fa-<?php 
                                        echo $activity['category'] == 'math' ? 'calculator' : 
                                            ($activity['category'] == 'language' ? 'book-open' : 
                                            ($activity['category'] == 'science' ? 'flask' : 
                                            ($activity['category'] == 'art' ? 'palette' : 'gamepad')));
                                    ?>"></i>
                                </div>
                                <h4><?php echo htmlspecialchars($activity['title']); ?></h4>
                                <p class="text-muted"><?php echo htmlspecialchars($activity['description']); ?></p>
                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="difficulty-badge difficulty-<?php echo $activity['difficulty_level']; ?>">
                                        <?php 
                                        $difficulty_labels = [
                                            'easy' => 'سهل',
                                            'medium' => 'متوسط',
                                            'hard' => 'صعب'
                                        ];
                                        echo $difficulty_labels[$activity['difficulty_level']] ?? 'سهل';
                                        ?>
                                    </span>
                                    <div class="text-warning">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star<?php echo $i <= $activity['rating'] ? '' : ' text-muted'; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i> <?php echo $activity['duration_minutes']; ?> دقيقة
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-star"></i> +<?php echo $activity['points_reward']; ?> نقطة
                                    </small>
                                </div>
                            </div>
                            
                            <div class="p-3 bg-light">
                                <a href="play-activity.php?id=<?php echo $activity['id']; ?>" class="nibrass-btn nibrass-btn-primary w-100">
                                    <i class="fas fa-play"></i> ابدأ النشاط
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-gamepad fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد أنشطة متاحة</h4>
                <p class="text-muted">ستتوفر أنشطة جديدة قريباً</p>
                <a href="activities.php?type=all" class="nibrass-btn nibrass-btn-primary">
                    <i class="fas fa-refresh"></i> عرض جميع الأنشطة
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    // تأثيرات التمرير للكروت
    document.addEventListener('DOMContentLoaded', function() {
        const activityCards = document.querySelectorAll('.activity-item');
        activityCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
