<?php
/**
 * Header مشترك لجميع صفحات المنصة
 * Shared Header for all platform pages
 */

// تحديد الصفحة الحالية لتفعيل الرابط المناسب في القائمة
$current_page = basename($_SERVER['PHP_SELF'], '.php');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : 'منصة نبراس - منصة تعليمية متخصصة في دعم الأطفال ذوي اضطراب طيف التوحد وأسرهم'; ?>">
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo get_asset_url('css/style.css'); ?>" rel="stylesheet">
    <link href="<?php echo get_asset_url('css/rtl-enhancements.css'); ?>" rel="stylesheet">
    
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css_file): ?>
            <link href="<?php echo $css_file; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <!-- الشريط العلوي المحسن -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top enhanced-navbar">
        <div class="container">
            <a class="navbar-brand logo-enhanced" href="<?php echo get_url('index.php'); ?>">
                <i class="fas fa-star logo-icon"></i>
                <span class="logo-text">نبراس</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'index') ? 'active' : ''; ?>" href="<?php echo get_url('index.php'); ?>">
                            <i class="fas fa-home nav-icon"></i>
                            <span>الرئيسية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'about') ? 'active' : ''; ?>" href="<?php echo get_url('about.php'); ?>">
                            <i class="fas fa-info-circle nav-icon"></i>
                            <span>من نحن</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'library') ? 'active' : ''; ?>" href="<?php echo get_url('library.php'); ?>">
                            <i class="fas fa-book nav-icon"></i>
                            <span>المكتبة</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'workshops') ? 'active' : ''; ?>" href="<?php echo get_url('workshops.php'); ?>">
                            <i class="fas fa-chalkboard-teacher nav-icon"></i>
                            <span>الورشات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'creativity') ? 'active' : ''; ?>" href="<?php echo get_url('creativity.php'); ?>">
                            <i class="fas fa-palette nav-icon"></i>
                            <span>الإبداع</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'contact') ? 'active' : ''; ?>" href="<?php echo get_url('contact.php'); ?>">
                            <i class="fas fa-envelope nav-icon"></i>
                            <span>اتصل بنا</span>
                        </a>
                    </li>

                    <?php if (is_logged_in()): ?>
                        <?php
                        $user = current_user();

                        // جلب الإشعارات غير المقروءة
                        $unread_notifications = [];
                        $unread_count = 0;
                        try {
                            require_once dirname(__FILE__) . '/../db.php';

                            // جلب الإشعارات النشطة للمستخدم
                            $user_role = $user['role'] == 'parent' ? 'parents' :
                                        ($user['role'] == 'student' ? 'students' :
                                        ($user['role'] == 'specialist' ? 'specialists' :
                                        ($user['role'] == 'admin' ? 'admins' : 'all')));

                            $notifications_query = "SELECT n.*,
                                                           CASE WHEN un.is_read IS NULL THEN 0 ELSE un.is_read END as is_read
                                                    FROM notifications n
                                                    LEFT JOIN user_notifications un ON n.id = un.notification_id AND un.user_id = ?
                                                    WHERE n.is_active = 1
                                                    AND (n.target_audience = 'all' OR n.target_audience = ?)
                                                    AND (n.start_date <= NOW())
                                                    AND (n.end_date IS NULL OR n.end_date >= NOW())
                                                    ORDER BY n.priority DESC, n.created_at DESC
                                                    LIMIT 10";

                            $notifications_stmt = $db->prepare($notifications_query);
                            $notifications_stmt->execute([$user['id'], $user_role]);
                            $unread_notifications = $notifications_stmt->fetchAll();

                            // عد الإشعارات غير المقروءة
                            $unread_count = 0;
                            foreach ($unread_notifications as $notification) {
                                if (!$notification['is_read']) {
                                    $unread_count++;
                                }
                            }
                        } catch (Exception $e) {
                            // في حالة الخطأ، تجاهل الإشعارات
                        }
                        ?>

                        <!-- أيقونة الإشعارات -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle position-relative" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-bell nav-icon"></i>
                                <?php if ($unread_count > 0): ?>
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                        <?php echo $unread_count > 9 ? '9+' : $unread_count; ?>
                                    </span>
                                <?php endif; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end notifications-dropdown" style="width: 350px; max-height: 400px; overflow-y: auto;">
                                <li class="dropdown-header d-flex justify-content-between align-items-center">
                                    <span>الإشعارات</span>
                                    <?php if ($unread_count > 0): ?>
                                        <small class="text-primary"><?php echo $unread_count; ?> جديد</small>
                                    <?php endif; ?>
                                </li>
                                <li><hr class="dropdown-divider"></li>

                                <?php if (!empty($unread_notifications)): ?>
                                    <?php foreach ($unread_notifications as $notification): ?>
                                        <li>
                                            <a class="dropdown-item notification-item <?php echo !$notification['is_read'] ? 'unread' : ''; ?>"
                                               href="#" onclick="markAsRead(<?php echo $notification['id']; ?>)">
                                                <div class="d-flex">
                                                    <div class="notification-icon me-2">
                                                        <i class="fas fa-<?php
                                                            echo $notification['type'] === 'info' ? 'info-circle text-info' :
                                                                ($notification['type'] === 'success' ? 'check-circle text-success' :
                                                                ($notification['type'] === 'warning' ? 'exclamation-triangle text-warning' :
                                                                ($notification['type'] === 'error' ? 'times-circle text-danger' : 'bullhorn text-primary')));
                                                        ?>"></i>
                                                    </div>
                                                    <div class="notification-content flex-grow-1">
                                                        <div class="notification-title fw-bold"><?php echo htmlspecialchars($notification['title']); ?></div>
                                                        <div class="notification-message text-muted small">
                                                            <?php echo mb_substr(htmlspecialchars($notification['message']), 0, 80) . '...'; ?>
                                                        </div>
                                                        <div class="notification-time text-muted small">
                                                            <?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                    <?php endforeach; ?>
                                    <li class="text-center">
                                        <a class="dropdown-item text-center text-primary" href="<?php echo get_url('notifications.php'); ?>">
                                            <small>عرض جميع الإشعارات</small>
                                        </a>
                                    </li>
                                <?php else: ?>
                                    <li class="text-center py-3">
                                        <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                                        <div class="text-muted">لا توجد إشعارات جديدة</div>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>

                        <!-- قائمة المستخدم -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle nav-icon"></i>
                                <span><?php echo htmlspecialchars($user['name']); ?></span>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if ($user['role'] == 'student'): ?>
                                    <li><a class="dropdown-item" href="<?php echo get_url('student-space/dashboard.php'); ?>">
                                        <i class="fas fa-child"></i> فضاء التلاميذ
                                    </a></li>
                                <?php elseif ($user['role'] == 'parent'): ?>
                                    <li><a class="dropdown-item" href="<?php echo get_url('parent-space/dashboard.php'); ?>">
                                        <i class="fas fa-user-friends"></i> فضاء الأولياء
                                    </a></li>
                                <?php elseif ($user['role'] == 'specialist'): ?>
                                    <li><a class="dropdown-item" href="<?php echo get_url('specialist-space/dashboard.php'); ?>">
                                        <i class="fas fa-user-md"></i> فضاء الأخصائيين
                                    </a></li>
                                <?php elseif ($user['role'] == 'admin'): ?>
                                    <li><a class="dropdown-item" href="<?php echo get_url('admin/dashboard.php'); ?>">
                                        <i class="fas fa-cogs"></i> لوحة الإدارة
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo get_url('parent-space/ai-chat.php'); ?>">
                                    <i class="fas fa-robot"></i> المساعد الذكي
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo get_url('logout.php'); ?>">
                                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link btn-login <?php echo ($current_page == 'login') ? 'active' : ''; ?>" href="<?php echo get_url('login.php'); ?>">
                                <i class="fas fa-sign-in-alt nav-icon"></i>
                                <span>تسجيل الدخول</span>
                            </a>
                        </li>
                        <!-- <li class="nav-item">
                            <a class="nav-link btn-register <?php echo ($current_page == 'register') ? 'active' : ''; ?>" href="<?php echo get_url('register.php'); ?>">
                                <i class="fas fa-user-plus nav-icon"></i>
                                <span>إنشاء حساب</span>
                            </a>
                        </li> -->
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- رسائل النظام -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="container mt-3">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="container mt-3">
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    <?php endif; ?>

    <!-- رسالة تسجيل الخروج -->
    <?php if (isset($_GET['logged_out']) && $_GET['logged_out'] == '1'): ?>
        <div class="container mt-3">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i>
                تم تسجيل الخروج بنجاح. نراك قريباً!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    <?php endif; ?>

    <!-- CSS للإشعارات -->
    <style>
    .notifications-dropdown {
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        border-radius: 15px;
    }

    .notification-item {
        padding: 0.75rem 1rem;
        border: none;
        transition: all 0.3s ease;
    }

    .notification-item:hover {
        background-color: #f8f9fa;
    }

    .notification-item.unread {
        background-color: #e3f2fd;
        border-right: 3px solid #2196f3;
    }

    .notification-icon {
        width: 30px;
        text-align: center;
    }

    .notification-content {
        line-height: 1.4;
    }

    .notification-title {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .notification-message {
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
    }

    .notification-time {
        font-size: 0.75rem;
    }

    .nav-link .badge {
        font-size: 0.6rem;
        padding: 0.2rem 0.4rem;
    }
    </style>

    <!-- JavaScript للإشعارات -->
    <script>
    function markAsRead(notificationId) {
        // إرسال طلب AJAX لتحديد الإشعار كمقروء
        fetch('<?php echo get_url("mark_notification_read.php"); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                notification_id: notificationId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إزالة علامة "غير مقروء" من الإشعار
                const notificationElement = event.target.closest('.notification-item');
                if (notificationElement) {
                    notificationElement.classList.remove('unread');
                }

                // تحديث عداد الإشعارات
                updateNotificationCount();
            }
        })
        .catch(error => {
            console.error('خطأ في تحديد الإشعار كمقروء:', error);
        });
    }

    function updateNotificationCount() {
        // تحديث عداد الإشعارات غير المقروءة
        const unreadItems = document.querySelectorAll('.notification-item.unread');
        const badge = document.querySelector('.nav-link .badge');
        const headerCount = document.querySelector('.dropdown-header small');

        const count = unreadItems.length;

        if (count > 0) {
            if (badge) {
                badge.textContent = count > 9 ? '9+' : count;
                badge.style.display = 'inline';
            }
            if (headerCount) {
                headerCount.textContent = count + ' جديد';
            }
        } else {
            if (badge) {
                badge.style.display = 'none';
            }
            if (headerCount) {
                headerCount.textContent = '';
            }
        }
    }
    </script>
