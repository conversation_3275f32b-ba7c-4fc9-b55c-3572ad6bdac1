<?php
// التحقق من تسجيل الدخول كمدير
if (!is_logged_in() || $_SESSION['user_role'] !== 'admin') {
    redirect('login.php');
}

$current_page = basename($_SERVER['PHP_SELF']);
?>

<style>
.admin-sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
    box-shadow: -5px 0 15px rgba(0,0,0,0.1);
}

.admin-sidebar.active {
    transform: translateX(0);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
}

.sidebar-header h4 {
    margin: 0;
    font-weight: 600;
}

.sidebar-header p {
    margin: 0.5rem 0 0 0;
    opacity: 0.8;
    font-size: 0.9rem;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    border-right-color: #4a90e2;
}

.nav-link.active {
    background: rgba(74, 144, 226, 0.2);
    color: white;
    border-right-color: #4a90e2;
}

.nav-link i {
    width: 20px;
    margin-left: 1rem;
    text-align: center;
}

.nav-section {
    padding: 1rem 1.5rem 0.5rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    opacity: 0.6;
    letter-spacing: 1px;
}

.sidebar-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    background: #4a90e2;
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: #2c5aa0;
    transform: scale(1.1);
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

.admin-content {
    margin-right: 0;
    transition: margin-right 0.3s ease;
}

.admin-content.sidebar-open {
    margin-right: 280px;
}

.logout-section {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #4a90e2;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-left: 1rem;
}

.user-details h6 {
    margin: 0;
    font-size: 0.9rem;
}

.user-details small {
    opacity: 0.7;
}

@media (max-width: 768px) {
    .admin-sidebar {
        width: 100%;
    }
    
    .admin-content.sidebar-open {
        margin-right: 0;
    }
}
</style>

<!-- زر تبديل الشريط الجانبي -->
<button class="sidebar-toggle" id="sidebarToggle">
    <i class="fas fa-bars"></i>
</button>

<!-- طبقة التراكب -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<!-- الشريط الجانبي للإدارة -->
<div class="admin-sidebar" id="adminSidebar">
    <!-- رأس الشريط الجانبي -->
    <div class="sidebar-header">
        <h4><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h4>
        <p>إدارة منصة نبراس</p>
    </div>
    
    <!-- قائمة التنقل -->
    <nav class="sidebar-nav">
        <!-- القسم الرئيسي -->
        <div class="nav-section">الرئيسية</div>
        
        <div class="nav-item">
            <a href="dashboard.php" class="nav-link <?php echo $current_page === 'dashboard.php' ? 'active' : ''; ?>">
                <i class="fas fa-home"></i>
                <span>لوحة التحكم الرئيسية</span>
            </a>
        </div>
        
        <!-- إدارة المستخدمين -->
        <div class="nav-section">إدارة المستخدمين</div>
        
        <div class="nav-item">
            <a href="users.php" class="nav-link <?php echo $current_page === 'users.php' ? 'active' : ''; ?>">
                <i class="fas fa-users"></i>
                <span>إدارة المستخدمين</span>
            </a>
        </div>
        
        <div class="nav-item">
            <a href="user-roles.php" class="nav-link <?php echo $current_page === 'user-roles.php' ? 'active' : ''; ?>">
                <i class="fas fa-user-shield"></i>
                <span>أدوار المستخدمين</span>
            </a>
        </div>
        
        <!-- إدارة المحتوى -->
        <div class="nav-section">إدارة المحتوى</div>
        
        <div class="nav-item">
            <a href="activities.php" class="nav-link <?php echo $current_page === 'activities.php' ? 'active' : ''; ?>">
                <i class="fas fa-gamepad"></i>
                <span>الأنشطة التعليمية</span>
            </a>
        </div>
        
        <div class="nav-item">
            <a href="videos.php" class="nav-link <?php echo $current_page === 'videos.php' ? 'active' : ''; ?>">
                <i class="fas fa-video"></i>
                <span>الفيديوهات التعليمية</span>
            </a>
        </div>
        
        <div class="nav-item">
            <a href="stories.php" class="nav-link <?php echo $current_page === 'stories.php' ? 'active' : ''; ?>">
                <i class="fas fa-book-open"></i>
                <span>القصص التفاعلية</span>
            </a>
        </div>
        
        <div class="nav-item">
            <a href="resources.php" class="nav-link <?php echo $current_page === 'resources.php' ? 'active' : ''; ?>">
                <i class="fas fa-folder"></i>
                <span>الموارد التعليمية</span>
            </a>
        </div>
        
        <!-- إدارة النظام -->
        <div class="nav-section">إدارة النظام</div>
        
        <div class="nav-item">
            <a href="achievements.php" class="nav-link <?php echo $current_page === 'achievements.php' ? 'active' : ''; ?>">
                <i class="fas fa-trophy"></i>
                <span>الإنجازات والجوائز</span>
            </a>
        </div>
        
        <div class="nav-item">
            <a href="notifications.php" class="nav-link <?php echo $current_page === 'notifications.php' ? 'active' : ''; ?>">
                <i class="fas fa-bell"></i>
                <span>إدارة الإشعارات</span>
            </a>
        </div>
        
        <div class="nav-item">
            <a href="reports.php" class="nav-link <?php echo $current_page === 'reports.php' ? 'active' : ''; ?>">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير والإحصائيات</span>
            </a>
        </div>
        
        <div class="nav-item">
            <a href="settings.php" class="nav-link <?php echo $current_page === 'settings.php' ? 'active' : ''; ?>">
                <i class="fas fa-cog"></i>
                <span>إعدادات النظام</span>
            </a>
        </div>
        
        <!-- أدوات المطور -->
        <div class="nav-section">أدوات المطور</div>
        
        <div class="nav-item">
            <a href="database.php" class="nav-link <?php echo $current_page === 'database.php' ? 'active' : ''; ?>">
                <i class="fas fa-database"></i>
                <span>إدارة قاعدة البيانات</span>
            </a>
        </div>
        
        <div class="nav-item">
            <a href="logs.php" class="nav-link <?php echo $current_page === 'logs.php' ? 'active' : ''; ?>">
                <i class="fas fa-file-alt"></i>
                <span>سجلات النظام</span>
            </a>
        </div>
        
        <div class="nav-item">
            <a href="backup.php" class="nav-link <?php echo $current_page === 'backup.php' ? 'active' : ''; ?>">
                <i class="fas fa-download"></i>
                <span>النسخ الاحتياطي</span>
            </a>
        </div>
    </nav>
    
    <!-- قسم تسجيل الخروج -->
    <div class="logout-section">
        <div class="user-info">
            <div class="user-avatar">
                <?php echo strtoupper(substr(current_user()['name'], 0, 1)); ?>
            </div>
            <div class="user-details">
                <h6><?php echo htmlspecialchars(current_user()['name']); ?></h6>
                <small>مدير النظام</small>
            </div>
        </div>
        
        <a href="../logout.php" class="nav-link" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
            <i class="fas fa-sign-out-alt"></i>
            <span>تسجيل الخروج</span>
        </a>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const adminSidebar = document.getElementById('adminSidebar');
    const sidebarOverlay = document.getElementById('sidebarOverlay');
    const adminContent = document.querySelector('.admin-content');
    
    // تبديل الشريط الجانبي
    function toggleSidebar() {
        adminSidebar.classList.toggle('active');
        sidebarOverlay.classList.toggle('active');
        
        if (adminContent) {
            adminContent.classList.toggle('sidebar-open');
        }
        
        // تغيير أيقونة الزر
        const icon = sidebarToggle.querySelector('i');
        if (adminSidebar.classList.contains('active')) {
            icon.className = 'fas fa-times';
        } else {
            icon.className = 'fas fa-bars';
        }
    }
    
    // أحداث النقر
    sidebarToggle.addEventListener('click', toggleSidebar);
    sidebarOverlay.addEventListener('click', toggleSidebar);
    
    // إغلاق الشريط الجانبي عند النقر على رابط (للهواتف المحمولة)
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                toggleSidebar();
            }
        });
    });
    
    // إغلاق الشريط الجانبي عند تغيير حجم الشاشة
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            adminSidebar.classList.remove('active');
            sidebarOverlay.classList.remove('active');
            if (adminContent) {
                adminContent.classList.remove('sidebar-open');
            }
            sidebarToggle.querySelector('i').className = 'fas fa-bars';
        }
    });
    
    // إضافة تأثير التمرير السلس للروابط
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(-5px)';
        });
        
        link.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
});
</script>
