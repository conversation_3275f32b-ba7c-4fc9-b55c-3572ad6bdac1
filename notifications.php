<?php
require_once 'includes/auth.php';
require_once 'db.php';

// التحقق من تسجيل الدخول
require_login();

$user = current_user();

// تحديد دور المستخدم للإشعارات
$user_role = $user['role'] == 'parent' ? 'parents' : 
            ($user['role'] == 'student' ? 'students' : 
            ($user['role'] == 'specialist' ? 'specialists' : 
            ($user['role'] == 'admin' ? 'admins' : 'all')));

// معالجة تحديد جميع الإشعارات كمقروءة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['mark_all_read'])) {
    try {
        // جلب جميع الإشعارات النشطة للمستخدم
        $notifications_query = "SELECT n.id
                               FROM notifications n
                               WHERE n.is_active = 1 
                               AND (n.target_audience = 'all' OR n.target_audience = ?)
                               AND (n.start_date <= NOW())
                               AND (n.end_date IS NULL OR n.end_date >= NOW())";
        
        $notifications_stmt = $db->prepare($notifications_query);
        $notifications_stmt->execute([$user_role]);
        $notification_ids = $notifications_stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // تحديد جميع الإشعارات كمقروءة
        foreach ($notification_ids as $notification_id) {
            $query = "INSERT INTO user_notifications (user_id, notification_id, is_read, read_at) 
                     VALUES (?, ?, 1, NOW()) 
                     ON DUPLICATE KEY UPDATE is_read = 1, read_at = NOW()";
            $stmt = $db->prepare($query);
            $stmt->execute([$user['id'], $notification_id]);
        }
        
        $_SESSION['success_message'] = 'تم تحديد جميع الإشعارات كمقروءة';
        header('Location: notifications.php');
        exit;
        
    } catch (PDOException $e) {
        $_SESSION['error_message'] = 'خطأ في تحديث الإشعارات: ' . $e->getMessage();
    }
}

// جلب الإشعارات مع حالة القراءة
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = 10;
$offset = ($page - 1) * $per_page;

try {
    // عدد الإشعارات الإجمالي
    $count_query = "SELECT COUNT(*)
                   FROM notifications n
                   WHERE n.is_active = 1 
                   AND (n.target_audience = 'all' OR n.target_audience = ?)
                   AND (n.start_date <= NOW())
                   AND (n.end_date IS NULL OR n.end_date >= NOW())";
    
    $count_stmt = $db->prepare($count_query);
    $count_stmt->execute([$user_role]);
    $total_notifications = $count_stmt->fetchColumn();
    
    // جلب الإشعارات مع حالة القراءة
    $notifications_query = "SELECT n.*, 
                                   CASE WHEN un.is_read IS NULL THEN 0 ELSE un.is_read END as is_read,
                                   un.read_at
                           FROM notifications n
                           LEFT JOIN user_notifications un ON n.id = un.notification_id AND un.user_id = ?
                           WHERE n.is_active = 1 
                           AND (n.target_audience = 'all' OR n.target_audience = ?)
                           AND (n.start_date <= NOW())
                           AND (n.end_date IS NULL OR n.end_date >= NOW())
                           ORDER BY n.priority DESC, n.created_at DESC
                           LIMIT $per_page OFFSET $offset";
    
    $notifications_stmt = $db->prepare($notifications_query);
    $notifications_stmt->execute([$user['id'], $user_role]);
    $notifications = $notifications_stmt->fetchAll();
    
    $total_pages = ceil($total_notifications / $per_page);
    
    // عد الإشعارات غير المقروءة
    $unread_count = 0;
    foreach ($notifications as $notification) {
        if (!$notification['is_read']) {
            $unread_count++;
        }
    }
    
} catch (PDOException $e) {
    $_SESSION['error_message'] = 'خطأ في جلب الإشعارات: ' . $e->getMessage();
    $notifications = [];
    $total_notifications = 0;
    $total_pages = 0;
    $unread_count = 0;
}

// إعداد متغيرات الصفحة
$page_title = 'الإشعارات';
$page_description = 'جميع إشعاراتك في مكان واحد';

// تضمين الهيدر
include 'includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="css/nibrass-unified.css" rel="stylesheet">

<style>
/* صفحة الإشعارات */
.notifications-page {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 2rem 0;
}

.notifications-header {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 5rem;
}

.notifications-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.notification-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.notification-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.notification-card.unread {
    border-right: 5px solid #2196f3;
    background: linear-gradient(to right, #e3f2fd, white);
}

.notification-card.unread::before {
    content: '';
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 8px;
    height: 8px;
    background: #2196f3;
    border-radius: 50%;
}

.notification-type-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-left: 1rem;
}

.type-info { background: #e3f2fd; color: #1976d2; }
.type-success { background: #e8f5e8; color: #388e3c; }
.type-warning { background: #fff3e0; color: #f57c00; }
.type-error { background: #ffebee; color: #d32f2f; }
.type-announcement { background: #f3e5f5; color: #7b1fa2; }

.notification-content {
    flex: 1;
}

.notification-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.notification-message {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.notification-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #888;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.priority-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.priority-low { background: #e8f5e8; color: #388e3c; }
.priority-medium { background: #fff3e0; color: #f57c00; }
.priority-high { background: #ffebee; color: #d32f2f; }
.priority-urgent { background: #f3e5f5; color: #7b1fa2; }

.btn-mark-read {
    background: linear-gradient(135deg, #2196f3, #1976d2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-mark-read:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .notifications-page {
        padding: 1rem 0;
    }
    
    .notifications-header,
    .notifications-container {
        padding: 1rem;
        margin-top: 3rem;
    }
    
    .notification-card {
        padding: 1rem;
    }
    
    .notification-type-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
}
</style>

<div class="notifications-page">
    <div class="container">
        <!-- رأس الصفحة -->
        <div class="notifications-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-bell"></i> الإشعارات</h1>
                    <p class="mb-0">جميع إشعاراتك في مكان واحد</p>
                </div>
                <div>
                    <?php if ($unread_count > 0): ?>
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="mark_all_read" class="btn btn-mark-read">
                                <i class="fas fa-check-double"></i> تحديد الكل كمقروء
                            </button>
                        </form>
                    <?php endif; ?>
                    <a href="<?php echo get_dashboard_url(); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                    </a>
                </div>
            </div>
            
            <?php if ($total_notifications > 0): ?>
                <div class="mt-3">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="stat-item">
                                <h3 class="text-primary"><?php echo number_format($total_notifications); ?></h3>
                                <p class="text-muted mb-0">إجمالي الإشعارات</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-item">
                                <h3 class="text-warning"><?php echo number_format($unread_count); ?></h3>
                                <p class="text-muted mb-0">غير مقروءة</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-item">
                                <h3 class="text-success"><?php echo number_format($total_notifications - $unread_count); ?></h3>
                                <p class="text-muted mb-0">مقروءة</p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- قائمة الإشعارات -->
        <div class="notifications-container">
            <?php if (!empty($notifications)): ?>
                <?php foreach ($notifications as $notification): ?>
                    <div class="notification-card <?php echo !$notification['is_read'] ? 'unread' : ''; ?>">
                        <div class="d-flex">
                            <div class="notification-type-icon type-<?php echo $notification['type']; ?>">
                                <i class="fas fa-<?php
                                    echo $notification['type'] === 'info' ? 'info-circle' :
                                        ($notification['type'] === 'success' ? 'check-circle' :
                                        ($notification['type'] === 'warning' ? 'exclamation-triangle' :
                                        ($notification['type'] === 'error' ? 'times-circle' : 'bullhorn')));
                                ?>"></i>
                            </div>

                            <div class="notification-content">
                                <div class="notification-title">
                                    <?php echo htmlspecialchars($notification['title']); ?>
                                    <?php if (!$notification['is_read']): ?>
                                        <span class="badge bg-primary ms-2">جديد</span>
                                    <?php endif; ?>
                                </div>

                                <div class="notification-message">
                                    <?php echo nl2br(htmlspecialchars($notification['message'])); ?>
                                </div>

                                <div class="notification-meta">
                                    <div class="meta-item">
                                        <i class="fas fa-tag"></i>
                                        <span class="priority-badge priority-<?php echo $notification['priority']; ?>">
                                            <?php
                                            $priority_labels = [
                                                'low' => 'منخفضة',
                                                'medium' => 'متوسطة',
                                                'high' => 'عالية',
                                                'urgent' => 'عاجلة'
                                            ];
                                            echo $priority_labels[$notification['priority']] ?? $notification['priority'];
                                            ?>
                                        </span>
                                    </div>

                                    <div class="meta-item">
                                        <i class="fas fa-users"></i>
                                        <span><?php
                                            $audience_labels = [
                                                'all' => 'الجميع',
                                                'parents' => 'أولياء الأمور',
                                                'students' => 'الطلاب',
                                                'specialists' => 'الأخصائيين',
                                                'admins' => 'المديرين'
                                            ];
                                            echo $audience_labels[$notification['target_audience']] ?? $notification['target_audience'];
                                        ?></span>
                                    </div>

                                    <div class="meta-item">
                                        <i class="fas fa-calendar"></i>
                                        <span><?php echo date('Y-m-d H:i', strtotime($notification['start_date'])); ?></span>
                                    </div>

                                    <?php if ($notification['read_at']): ?>
                                        <div class="meta-item">
                                            <i class="fas fa-eye"></i>
                                            <span>قُرئ في: <?php echo date('Y-m-d H:i', strtotime($notification['read_at'])); ?></span>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($notification['end_date']): ?>
                                        <div class="meta-item">
                                            <i class="fas fa-calendar-times"></i>
                                            <span>ينتهي: <?php echo date('Y-m-d H:i', strtotime($notification['end_date'])); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <?php if (!$notification['is_read']): ?>
                                    <div class="mt-2">
                                        <button class="btn btn-sm btn-mark-read" onclick="markAsRead(<?php echo $notification['id']; ?>, this)">
                                            <i class="fas fa-check"></i> تحديد كمقروء
                                        </button>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <!-- التنقل بين الصفحات -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination-container mt-4">
                        <nav aria-label="تنقل الصفحات">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>">
                                            <i class="fas fa-chevron-right"></i> السابق
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>">
                                            التالي <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد إشعارات</h4>
                    <p class="text-muted">لم يتم العثور على أي إشعارات لك حتى الآن</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function markAsRead(notificationId, buttonElement) {
    // إرسال طلب AJAX لتحديد الإشعار كمقروء
    fetch('mark_notification_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            notification_id: notificationId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إزالة علامة "غير مقروء" من الإشعار
            const notificationCard = buttonElement.closest('.notification-card');
            if (notificationCard) {
                notificationCard.classList.remove('unread');

                // إزالة الزر
                buttonElement.remove();

                // إزالة شارة "جديد"
                const newBadge = notificationCard.querySelector('.badge.bg-primary');
                if (newBadge) {
                    newBadge.remove();
                }
            }

            // إعادة تحميل الصفحة لتحديث الإحصائيات
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
    })
    .catch(error => {
        console.error('خطأ في تحديد الإشعار كمقروء:', error);
        alert('حدث خطأ في تحديد الإشعار كمقروء');
    });
}
</script>

<?php include 'includes/footer.php'; ?>
