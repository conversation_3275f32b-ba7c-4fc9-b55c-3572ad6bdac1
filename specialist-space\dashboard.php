<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كأخصائي
require_specialist();

$user = current_user();

// جلب الاستشارات الجديدة المعلقة
try {
    $pending_consultations_query = "SELECT c.*, p.name as parent_name, s.name as student_name, s.age, s.diagnosis_level
                                    FROM consultations c
                                    LEFT JOIN users p ON c.parent_id = p.id
                                    LEFT JOIN students s ON c.student_id = s.id
                                    WHERE c.status IN ('pending', 'assigned')
                                    AND (c.specialist_id IS NULL OR c.specialist_id = :specialist_id)
                                    ORDER BY
                                        CASE c.urgency_level
                                            WHEN 'urgent' THEN 1
                                            WHEN 'normal' THEN 2
                                            WHEN 'routine' THEN 3
                                        END,
                                        c.created_at ASC
                                    LIMIT 10";
    $pending_stmt = $db->prepare($pending_consultations_query);
    $pending_stmt->bindParam(':specialist_id', $user['id']);
    $pending_stmt->execute();
    $pending_consultations = $pending_stmt->fetchAll();
} catch (PDOException $e) {
    $pending_consultations = [];
}

// جلب المواعيد القادمة
try {
    $upcoming_appointments_query = "SELECT a.*, p.name as parent_name, s.name as student_name, s.age
                                   FROM appointments a
                                   LEFT JOIN users p ON a.parent_id = p.id
                                   LEFT JOIN students s ON a.student_id = s.id
                                   WHERE a.specialist_id = :specialist_id
                                   AND a.appointment_date >= NOW()
                                   AND a.status IN ('scheduled', 'confirmed')
                                   ORDER BY a.appointment_date ASC
                                   LIMIT 5";
    $appointments_stmt = $db->prepare($upcoming_appointments_query);
    $appointments_stmt->bindParam(':specialist_id', $user['id']);
    $appointments_stmt->execute();
    $upcoming_appointments = $appointments_stmt->fetchAll();
} catch (PDOException $e) {
    $upcoming_appointments = [];
}

// جلب المرضى المتابعين
try {
    $patients_query = "SELECT DISTINCT s.*, p.name as parent_name, p.phone as parent_phone,
                              COUNT(c.id) as consultation_count,
                              MAX(c.created_at) as last_consultation
                       FROM students s
                       LEFT JOIN users p ON s.parent_id = p.id
                       LEFT JOIN consultations c ON s.id = c.student_id AND c.specialist_id = :specialist_id
                       WHERE c.id IS NOT NULL
                       GROUP BY s.id
                       ORDER BY last_consultation DESC
                       LIMIT 8";
    $patients_stmt = $db->prepare($patients_query);
    $patients_stmt->bindParam(':specialist_id', $user['id']);
    $patients_stmt->execute();
    $patients = $patients_stmt->fetchAll();
} catch (PDOException $e) {
    $patients = [];
}

// جلب الإحصائيات
try {
    $stats_query = "SELECT
                        (SELECT COUNT(*) FROM consultations WHERE specialist_id = :specialist_id) as total_consultations,
                        (SELECT COUNT(*) FROM consultations WHERE specialist_id = :specialist_id AND status = 'completed') as completed_consultations,
                        (SELECT COUNT(*) FROM appointments WHERE specialist_id = :specialist_id AND appointment_date >= CURDATE()) as upcoming_appointments,
                        (SELECT COUNT(DISTINCT student_id) FROM consultations WHERE specialist_id = :specialist_id) as total_patients,
                        (SELECT AVG(rating) FROM consultation_ratings WHERE specialist_id = :specialist_id) as avg_rating";
    $stats_stmt = $db->prepare($stats_query);
    $stats_stmt->bindParam(':specialist_id', $user['id']);
    $stats_stmt->execute();
    $stats = $stats_stmt->fetch();
} catch (PDOException $e) {
    $stats = [
        'total_consultations' => 0,
        'completed_consultations' => 0,
        'upcoming_appointments' => 0,
        'total_patients' => 0,
        'avg_rating' => 0
    ];
}

// جلب الأنشطة الحديثة
try {
    $recent_activities_query = "SELECT 'consultation' as type, c.subject as title, c.created_at, p.name as user_name
                               FROM consultations c
                               LEFT JOIN users p ON c.parent_id = p.id
                               WHERE c.specialist_id = :specialist_id
                               UNION ALL
                               SELECT 'appointment' as type, CONCAT('موعد مع ', p.name) as title, a.created_at, p.name as user_name
                               FROM appointments a
                               LEFT JOIN users p ON a.parent_id = p.id
                               WHERE a.specialist_id = :specialist_id
                               ORDER BY created_at DESC
                               LIMIT 5";
    $activities_stmt = $db->prepare($recent_activities_query);
    $activities_stmt->bindParam(':specialist_id', $user['id']);
    $activities_stmt->execute();
    $recent_activities = $activities_stmt->fetchAll();
} catch (PDOException $e) {
    $recent_activities = [];
}

// إعداد متغيرات الصفحة
$page_title = 'فضاء الأخصائيين';
$page_description = 'لوحة تحكم الأخصائي لإدارة الاستشارات والمواعيد';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
/* Specialist Dashboard Modern Styles with Enhanced Arabic RTL Support */

/* تحسين مظهر أزرار الإجراءات السريعة */
.btn-lg {
    padding: 1.5rem 1rem;
    border-radius: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: none;
    font-weight: 600;
}

.btn-lg:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn-lg i {
    transition: all 0.3s ease;
}

.btn-lg:hover i {
    transform: scale(1.1);
}

/* ألوان مخصصة للأزرار */
.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f, #a8e6cf);
}

.btn-info {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
}
/* CSS للهيدر الموحد - تم نقله إلى nibrass-unified.css */

/* Enhanced Dashboard Cards with Better Arabic RTL Support */
.dashboard-card {
    background: white;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.08);
    padding: 2.5rem;
    margin-bottom: 2.5rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(40, 167, 69, 0.1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
    border-radius: 25px 25px 0 0;
}

.dashboard-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(40, 167, 69, 0.15);
    border-color: rgba(40, 167, 69, 0.3);
}

.card-header-custom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid rgba(40, 167, 69, 0.1);
    position: relative;
}

.card-header-custom::after {
    content: '';
    position: absolute;
    bottom: -2px;
    right: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 2px;
}

.card-icon {
    width: 70px;
    height: 70px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white !important;
    margin-left: 1.5rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    flex-shrink: 0;
        background-color: black;

}

.card-icon:hover {
    transform: scale(1.1) rotate(5deg);
}

.card-icon i {
    color: white !important;
    font-size: 1.8rem;
    line-height: 1;
}



.card-title-custom {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.card-subtitle {
    color: #7f8c8d;
    font-size: 1rem;
    margin-top: 0.5rem;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    font-weight: 400;
}

/* Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #28a745, #20c997);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(40, 167, 69, 0.15);
    border-color: rgba(40, 167, 69, 0.3);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white !important;
    margin: 0 auto 1rem auto;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    flex-shrink: 0;
}

.stat-icon i {
    color: white !important;
    font-size: 1.5rem;
    line-height: 1;
}

.stat-icon.green {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.stat-icon.blue {
    background: linear-gradient(135deg, #17a2b8, #138496);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
}

.stat-icon.orange {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
}

.stat-icon.purple {
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    box-shadow: 0 8px 25px rgba(111, 66, 193, 0.3);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.stat-label {
    color: #7f8c8d;
    font-size: 1rem;
    font-weight: 500;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

/* Consultation Cards */
.consultation-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border-right: 4px solid transparent;
}

.consultation-card:hover {
    transform: translateX(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.consultation-card.urgent {
    border-right-color: #dc3545;
}

.consultation-card.normal {
    border-right-color: #ffc107;
}

.consultation-card.routine {
    border-right-color: #28a745;
}

.consultation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.consultation-title {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    font-size: 1.1rem;
}

.urgency-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.urgency-urgent { background: #f8d7da; color: #721c24; }
.urgency-normal { background: #fff3cd; color: #856404; }
.urgency-routine { background: #d4edda; color: #155724; }

.consultation-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin: 1rem 0;
    font-size: 0.9rem;
    color: #666;
}

.consultation-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn-action {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-accept {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.btn-accept:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
    color: white;
}

.btn-respond {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-respond:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
    color: white;
}

.btn-schedule {
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    color: white;
}

.btn-schedule:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(111, 66, 193, 0.3);
    color: white;
}

/* Patient Cards */
.patient-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.patient-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.12);
    border-color: rgba(40, 167, 69, 0.3);
}

.patient-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.patient-avatar {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #28a745, #20c997);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    margin-left: 1rem;
}

.patient-info h5 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
}

.patient-info p {
    margin: 0;
    color: #7f8c8d;
    font-size: 0.9rem;
}



@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .consultation-header,
    .patient-header {
        flex-direction: column;
        text-align: center;
    }

    .consultation-actions {
        justify-content: center;
    }

    .card-header-custom {
        flex-direction: column;
        text-align: center;
    }

    .card-icon {
        margin-left: 0;
        margin-bottom: 1rem;
    }
}
</style>

<!-- قسم العنوان -->
<section class="nibrass-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1><i class="fas fa-user-md"></i> مرحباً د. <?php echo htmlspecialchars($user['name']); ?></h1>
                <p class="lead">شكراً لك على جهودك المتميزة في دعم الأطفال وأسرهم</p>
                <?php if (!empty($user['specialization'])): ?>
                <div class="mt-3">
                    <span class="badge bg-light text-dark fs-6">
                        <i class="fas fa-stethoscope"></i> <?php echo htmlspecialchars($user['specialization']); ?>
                    </span>
                </div>
                <?php endif; ?>
            </div>
            <div class="col-lg-4">
                <div class="text-center">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <i class="fas fa-comments fa-2x mb-2"></i>
                                <h4><?php echo $stats['total_consultations'] ?? 0; ?></h4>
                                <small>إجمالي الاستشارات</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h4><?php echo $stats['total_patients'] ?? 0; ?></h4>
                                <small>المرضى المتابعين</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- الإحصائيات -->
<section class="section">
    <div class="container">
        <!-- الإجراءات السريعة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon success">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom"></i>الإجراءات السريعة</h3>
                                <p class="card-subtitle">الوصول السريع للخدمات الأساسية</p>
                            </div>
                        </div>
                    </div>

                    <div class="row text-center">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="consultations.php" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-clipboard-list fa-2x mb-2 d-block"></i>
                                إدارة الاستشارات
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="appointments.php" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-calendar-plus fa-2x mb-2 d-block"></i>
                                إدارة المواعيد
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="patients.php" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-users fa-2x mb-2 d-block"></i>
                                ملفات المرضى
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="reports.php" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-chart-bar fa-2x mb-2 d-block"></i>
                                التقارير والإحصائيات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon green">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="stat-value"><?php echo count($pending_consultations); ?></div>
                <div class="stat-label">الاستشارات الجديدة</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon blue">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stat-value"><?php echo count($upcoming_appointments); ?></div>
                <div class="stat-label">المواعيد القادمة</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon orange">
                    <i class="fas fa-user-friends"></i>
                </div>
                <div class="stat-value"><?php echo count($patients); ?></div>
                <div class="stat-label">المرضى المتابعين</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon purple">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-value"><?php echo $stats['completed_consultations'] ?? 0; ?></div>
                <div class="stat-label">الاستشارات المكتملة</div>
            </div>
        </div>
    </div>
</section>

<!-- المحتوى الرئيسي -->
<section class="section">
    <div class="container">
        <div class="row">
            <!-- الاستشارات الجديدة -->
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon success">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">الاستشارات الجديدة</h3>
                                <p class="card-subtitle">طلبات الاستشارة المعلقة والجديدة</p>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($pending_consultations)): ?>
                        <?php foreach ($pending_consultations as $consultation): ?>
                            <div class="consultation-card <?php echo $consultation['urgency_level']; ?>">
                                <div class="consultation-header">
                                    <h5 class="consultation-title"><?php echo htmlspecialchars($consultation['subject']); ?></h5>
                                    <span class="urgency-badge urgency-<?php echo $consultation['urgency_level']; ?>">
                                        <?php
                                        $urgency_labels = [
                                            'urgent' => 'عاجل',
                                            'normal' => 'عادي',
                                            'routine' => 'روتيني'
                                        ];
                                        echo $urgency_labels[$consultation['urgency_level']] ?? 'عادي';
                                        ?>
                                    </span>
                                </div>

                                <p class="text-muted"><?php echo htmlspecialchars(substr($consultation['description'], 0, 100)) . '...'; ?></p>

                                <div class="consultation-meta">
                                    <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($consultation['parent_name']); ?></span>
                                    <?php if ($consultation['student_name']): ?>
                                    <span><i class="fas fa-child"></i> <?php echo htmlspecialchars($consultation['student_name']); ?></span>
                                    <?php endif; ?>
                                    <span><i class="fas fa-clock"></i> <?php echo date('d/m/Y H:i', strtotime($consultation['created_at'])); ?></span>
                                </div>

                                <div class="consultation-actions">
                                    <a href="consultation-details.php?id=<?php echo $consultation['id']; ?>" class="btn-action btn-accept">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </a>
                                    <a href="respond-consultation.php?id=<?php echo $consultation['id']; ?>" class="btn-action btn-respond">
                                        <i class="fas fa-reply"></i> الرد
                                    </a>
                                    <a href="schedule-appointment.php?consultation_id=<?php echo $consultation['id']; ?>" class="btn-action btn-schedule">
                                        <i class="fas fa-calendar-plus"></i> حجز موعد
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="text-center mt-3">
                            <a href="consultations.php" class="btn btn-success">
                                <i class="fas fa-list"></i> عرض جميع الاستشارات
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد استشارات جديدة</h5>
                            <p class="text-muted">ستظهر هنا طلبات الاستشارة الجديدة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- المواعيد القادمة -->
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon info">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">المواعيد القادمة</h3>
                                <p class="card-subtitle">مواعيدك المجدولة مع المرضى</p>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($upcoming_appointments)): ?>
                        <?php foreach ($upcoming_appointments as $appointment): ?>
                            <div class="consultation-card">
                                <div class="consultation-header">
                                    <h5 class="consultation-title">موعد مع <?php echo htmlspecialchars($appointment['parent_name']); ?></h5>
                                    <span class="urgency-badge urgency-normal">
                                        <?php echo $appointment['appointment_type']; ?>
                                    </span>
                                </div>

                                <div class="consultation-meta">
                                    <?php if ($appointment['student_name']): ?>
                                    <span><i class="fas fa-child"></i> <?php echo htmlspecialchars($appointment['student_name']); ?></span>
                                    <?php endif; ?>
                                    <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y', strtotime($appointment['appointment_date'])); ?></span>
                                    <span><i class="fas fa-clock"></i> <?php echo date('H:i', strtotime($appointment['appointment_date'])); ?></span>
                                    <span><i class="fas fa-hourglass-half"></i> <?php echo $appointment['duration_minutes']; ?> دقيقة</span>
                                </div>

                                <?php if ($appointment['notes']): ?>
                                <p class="text-muted mt-2"><?php echo htmlspecialchars($appointment['notes']); ?></p>
                                <?php endif; ?>

                                <div class="consultation-actions">
                                    <a href="appointment-details.php?id=<?php echo $appointment['id']; ?>" class="btn-action btn-accept">
                                        <i class="fas fa-eye"></i> التفاصيل
                                    </a>
                                    <a href="reschedule-appointment.php?id=<?php echo $appointment['id']; ?>" class="btn-action btn-respond">
                                        <i class="fas fa-edit"></i> إعادة جدولة
                                    </a>
                                    <?php if ($appointment['meeting_link']): ?>
                                    <a href="<?php echo htmlspecialchars($appointment['meeting_link']); ?>" target="_blank" class="btn-action btn-schedule">
                                        <i class="fas fa-video"></i> الانضمام
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="text-center mt-3">
                            <a href="appointments.php" class="btn btn-info">
                                <i class="fas fa-calendar"></i> عرض جميع المواعيد
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مواعيد قادمة</h5>
                            <p class="text-muted">ستظهر هنا مواعيدك المجدولة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- المرضى المتابعين -->
            <div class="col-lg-8">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon warning">
                                <i class="fas fa-user-friends"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">المرضى المتابعين</h3>
                                <p class="card-subtitle">الأطفال الذين تتابع حالاتهم</p>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($patients)): ?>
                        <div class="row">
                            <?php foreach ($patients as $patient): ?>
                                <div class="col-md-6">
                                    <div class="patient-card">
                                        <div class="patient-header">
                                            <div class="patient-avatar">
                                                <i class="fas fa-child"></i>
                                            </div>
                                            <div class="patient-info">
                                                <h5><?php echo htmlspecialchars($patient['name'] ?? 'غير محدد'); ?></h5>
                                                <p>
                                                    العمر: <?php echo $patient['age'] ?? 'غير محدد'; ?> سنة |
                                                    المستوى: <?php echo $patient['diagnosis_level'] ?? 'غير محدد'; ?>
                                                </p>
                                            </div>
                                        </div>

                                        <div class="consultation-meta">
                                            <span><i class="fas fa-user"></i> ولي الأمر: <?php echo htmlspecialchars($patient['parent_name']); ?></span>
                                            <span><i class="fas fa-comments"></i> <?php echo $patient['consultation_count']; ?> استشارة</span>
                                            <?php if ($patient['last_consultation']): ?>
                                            <span><i class="fas fa-clock"></i> آخر استشارة: <?php echo date('d/m/Y', strtotime($patient['last_consultation'])); ?></span>
                                            <?php endif; ?>
                                        </div>

                                        <div class="consultation-actions">
                                            <a href="patient-profile.php?id=<?php echo $patient['id']; ?>" class="btn-action btn-accept">
                                                <i class="fas fa-user"></i> الملف الشخصي
                                            </a>
                                            <a href="patient-history.php?id=<?php echo $patient['id']; ?>" class="btn-action btn-respond">
                                                <i class="fas fa-history"></i> التاريخ الطبي
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="text-center mt-3">
                            <a href="patients.php" class="btn btn-warning">
                                <i class="fas fa-users"></i> عرض جميع المرضى
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-user-friends fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد حالات متابعة</h5>
                            <p class="text-muted">ستظهر هنا الحالات التي تتابعها</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- الأنشطة الحديثة -->
            <div class="col-lg-4">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon primary">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">الأنشطة الحديثة</h3>
                                <p class="card-subtitle">آخر الأنشطة والتحديثات</p>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($recent_activities)): ?>
                        <?php foreach ($recent_activities as $activity): ?>
                            <div class="consultation-card">
                                <div class="consultation-header">
                                    <h6 class="consultation-title">
                                        <?php if ($activity['type'] == 'consultation'): ?>
                                            <i class="fas fa-comments text-success"></i>
                                        <?php else: ?>
                                            <i class="fas fa-calendar text-info"></i>
                                        <?php endif; ?>
                                        <?php echo htmlspecialchars($activity['title']); ?>
                                    </h6>
                                </div>

                                <div class="consultation-meta">
                                    <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($activity['user_name']); ?></span>
                                    <span><i class="fas fa-clock"></i> <?php echo date('d/m/Y H:i', strtotime($activity['created_at'])); ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أنشطة حديثة</h5>
                            <p class="text-muted">ستظهر هنا آخر أنشطتك</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>


    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تأثيرات التمرير للكروت
        const cards = document.querySelectorAll('.dashboard-card, .stat-card, .consultation-card, .patient-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // تحديث الوقت كل دقيقة
        setInterval(function() {
            const timeElements = document.querySelectorAll('[data-time]');
            timeElements.forEach(element => {
                const timestamp = element.getAttribute('data-time');
                const date = new Date(timestamp);
                const now = new Date();
                const diff = Math.floor((now - date) / 1000 / 60);

                if (diff < 60) {
                    element.textContent = diff + ' دقيقة مضت';
                } else if (diff < 1440) {
                    element.textContent = Math.floor(diff / 60) + ' ساعة مضت';
                }
            });
        }, 60000);

        // إضافة تأثيرات للأزرار
        const actionButtons = document.querySelectorAll('.btn-action');
        actionButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // إضافة تأثير النقر
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
