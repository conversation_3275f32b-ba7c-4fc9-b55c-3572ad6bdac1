<?php
/**
 * إنشاء الجداول الأساسية المطلوبة
 */

require_once 'config.php';
require_once 'db.php';

echo "إنشاء الجداول الأساسية...\n";

try {
    // جدول إعدادات المنصة
    $sql = "CREATE TABLE IF NOT EXISTS platform_settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        setting_key VARCHAR(255) NOT NULL UNIQUE,
        setting_value TEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        category VARCHAR(100) DEFAULT 'general',
        description TEXT,
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_setting_key (setting_key),
        INDEX idx_category (category)
    )";
    $db->exec($sql);
    echo "✅ تم إنشاء جدول platform_settings\n";

    // جدول الإشعارات
    $sql = "CREATE TABLE IF NOT EXISTS notifications (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'success', 'warning', 'error', 'announcement') DEFAULT 'info',
        target_audience ENUM('all', 'parents', 'students', 'specialists', 'admins') DEFAULT 'all',
        priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
        is_active BOOLEAN DEFAULT TRUE,
        start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        end_date TIMESTAMP NULL,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_type (type),
        INDEX idx_target_audience (target_audience),
        INDEX idx_is_active (is_active),
        INDEX idx_start_date (start_date)
    )";
    $db->exec($sql);
    echo "✅ تم إنشاء جدول notifications\n";

    // جدول قراءة الإشعارات
    $sql = "CREATE TABLE IF NOT EXISTS user_notifications (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        notification_id INT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        read_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_notification (user_id, notification_id),
        INDEX idx_user_id (user_id),
        INDEX idx_notification_id (notification_id),
        INDEX idx_is_read (is_read)
    )";
    $db->exec($sql);
    echo "✅ تم إنشاء جدول user_notifications\n";

    // جدول الأنشطة
    $sql = "CREATE TABLE IF NOT EXISTS activities (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        category ENUM('math', 'language', 'science', 'art', 'social') DEFAULT 'math',
        difficulty_level ENUM('easy', 'medium', 'hard') DEFAULT 'easy',
        target_age INT DEFAULT 5,
        duration_minutes INT DEFAULT 15,
        points_reward INT DEFAULT 10,
        rating DECIMAL(2,1) DEFAULT 5.0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_category (category),
        INDEX idx_difficulty_level (difficulty_level),
        INDEX idx_is_active (is_active)
    )";
    $db->exec($sql);
    echo "✅ تم إنشاء جدول activities\n";

    // إدراج الإعدادات الافتراضية
    $settings = [
        ['platform_name', 'منصة نبراس التعليمية', 'string', 'general', 'اسم المنصة', 1],
        ['platform_description', 'منصة تعليمية متخصصة في دعم الأطفال ذوي الاحتياجات الخاصة', 'string', 'general', 'وصف المنصة', 1],
        ['platform_version', '1.0.0', 'string', 'system', 'إصدار المنصة', 0],
        ['maintenance_mode', 'false', 'boolean', 'system', 'وضع الصيانة', 0],
        ['registration_enabled', 'true', 'boolean', 'user', 'تفعيل التسجيل الجديد', 0],
        ['contact_email', '<EMAIL>', 'string', 'contact', 'البريد الإلكتروني للتواصل', 1],
        ['support_phone', '+966123456789', 'string', 'contact', 'رقم الهاتف للدعم', 1]
    ];

    $stmt = $db->prepare("INSERT IGNORE INTO platform_settings (setting_key, setting_value, setting_type, category, description, is_public) VALUES (?, ?, ?, ?, ?, ?)");
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    echo "✅ تم إدراج الإعدادات الافتراضية\n";

    // إدراج إشعارات تجريبية
    $notifications = [
        ['مرحباً بكم في منصة نبراس', 'نرحب بكم في منصة نبراس التعليمية المتخصصة في دعم الأطفال ذوي الاحتياجات الخاصة', 'announcement', 'all', 'high'],
        ['تحديث جديد للمنصة', 'تم إضافة ميزات جديدة لتحسين تجربة التعلم', 'info', 'all', 'medium'],
        ['نصائح للآباء', 'تذكروا أن التشجيع والصبر هما مفتاح نجاح أطفالكم في التعلم', 'info', 'parents', 'low']
    ];

    $stmt = $db->prepare("INSERT IGNORE INTO notifications (title, message, type, target_audience, priority) VALUES (?, ?, ?, ?, ?)");
    foreach ($notifications as $notification) {
        $stmt->execute($notification);
    }
    echo "✅ تم إدراج الإشعارات التجريبية\n";

    // إدراج أنشطة تجريبية
    $activities = [
        ['لعبة الرياضيات المرحة', 'تعلم الجمع والطرح بطريقة ممتعة وتفاعلية', 'math', 'easy', 5, 15, 50, 5.0],
        ['عالم الحروف والكلمات', 'تعلم الحروف والكلمات بطريقة شيقة', 'language', 'easy', 4, 20, 40, 4.5],
        ['اكتشاف العلوم', 'تجارب علمية بسيطة وممتعة للأطفال', 'science', 'medium', 6, 25, 60, 4.8],
        ['الرسم والتلوين', 'أطلق إبداعك في الرسم والتلوين', 'art', 'easy', 3, 30, 35, 4.2]
    ];

    $stmt = $db->prepare("INSERT IGNORE INTO activities (title, description, category, difficulty_level, target_age, duration_minutes, points_reward, rating) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    foreach ($activities as $activity) {
        $stmt->execute($activity);
    }
    echo "✅ تم إدراج الأنشطة التجريبية\n";

    echo "\n🎉 تم إنشاء جميع الجداول الأساسية بنجاح!\n";

} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    exit(1);
}

// التحقق النهائي
echo "\n📋 التحقق من الجداول:\n";
try {
    $tables = ['platform_settings', 'notifications', 'user_notifications', 'activities'];
    foreach ($tables as $table) {
        $result = $db->query("SELECT COUNT(*) FROM $table");
        $count = $result->fetchColumn();
        echo "✅ $table - $count سجل\n";
    }
} catch (PDOException $e) {
    echo "خطأ في التحقق: " . $e->getMessage() . "\n";
}

echo "\n🚀 النظام جاهز للاستخدام!\n";
?>
