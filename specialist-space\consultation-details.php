<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كأخصائي
require_specialist();

$user = current_user();
$consultation_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$consultation_id) {
    redirect('consultations.php');
}

// جلب تفاصيل الاستشارة
try {
    $consultation_query = "SELECT c.*, p.name as parent_name, p.phone as parent_phone, p.email as parent_email,
                                  s.name as student_name, s.age, s.diagnosis_level, s.progress_level
                           FROM consultations c
                           LEFT JOIN users p ON c.parent_id = p.id
                           LEFT JOIN students s ON c.student_id = s.id
                           WHERE c.id = :id";
    $consultation_stmt = $db->prepare($consultation_query);
    $consultation_stmt->bindParam(':id', $consultation_id);
    $consultation_stmt->execute();
    $consultation = $consultation_stmt->fetch();
    
    if (!$consultation) {
        redirect('consultations.php');
    }
} catch (PDOException $e) {
    redirect('consultations.php');
}

// جلب ردود الاستشارة
try {
    $responses_query = "SELECT cr.*, u.name as specialist_name
                        FROM consultation_responses cr
                        LEFT JOIN users u ON cr.specialist_id = u.id
                        WHERE cr.consultation_id = :consultation_id
                        ORDER BY cr.created_at ASC";
    $responses_stmt = $db->prepare($responses_query);
    $responses_stmt->bindParam(':consultation_id', $consultation_id);
    $responses_stmt->execute();
    $responses = $responses_stmt->fetchAll();
} catch (PDOException $e) {
    $responses = [];
}

// معالجة إضافة رد جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['response_text'])) {
    $response_text = clean_input($_POST['response_text']);
    $is_private = isset($_POST['is_private']) ? 1 : 0;
    
    if (!empty($response_text)) {
        try {
            $insert_response_query = "INSERT INTO consultation_responses (consultation_id, specialist_id, response_text, is_private) 
                                     VALUES (:consultation_id, :specialist_id, :response_text, :is_private)";
            $insert_response_stmt = $db->prepare($insert_response_query);
            $insert_response_stmt->execute([
                ':consultation_id' => $consultation_id,
                ':specialist_id' => $user['id'],
                ':response_text' => $response_text,
                ':is_private' => $is_private
            ]);
            
            // تحديث حالة الاستشارة
            $update_status_query = "UPDATE consultations SET status = 'in_progress', specialist_id = :specialist_id WHERE id = :id";
            $update_status_stmt = $db->prepare($update_status_query);
            $update_status_stmt->execute([':specialist_id' => $user['id'], ':id' => $consultation_id]);
            
            $success_message = "تم إضافة الرد بنجاح";
            
            // إعادة تحميل الردود
            $responses_stmt->execute();
            $responses = $responses_stmt->fetchAll();
            
        } catch (PDOException $e) {
            $error_message = "حدث خطأ في إضافة الرد";
        }
    } else {
        $error_message = "يرجى كتابة نص الرد";
    }
}

// إعداد متغيرات الصفحة
$page_title = 'تفاصيل الاستشارة';
$page_description = 'عرض تفاصيل الاستشارة والرد عليها';

// تضمين الهيدر
include '../includes/header.php';
?>

<style>
.consultation-details-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.consultation-details-header h1,
.consultation-details-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

/* تأكيد ظهور الأيقونات */
.consultation-details-header i,
.detail-card i,
.btn i,
.alert i,
.badge i {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

.detail-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.detail-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.response-item {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-right: 4px solid #17a2b8;
}

.response-item.private {
    background: #fff3cd;
    border-right-color: #ffc107;
}

.response-form {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 2rem;
}


</style>

<!-- قسم العنوان -->
<section class="consultation-details-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-clipboard-list"></i> تفاصيل الاستشارة</h1>
            <p class="lead">عرض تفاصيل الاستشارة والتفاعل معها</p>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- تفاصيل الاستشارة -->
            <div class="col-lg-8">
                <div class="detail-card">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h3><?php echo htmlspecialchars($consultation['subject']); ?></h3>
                        <div>
                            <span class="badge bg-<?php echo $consultation['urgency_level'] == 'urgent' ? 'danger' : ($consultation['urgency_level'] == 'normal' ? 'warning' : 'success'); ?>">
                                <?php 
                                $urgency_labels = ['urgent' => 'عاجل', 'normal' => 'عادي', 'routine' => 'روتيني'];
                                echo $urgency_labels[$consultation['urgency_level']] ?? $consultation['urgency_level'];
                                ?>
                            </span>
                            <span class="badge bg-info ms-2">
                                <?php 
                                $status_labels = ['pending' => 'معلقة', 'assigned' => 'مقبولة', 'in_progress' => 'قيد التنفيذ', 'completed' => 'مكتملة'];
                                echo $status_labels[$consultation['status']] ?? $consultation['status'];
                                ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h5><i class="fas fa-align-right"></i> وصف المشكلة:</h5>
                        <p class="text-muted"><?php echo nl2br(htmlspecialchars($consultation['description'])); ?></p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-tag"></i> نوع الاستشارة:</h6>
                            <p><?php echo $consultation['consultation_type']; ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-clock"></i> تاريخ الطلب:</h6>
                            <p><?php echo date('d/m/Y H:i', strtotime($consultation['created_at'])); ?></p>
                        </div>
                    </div>
                    
                    <?php if ($consultation['preferred_date']): ?>
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-calendar"></i> التاريخ المفضل:</h6>
                            <p><?php echo date('d/m/Y', strtotime($consultation['preferred_date'])); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-sun"></i> الوقت المفضل:</h6>
                            <p><?php echo $consultation['preferred_time']; ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- الردود -->
                <div class="detail-card">
                    <h4><i class="fas fa-comments"></i> الردود والمتابعة</h4>
                    
                    <?php if (!empty($responses)): ?>
                        <?php foreach ($responses as $response): ?>
                            <div class="response-item <?php echo $response['is_private'] ? 'private' : ''; ?>">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <strong><?php echo htmlspecialchars($response['specialist_name']); ?></strong>
                                    <div>
                                        <?php if ($response['is_private']): ?>
                                            <span class="badge bg-warning">ملاحظة خاصة</span>
                                        <?php endif; ?>
                                        <small class="text-muted"><?php echo date('d/m/Y H:i', strtotime($response['created_at'])); ?></small>
                                    </div>
                                </div>
                                <p class="mb-0"><?php echo nl2br(htmlspecialchars($response['response_text'])); ?></p>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted text-center py-3">لا توجد ردود بعد</p>
                    <?php endif; ?>
                </div>

                <!-- نموذج الرد -->
                <?php if ($consultation['status'] != 'completed'): ?>
                <div class="response-form">
                    <h4><i class="fas fa-reply"></i> إضافة رد</h4>
                    <form method="POST">
                        <div class="mb-3">
                            <label for="response_text" class="form-label">نص الرد:</label>
                            <textarea class="form-control" id="response_text" name="response_text" rows="5" required></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_private" name="is_private">
                                <label class="form-check-label" for="is_private">
                                    ملاحظة خاصة (لن تظهر لولي الأمر)
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-paper-plane"></i> إرسال الرد
                            </button>
                            <a href="schedule-appointment.php?consultation_id=<?php echo $consultation_id; ?>" class="btn btn-warning">
                                <i class="fas fa-calendar-plus"></i> حجز موعد
                            </a>
                        </div>
                    </form>
                </div>
                <?php endif; ?>
            </div>

            <!-- معلومات ولي الأمر والطفل -->
            <div class="col-lg-4">
                <div class="detail-card">
                    <h4><i class="fas fa-user"></i> معلومات ولي الأمر</h4>
                    <div class="mb-3">
                        <strong>الاسم:</strong>
                        <p><?php echo htmlspecialchars($consultation['parent_name']); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>البريد الإلكتروني:</strong>
                        <p><?php echo htmlspecialchars($consultation['parent_email']); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>رقم الهاتف:</strong>
                        <p><?php echo htmlspecialchars($consultation['parent_phone'] ?? 'غير محدد'); ?></p>
                    </div>
                </div>

                <?php if ($consultation['student_name']): ?>
                <div class="detail-card">
                    <h4><i class="fas fa-child"></i> معلومات الطفل</h4>
                    <div class="mb-3">
                        <strong>الاسم:</strong>
                        <p><?php echo htmlspecialchars($consultation['student_name']); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>العمر:</strong>
                        <p><?php echo $consultation['age']; ?> سنة</p>
                    </div>
                    <div class="mb-3">
                        <strong>مستوى التشخيص:</strong>
                        <p><?php echo htmlspecialchars($consultation['diagnosis_level'] ?? 'غير محدد'); ?></p>
                    </div>
                    <div class="mb-3">
                        <strong>مستوى التقدم:</strong>
                        <div class="progress">
                            <div class="progress-bar" style="width: <?php echo $consultation['progress_level'] ?? 0; ?>%">
                                <?php echo $consultation['progress_level'] ?? 0; ?>%
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="detail-card">
                    <h4><i class="fas fa-tools"></i> إجراءات</h4>
                    <div class="d-grid gap-2">
                        <a href="consultations.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للقائمة
                        </a>
                        <a href="schedule-appointment.php?consultation_id=<?php echo $consultation_id; ?>" class="btn btn-warning">
                            <i class="fas fa-calendar-plus"></i> حجز موعد
                        </a>
                        <?php if ($consultation['status'] != 'completed'): ?>
                        <button class="btn btn-success" onclick="markAsCompleted()">
                            <i class="fas fa-check-circle"></i> إكمال الاستشارة
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    function markAsCompleted() {
        if (confirm('هل أنت متأكد من إكمال هذه الاستشارة؟')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'consultations.php';
            
            const consultationId = document.createElement('input');
            consultationId.type = 'hidden';
            consultationId.name = 'consultation_id';
            consultationId.value = '" . $consultation_id . "';
            
            const action = document.createElement('input');
            action.type = 'hidden';
            action.name = 'action';
            action.value = 'complete';
            
            form.appendChild(consultationId);
            form.appendChild(action);
            document.body.appendChild(form);
            form.submit();
        }
    }
";

// تضمين الفوتر
include '../includes/footer.php';
?>
