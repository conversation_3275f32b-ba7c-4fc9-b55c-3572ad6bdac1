<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كمدير
require_admin();

$user = current_user();

// معالجة الإجراءات
$action = $_GET['action'] ?? '';
$success_message = '';
$error_message = '';

// معالجة تفعيل/إلغاء تفعيل المستخدم
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['toggle_user'])) {
    check_csrf();
    
    $user_id = (int)$_POST['user_id'];
    $current_status = (int)$_POST['current_status'];
    $new_status = $current_status ? 0 : 1;
    
    try {
        $update_query = "UPDATE users SET is_active = :status WHERE id = :user_id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':status', $new_status);
        $update_stmt->bindParam(':user_id', $user_id);
        
        if ($update_stmt->execute()) {
            $action_text = $new_status ? 'تفعيل' : 'إلغاء تفعيل';
            $success_message = "تم {$action_text} المستخدم بنجاح";
        } else {
            $error_message = "فشل في تحديث حالة المستخدم";
        }
    } catch (PDOException $e) {
        $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
    }
}

// معالجة حذف المستخدم
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_user'])) {
    check_csrf();
    
    $user_id = (int)$_POST['user_id'];
    
    try {
        $delete_query = "DELETE FROM users WHERE id = :user_id AND id != :current_user_id";
        $delete_stmt = $db->prepare($delete_query);
        $delete_stmt->bindParam(':user_id', $user_id);
        $delete_stmt->bindParam(':current_user_id', $user['id']);
        
        if ($delete_stmt->execute()) {
            if ($delete_stmt->rowCount() > 0) {
                $success_message = "تم حذف المستخدم بنجاح";
            } else {
                $error_message = "لا يمكن حذف هذا المستخدم";
            }
        } else {
            $error_message = "فشل في حذف المستخدم";
        }
    } catch (PDOException $e) {
        $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
    }
}

// جلب المستخدمين مع الفلترة والبحث
$search = $_GET['search'] ?? '';
$role_filter = $_GET['role'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

try {
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(name LIKE :search OR email LIKE :search)";
        $params[':search'] = "%{$search}%";
    }
    
    if (!empty($role_filter)) {
        $where_conditions[] = "role = :role";
        $params[':role'] = $role_filter;
    }
    
    if ($status_filter !== '') {
        $where_conditions[] = "is_active = :status";
        $params[':status'] = (int)$status_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // جلب المستخدمين
    $users_query = "SELECT * FROM users {$where_clause} ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
    $users_stmt = $db->prepare($users_query);
    
    foreach ($params as $key => $value) {
        $users_stmt->bindValue($key, $value);
    }
    $users_stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $users_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $users_stmt->execute();
    $users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب العدد الإجمالي
    $count_query = "SELECT COUNT(*) as total FROM users {$where_clause}";
    $count_stmt = $db->prepare($count_query);
    
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    
    $count_stmt->execute();
    $total_users = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_users / $limit);
    
} catch (PDOException $e) {
    $error_message = "خطأ في جلب المستخدمين: " . $e->getMessage();
    $users = [];
    $total_users = 0;
    $total_pages = 0;
}

// إعداد متغيرات الصفحة
$page_title = 'إدارة المستخدمين';
$page_description = 'إدارة حسابات المستخدمين في منصة نبراس';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
.admin-container {
    /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
    min-height: 100vh;
    padding: 2rem 0;
}

.admin-header {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 5rem;
}

.filters-section {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.users-table-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow-x: auto;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.users-table th,
.users-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.users-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    position: sticky;
    top: 0;
}

.users-table tr:hover {
    background: #f8f9fa;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-left: auto;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-details h6 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
}

.user-details small {
    color: #6c757d;
}

.role-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
}

.role-badge.admin {
    background: #dc3545;
    color: white;
}

.role-badge.parent {
    background: #28a745;
    color: white;
}

.role-badge.specialist {
    background: #4a90e2;
    color: white;
}

.role-badge.student {
    background: #ffc107;
    color: #212529;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 8px;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.pagination {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.pagination a,
.pagination span {
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    text-decoration: none;
    color: #6c757d;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: #4a90e2;
    color: white;
    border-color: #4a90e2;
}

.pagination .current {
    background: #4a90e2;
    color: white;
    border-color: #4a90e2;
}

.search-filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1rem;
    align-items: end;
}

@media (max-width: 768px) {
    .search-filters {
        grid-template-columns: 1fr;
    }
    
    .users-table-container {
        padding: 1rem;
    }
    
    .users-table {
        font-size: 0.9rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>

<?php include 'includes/admin-nav.php'; ?>

<div class="admin-container">
    <div class="container admin-content mx-auto">
        <!-- رأس الصفحة -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-users"></i> إدارة المستخدمين</h1>
                    <p class="text-muted mb-0">إدارة حسابات المستخدمين في منصة نبراس</p>
                </div>
                <div>
                    <a href="dashboard.php" class="nibrass-btn nibrass-btn-outline-primary">
                        <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- فلاتر البحث -->
        <div class="filters-section">
            <form method="GET" class="search-filters">
                <div>
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث بالاسم أو البريد الإلكتروني..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div>
                    <label class="form-label">نوع المستخدم</label>
                    <select name="role" class="form-select">
                        <option value="">جميع الأنواع</option>
                        <option value="admin" <?php echo $role_filter === 'admin' ? 'selected' : ''; ?>>مدير</option>
                        <option value="parent" <?php echo $role_filter === 'parent' ? 'selected' : ''; ?>>ولي أمر</option>
                        <option value="specialist" <?php echo $role_filter === 'specialist' ? 'selected' : ''; ?>>أخصائي</option>
                        <option value="student" <?php echo $role_filter === 'student' ? 'selected' : ''; ?>>طالب</option>
                    </select>
                </div>
                
                <div>
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="1" <?php echo $status_filter === '1' ? 'selected' : ''; ?>>نشط</option>
                        <option value="0" <?php echo $status_filter === '0' ? 'selected' : ''; ?>>غير نشط</option>
                    </select>
                </div>
                
                <div>
                    <button type="submit" class="nibrass-btn nibrass-btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>

        <!-- جدول المستخدمين -->
        <div class="users-table-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3><i class="fas fa-list"></i> قائمة المستخدمين</h3>
                <div class="text-muted">
                    إجمالي النتائج: <?php echo number_format($total_users); ?>
                </div>
            </div>

            <?php if (empty($users)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-users-slash fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد نتائج</h4>
                    <p class="text-muted">لم يتم العثور على مستخدمين مطابقين لمعايير البحث</p>
                </div>
            <?php else: ?>
                <table class="users-table">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>نوع الحساب</th>
                            <th>الحالة</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user_item): ?>
                            <tr>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar">
                                            <?php echo strtoupper(substr($user_item['name'], 0, 1)); ?>
                                        </div>
                                        <div class="user-details">
                                            <h6><?php echo htmlspecialchars($user_item['name']); ?></h6>
                                            <small><?php echo htmlspecialchars($user_item['email']); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="role-badge <?php echo $user_item['role']; ?>">
                                        <?php 
                                        $roles = [
                                            'admin' => 'مدير',
                                            'parent' => 'ولي أمر',
                                            'specialist' => 'أخصائي',
                                            'student' => 'طالب'
                                        ];
                                        echo $roles[$user_item['role']] ?? $user_item['role'];
                                        ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge <?php echo $user_item['is_active'] ? 'active' : 'inactive'; ?>">
                                        <?php echo $user_item['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php echo date('Y-m-d', strtotime($user_item['created_at'])); ?>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <?php if ($user_item['id'] != $user['id']): ?>
                                            <!-- تفعيل/إلغاء تفعيل -->
                                            <form method="POST" style="display: inline;">
                                                <?php echo csrf_field(); ?>
                                                <input type="hidden" name="user_id" value="<?php echo $user_item['id']; ?>">
                                                <input type="hidden" name="current_status" value="<?php echo $user_item['is_active']; ?>">
                                                <button type="submit" name="toggle_user" 
                                                        class="nibrass-btn <?php echo $user_item['is_active'] ? 'nibrass-btn-warning' : 'nibrass-btn-success'; ?> btn-sm"
                                                        onclick="return confirm('هل أنت متأكد من تغيير حالة هذا المستخدم؟')">
                                                    <i class="fas <?php echo $user_item['is_active'] ? 'fa-pause' : 'fa-play'; ?>"></i>
                                                    <?php echo $user_item['is_active'] ? 'إلغاء تفعيل' : 'تفعيل'; ?>
                                                </button>
                                            </form>

                                            <!-- حذف -->
                                            <form method="POST" style="display: inline;">
                                                <?php echo csrf_field(); ?>
                                                <input type="hidden" name="user_id" value="<?php echo $user_item['id']; ?>">
                                                <button type="submit" name="delete_user" 
                                                        class="nibrass-btn nibrass-btn-danger btn-sm"
                                                        onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <span class="text-muted">المستخدم الحالي</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <!-- التنقل بين الصفحات -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination-container">
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                    <i class="fas fa-chevron-right"></i> السابق
                                </a>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <?php if ($i == $page): ?>
                                    <span class="current"><?php echo $i; ?></span>
                                <?php else: ?>
                                    <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                    التالي <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// تضمين الفوتر
include '../includes/footer.php';
?>
