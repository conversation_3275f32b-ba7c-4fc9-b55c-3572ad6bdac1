/* 
 * Nibrass Educational Platform - Unified CSS Framework
 * Consistent styling across Student, Parent, and Specialist spaces
 * Version: 1.0
 * Arabic RTL Support with Cairo and Tajawal fonts
 */

/* ===== CSS VARIABLES ===== */
:root {
    /* Primary Color Scheme */
    --nibrass-primary: #4a90e2;
    --nibrass-primary-dark: #2c5aa0;
    --nibrass-primary-light: #6ba3e8;
    --nibrass-primary-lighter: #e3f2fd;
    
    /* Secondary Colors */
    --nibrass-secondary: #6c757d;
    --nibrass-secondary-light: #adb5bd;
    
    /* Status Colors */
    --nibrass-success: #28a745;
    --nibrass-success-light: #d4edda;
    --nibrass-warning: #ffc107;
    --nibrass-warning-light: #fff3cd;
    --nibrass-danger: #dc3545;
    --nibrass-danger-light: #f8d7da;
    --nibrass-info: #17a2b8;
    --nibrass-info-light: #d1ecf1;
    
    /* Neutral Colors */
    --nibrass-light: #f8f9fa;
    --nibrass-dark: #343a40;
    --nibrass-white: #ffffff;
    --nibrass-gray-100: #f8f9fa;
    --nibrass-gray-200: #e9ecef;
    --nibrass-gray-300: #dee2e6;
    --nibrass-gray-400: #ced4da;
    --nibrass-gray-500: #adb5bd;
    --nibrass-gray-600: #6c757d;
    --nibrass-gray-700: #495057;
    --nibrass-gray-800: #343a40;
    --nibrass-gray-900: #212529;
    
    /* Typography */
    --nibrass-font-family-arabic: 'Cairo', 'Tajawal', sans-serif;
    --nibrass-font-family-english: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --nibrass-font-size-xs: 0.75rem;
    --nibrass-font-size-sm: 0.875rem;
    --nibrass-font-size-base: 1rem;
    --nibrass-font-size-lg: 1.125rem;
    --nibrass-font-size-xl: 1.25rem;
    --nibrass-font-size-2xl: 1.5rem;
    --nibrass-font-size-3xl: 1.875rem;
    --nibrass-font-size-4xl: 2.25rem;
    
    /* Font Weights */
    --nibrass-font-weight-light: 300;
    --nibrass-font-weight-normal: 400;
    --nibrass-font-weight-medium: 500;
    --nibrass-font-weight-semibold: 600;
    --nibrass-font-weight-bold: 700;
    
    /* Spacing */
    --nibrass-spacing-xs: 0.25rem;
    --nibrass-spacing-sm: 0.5rem;
    --nibrass-spacing-md: 1rem;
    --nibrass-spacing-lg: 1.5rem;
    --nibrass-spacing-xl: 2rem;
    --nibrass-spacing-2xl: 3rem;
    --nibrass-spacing-3xl: 4rem;
    
    /* Border Radius */
    --nibrass-border-radius-sm: 0.375rem;
    --nibrass-border-radius-md: 0.5rem;
    --nibrass-border-radius-lg: 0.75rem;
    --nibrass-border-radius-xl: 1rem;
    --nibrass-border-radius-2xl: 1.5rem;
    --nibrass-border-radius-3xl: 2rem;
    --nibrass-border-radius-full: 9999px;
    
    /* Shadows */
    --nibrass-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --nibrass-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --nibrass-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --nibrass-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --nibrass-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Transitions */
    --nibrass-transition-fast: 0.15s ease-in-out;
    --nibrass-transition-normal: 0.3s ease-in-out;
    --nibrass-transition-slow: 0.5s ease-in-out;
    
    /* Z-index */
    --nibrass-z-dropdown: 1000;
    --nibrass-z-sticky: 1020;
    --nibrass-z-fixed: 1030;
    --nibrass-z-modal-backdrop: 1040;
    --nibrass-z-modal: 1050;
    --nibrass-z-popover: 1060;
    --nibrass-z-tooltip: 1070;
}

/* ===== FONT IMPORTS ===== */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

/* ===== BASE STYLES ===== */
* {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

html[dir="rtl"] {
    font-family: var(--nibrass-font-family-arabic);
}

html[dir="ltr"] {
    font-family: var(--nibrass-font-family-english);
}

body {
    font-family: var(--nibrass-font-family-arabic);
    font-size: var(--nibrass-font-size-base);
    font-weight: var(--nibrass-font-weight-normal);
    line-height: 1.6;
    color: var(--nibrass-gray-800);
    background-color: var(--nibrass-gray-100);
    margin: 0;
    padding: 0;
    direction: rtl;
    text-align: right;
}

/* Enhanced Arabic text rendering */
.arabic-text,
[lang="ar"] {
    font-feature-settings: "liga" 1, "calt" 1, "kern" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== UNIFIED HEADER STYLES ===== */
.nibrass-header {
    background: var(--nibrass-primary);
    color: var(--nibrass-white);
    padding: var(--nibrass-spacing-2xl) 0;
    border-radius: 0 0 var(--nibrass-border-radius-3xl) var(--nibrass-border-radius-3xl);
    margin-bottom: var(--nibrass-spacing-xl);
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
    position: relative;
    overflow: hidden;
    margin-top: 80px;

}

.nibrass-header h1,
.nibrass-header h2,
.nibrass-header h3,
.nibrass-header p,
.nibrass-header .lead {
    color: var(--nibrass-white) !important;
    font-family: var(--nibrass-font-family-arabic);
    margin-bottom: var(--nibrass-spacing-md);
}

.nibrass-header h1 {
    font-size: var(--nibrass-font-size-4xl);
    font-weight: var(--nibrass-font-weight-bold);
    text-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.nibrass-header .lead {
    font-size: var(--nibrass-font-size-lg);
    font-weight: var(--nibrass-font-weight-normal);
    opacity: 0.95;
}

/* ===== UNIFIED CARD STYLES ===== */
.nibrass-card {
    background: var(--nibrass-white);
    border-radius: var(--nibrass-border-radius-2xl);
    box-shadow: var(--nibrass-shadow-lg);
    padding: var(--nibrass-spacing-xl);
    margin-bottom: var(--nibrass-spacing-xl);
    transition: all var(--nibrass-transition-normal);
    border: 1px solid var(--nibrass-gray-200);
    position: relative;
    overflow: hidden;
}

.nibrass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--nibrass-primary), var(--nibrass-primary-light));
    border-radius: var(--nibrass-border-radius-2xl) var(--nibrass-border-radius-2xl) 0 0;
}

.nibrass-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--nibrass-shadow-2xl);
    border-color: var(--nibrass-primary-light);
}

.nibrass-card-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--nibrass-spacing-lg);
    padding-bottom: var(--nibrass-spacing-md);
    border-bottom: 1px solid var(--nibrass-gray-200);
}

.nibrass-card-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--nibrass-border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--nibrass-font-size-2xl);
    color: var(--nibrass-white);
    margin-left: var(--nibrass-spacing-md);
    box-shadow: var(--nibrass-shadow-md);
}

.nibrass-card-icon.primary { background: linear-gradient(135deg, var(--nibrass-primary), var(--nibrass-primary-dark)); }
.nibrass-card-icon.success { background: linear-gradient(135deg, var(--nibrass-success), #20c997); }
.nibrass-card-icon.warning { background: linear-gradient(135deg, var(--nibrass-warning), #ffb347); }
.nibrass-card-icon.danger { background: linear-gradient(135deg, var(--nibrass-danger), #ee5a24); }
.nibrass-card-icon.info { background: linear-gradient(135deg, var(--nibrass-info), #45b7d1); }

.nibrass-card-title {
    font-size: var(--nibrass-font-size-xl);
    font-weight: var(--nibrass-font-weight-semibold);
    color: var(--nibrass-gray-800);
    margin: 0;
    font-family: var(--nibrass-font-family-arabic);
}

.nibrass-card-subtitle {
    font-size: var(--nibrass-font-size-sm);
    color: var(--nibrass-gray-600);
    margin: 0;
    font-family: var(--nibrass-font-family-arabic);
}

/* ===== UNIFIED BUTTON STYLES ===== */
.nibrass-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--nibrass-spacing-sm) var(--nibrass-spacing-lg);
    font-size: var(--nibrass-font-size-base);
    font-weight: var(--nibrass-font-weight-medium);
    font-family: var(--nibrass-font-family-arabic);
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--nibrass-border-radius-lg);
    cursor: pointer;
    transition: all var(--nibrass-transition-normal);
    min-height: 44px;
    min-width: 44px;
    gap: var(--nibrass-spacing-sm);
}

.nibrass-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--nibrass-shadow-lg);
    text-decoration: none;
}

.nibrass-btn:active {
    transform: translateY(0);
}

/* Button Variants */
.nibrass-btn-primary {
    background: linear-gradient(135deg, var(--nibrass-primary), var(--nibrass-primary-dark));
    color: var(--nibrass-white);
    border-color: var(--nibrass-primary);
}

.nibrass-btn-primary:hover {
    background: linear-gradient(135deg, var(--nibrass-primary-dark), var(--nibrass-primary));
    color: var(--nibrass-white);
    box-shadow: 0 10px 25px rgba(74, 144, 226, 0.3);
}

.nibrass-btn-outline-primary {
    background: transparent;
    color: var(--nibrass-primary);
    border-color: var(--nibrass-primary);
}

.nibrass-btn-outline-primary:hover {
    background: var(--nibrass-primary);
    color: var(--nibrass-white);
}

.nibrass-btn-success {
    background: linear-gradient(135deg, var(--nibrass-success), #20c997);
    color: var(--nibrass-white);
    border-color: var(--nibrass-success);
}

.nibrass-btn-success:hover {
    background: linear-gradient(135deg, #20c997, var(--nibrass-success));
    color: var(--nibrass-white);
    box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
}

.nibrass-btn-warning {
    background: linear-gradient(135deg, var(--nibrass-warning), #ffb347);
    color: var(--nibrass-gray-800);
    border-color: var(--nibrass-warning);
}

.nibrass-btn-warning:hover {
    background: linear-gradient(135deg, #ffb347, var(--nibrass-warning));
    color: var(--nibrass-gray-800);
    box-shadow: 0 10px 25px rgba(255, 193, 7, 0.3);
}

.nibrass-btn-danger {
    background: linear-gradient(135deg, var(--nibrass-danger), #ee5a24);
    color: var(--nibrass-white);
    border-color: var(--nibrass-danger);
}

.nibrass-btn-danger:hover {
    background: linear-gradient(135deg, #ee5a24, var(--nibrass-danger));
    color: var(--nibrass-white);
    box-shadow: 0 10px 25px rgba(220, 53, 69, 0.3);
}

/* Button Sizes */
.nibrass-btn-sm {
    padding: var(--nibrass-spacing-xs) var(--nibrass-spacing-md);
    font-size: var(--nibrass-font-size-sm);
    min-height: 36px;
}

.nibrass-btn-lg {
    padding: var(--nibrass-spacing-md) var(--nibrass-spacing-2xl);
    font-size: var(--nibrass-font-size-lg);
    min-height: 52px;
}

/* ===== UNIFIED FORM STYLES ===== */
.nibrass-form-group {
    margin-bottom: var(--nibrass-spacing-lg);
}

.nibrass-form-label {
    display: block;
    font-size: var(--nibrass-font-size-sm);
    font-weight: var(--nibrass-font-weight-medium);
    color: var(--nibrass-gray-700);
    margin-bottom: var(--nibrass-spacing-sm);
    font-family: var(--nibrass-font-family-arabic);
}

.nibrass-form-control {
    display: block;
    width: 100%;
    padding: var(--nibrass-spacing-sm) var(--nibrass-spacing-md);
    font-size: var(--nibrass-font-size-base);
    font-family: var(--nibrass-font-family-arabic);
    color: var(--nibrass-gray-800);
    background-color: var(--nibrass-white);
    border: 2px solid var(--nibrass-gray-300);
    border-radius: var(--nibrass-border-radius-md);
    transition: all var(--nibrass-transition-normal);
    min-height: 44px;
}

.nibrass-form-control:focus {
    outline: none;
    border-color: var(--nibrass-primary);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.nibrass-form-control::placeholder {
    color: var(--nibrass-gray-500);
    font-family: var(--nibrass-font-family-arabic);
}

/* ===== RTL SPECIFIC STYLES ===== */
html[dir="rtl"] .nibrass-card-icon {
    margin-right: 0;
    margin-left: var(--nibrass-spacing-md);
}

html[dir="rtl"] .icon-left {
    margin-right: 0;
    margin-left: var(--nibrass-spacing-sm);
}

html[dir="rtl"] .icon-right {
    margin-left: 0;
    margin-right: var(--nibrass-spacing-sm);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .nibrass-header {
        padding: var(--nibrass-spacing-xl) 0;
    }
    
    .nibrass-header h1 {
        font-size: var(--nibrass-font-size-3xl);
    }
    
    .nibrass-card {
        padding: var(--nibrass-spacing-lg);
        margin-bottom: var(--nibrass-spacing-lg);
    }
    
    .nibrass-btn {
        width: 100%;
        justify-content: center;
    }
    
    .nibrass-card-header {
        flex-direction: column;
        text-align: center;
    }
    
    .nibrass-card-icon {
        margin: 0 0 var(--nibrass-spacing-md) 0;
    }
}

@media (max-width: 480px) {
    .nibrass-header h1 {
        font-size: var(--nibrass-font-size-2xl);
    }
    
    .nibrass-card {
        padding: var(--nibrass-spacing-md);
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--nibrass-spacing-xs); }
.mb-2 { margin-bottom: var(--nibrass-spacing-sm); }
.mb-3 { margin-bottom: var(--nibrass-spacing-md); }
.mb-4 { margin-bottom: var(--nibrass-spacing-lg); }
.mb-5 { margin-bottom: var(--nibrass-spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--nibrass-spacing-xs); }
.mt-2 { margin-top: var(--nibrass-spacing-sm); }
.mt-3 { margin-top: var(--nibrass-spacing-md); }
.mt-4 { margin-top: var(--nibrass-spacing-lg); }
.mt-5 { margin-top: var(--nibrass-spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--nibrass-spacing-xs); }
.p-2 { padding: var(--nibrass-spacing-sm); }
.p-3 { padding: var(--nibrass-spacing-md); }
.p-4 { padding: var(--nibrass-spacing-lg); }
.p-5 { padding: var(--nibrass-spacing-xl); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

/* Icon visibility fix */
.fas, .far, .fab, .fal {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}
