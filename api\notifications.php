<?php
/**
 * Real-time Notifications API
 * Server-Sent Events endpoint for live notifications
 */

require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    http_response_code(401);
    exit('Unauthorized');
}

$user = current_user();

// إعداد headers للـ Server-Sent Events
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// منع timeout
set_time_limit(0);
ignore_user_abort(false);

// دالة لإرسال البيانات
function sendSSE($id, $event, $data) {
    echo "id: $id\n";
    echo "event: $event\n";
    echo "data: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n\n";
    ob_flush();
    flush();
}

// دالة لجلب الإشعارات الجديدة
function getNewNotifications($user_id, $last_check = null) {
    global $db;
    
    try {
        $query = "SELECT * FROM notifications 
                  WHERE user_id = :user_id 
                  AND is_read = 0";
        
        if ($last_check) {
            $query .= " AND created_at > :last_check";
        }
        
        $query .= " ORDER BY created_at DESC LIMIT 10";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        
        if ($last_check) {
            $stmt->bindParam(':last_check', $last_check);
        }
        
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (PDOException $e) {
        error_log("Notification fetch error: " . $e->getMessage());
        return [];
    }
}

// دالة لجلب عدد الإشعارات غير المقروءة
function getUnreadCount($user_id) {
    global $db;
    
    try {
        $query = "SELECT COUNT(*) as count FROM notifications 
                  WHERE user_id = :user_id AND is_read = 0";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)$result['count'];
        
    } catch (PDOException $e) {
        error_log("Unread count error: " . $e->getMessage());
        return 0;
    }
}

// دالة لإنشاء إشعار تجريبي (للاختبار)
function createTestNotification($user_id) {
    global $db;
    
    $test_messages = [
        'مرحباً! لديك نشاط جديد متاح',
        'تم إضافة قصة جديدة لمكتبتك',
        'حان وقت مراجعة دروسك',
        'لديك إنجاز جديد! تهانينا',
        'تذكير: لا تنس حل الواجبات'
    ];
    
    $message = $test_messages[array_rand($test_messages)];
    
    try {
        $query = "INSERT INTO notifications (user_id, title, message, type) 
                  VALUES (:user_id, 'إشعار جديد', :message, 'info')";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':message', $message);
        $stmt->execute();
        
        return $db->lastInsertId();
    } catch (PDOException $e) {
        error_log("Test notification error: " . $e->getMessage());
        return false;
    }
}

// متغيرات التحكم
$last_check = null;
$event_id = 1;
$heartbeat_interval = 30; // ثانية
$last_heartbeat = time();

// إرسال إشعار الاتصال الأولي
sendSSE($event_id++, 'connected', [
    'message' => 'تم الاتصال بنجاح',
    'user_id' => $user['id'],
    'timestamp' => date('Y-m-d H:i:s')
]);

// إرسال عدد الإشعارات غير المقروءة
$unread_count = getUnreadCount($user['id']);
sendSSE($event_id++, 'unread_count', [
    'count' => $unread_count
]);

// الحلقة الرئيسية
while (true) {
    // التحقق من قطع الاتصال
    if (connection_aborted()) {
        break;
    }
    
    // جلب الإشعارات الجديدة
    $notifications = getNewNotifications($user['id'], $last_check);
    
    if (!empty($notifications)) {
        foreach ($notifications as $notification) {
            sendSSE($event_id++, 'notification', [
                'id' => $notification['id'],
                'title' => $notification['title'],
                'message' => $notification['message'],
                'type' => $notification['type'],
                'action_url' => $notification['action_url'],
                'created_at' => $notification['created_at'],
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
        
        // تحديث عدد الإشعارات غير المقروءة
        $unread_count = getUnreadCount($user['id']);
        sendSSE($event_id++, 'unread_count', [
            'count' => $unread_count
        ]);
        
        $last_check = date('Y-m-d H:i:s');
    }
    
    // إرسال heartbeat كل 30 ثانية
    if (time() - $last_heartbeat >= $heartbeat_interval) {
        sendSSE($event_id++, 'heartbeat', [
            'timestamp' => date('Y-m-d H:i:s'),
            'server_time' => time()
        ]);
        $last_heartbeat = time();
    }
    
    // إنشاء إشعار تجريبي كل دقيقتين (للاختبار فقط)
    if (ENVIRONMENT === 'development' && rand(1, 120) === 1) {
        $test_id = createTestNotification($user['id']);
        if ($test_id) {
            sendSSE($event_id++, 'test_notification', [
                'message' => 'تم إنشاء إشعار تجريبي',
                'notification_id' => $test_id
            ]);
        }
    }
    
    // انتظار ثانية واحدة قبل التحقق مرة أخرى
    sleep(1);
}
?>
