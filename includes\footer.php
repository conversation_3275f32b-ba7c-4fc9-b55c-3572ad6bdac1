    <!-- التذييل المحسن -->
    <footer class="footer scroll-animate">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h5><i class="fas fa-star"></i> منصة نبراس</h5>
                    <p class="footer-description">منصة تعليمية متخصصة في دعم الأطفال ذوي اضطراب طيف التوحد وأسرهم</p>
                    <div class="footer-stats">
                        <span class="stat-item">
                            <i class="fas fa-users"></i> 500+ مستفيد
                        </span>
                        <span class="stat-item">
                            <i class="fas fa-book"></i> 100+ مورد
                        </span>
                    </div>
                </div>
                <div class="footer-section">
                    <h5><i class="fas fa-link"></i> روابط سريعة</h5>
                    <ul class="footer-links">
                        <li><a href="<?php echo get_url('index.php'); ?>"><i class="fas fa-home"></i> الرئيسية</a></li>
                        <li><a href="<?php echo get_url('about.php'); ?>"><i class="fas fa-info-circle"></i> من نحن</a></li>
                        <li><a href="<?php echo get_url('library.php'); ?>"><i class="fas fa-book"></i> المكتبة</a></li>
                        <li><a href="<?php echo get_url('workshops.php'); ?>"><i class="fas fa-chalkboard-teacher"></i> الورشات</a></li>
                        <li><a href="<?php echo get_url('creativity.php'); ?>"><i class="fas fa-palette"></i> الإبداع</a></li>
                        <li><a href="<?php echo get_url('contact.php'); ?>"><i class="fas fa-envelope"></i> اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h5><i class="fas fa-phone"></i> تواصل معنا</h5>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-phone"></i> +213 XX XXX XXX</p>
                        <p><i class="fas fa-map-marker-alt"></i> الجزائر، الجزائر</p>
                    </div>
                    <div class="social-links">
                        <a href="#" class="social-link facebook" title="فيسبوك">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-link twitter" title="تويتر">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link instagram" title="إنستغرام">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-link youtube" title="يوتيوب">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="footer-copyright">&copy; <?php echo date('Y'); ?> منصة نبراس. جميع الحقوق محفوظة.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="footer-made">صُنع بـ <i class="fas fa-heart text-danger"></i> في الجزائر</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="<?php echo get_asset_url('js/main.js'); ?>"></script>
    
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js_file): ?>
            <script src="<?php echo $js_file; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Real-time Notifications -->
    <?php if (is_logged_in()): ?>
        <script src="<?php echo get_url('js/notifications.js'); ?>"></script>
    <?php endif; ?>

    <!-- Page-specific JavaScript -->
    <?php if (isset($page_js)): ?>
        <script>
            <?php echo $page_js; ?>
        </script>
    <?php endif; ?>
</body>
</html>
