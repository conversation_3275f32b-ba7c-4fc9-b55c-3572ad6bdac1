<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كأخصائي
require_specialist();

$user = current_user();

// جلب جميع المرضى المتابعين
try {
    $patients_query = "SELECT DISTINCT s.*, p.name as parent_name, p.phone as parent_phone, p.email as parent_email,
                              COUNT(c.id) as consultation_count,
                              MAX(c.created_at) as last_consultation,
                              COUNT(a.id) as appointment_count,
                              MAX(a.appointment_date) as last_appointment
                       FROM students s
                       LEFT JOIN users p ON s.parent_id = p.id
                       LEFT JOIN consultations c ON s.id = c.student_id AND c.specialist_id = :specialist_id
                       LEFT JOIN appointments a ON s.id = a.student_id AND a.specialist_id = :specialist_id
                       WHERE c.id IS NOT NULL OR a.id IS NOT NULL
                       GROUP BY s.id
                       ORDER BY last_consultation DESC, last_appointment DESC";
    $patients_stmt = $db->prepare($patients_query);
    $patients_stmt->bindParam(':specialist_id', $user['id']);
    $patients_stmt->execute();
    $patients = $patients_stmt->fetchAll();
} catch (PDOException $e) {
    $patients = [];
    $error_message = "حدث خطأ في جلب بيانات المرضى";
}

// إعداد متغيرات الصفحة
$page_title = 'ملفات المرضى';
$page_description = 'إدارة ومتابعة ملفات المرضى';

// تضمين الهيدر
include '../includes/header.php';
?>

<style>
.patients-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.patients-header h1,
.patients-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

/* تأكيد ظهور الأيقونات */
.patients-header i,
.patient-card i,
.patient-avatar i,
.btn i,
.alert i,
.stat-item i {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

.patient-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.patient-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6f42c1, #20c997);
}

.patient-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(111, 66, 193, 0.15);
    border-color: rgba(111, 66, 193, 0.3);
}

.patient-avatar {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-left: 1.5rem;
    box-shadow: 0 10px 25px rgba(74, 144, 226, 0.3);
}

.patient-info h4 {
    color: #2c3e50;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.patient-info p {
    color: #7f8c8d;
    margin: 0;
    font-size: 1rem;
}

.patient-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: #e3f2fd;
    transform: translateY(-2px);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #4a90e2;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: #666;
    font-weight: 500;
}

.progress-indicator {
    margin: 1rem 0;
}

.progress-bar-custom {
    height: 8px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4a90e2, #2c5aa0);
    border-radius: 10px;
    transition: width 0.6s ease;
}

.search-section {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

.search-input {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}



@media (max-width: 768px) {
    .patient-card .row {
        text-align: center;
    }
    
    .patient-avatar {
        margin-left: 0;
        margin-bottom: 1rem;
    }
    
    .patient-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<!-- قسم العنوان -->
<section class="patients-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-users"></i> ملفات المرضى</h1>
            <p class="lead">إدارة ومتابعة ملفات المرضى والأطفال</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark fs-6">
                    <i class="fas fa-child"></i> <?php echo count($patients); ?> مريض
                </span>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- قسم البحث -->
        <div class="search-section">
            <div class="row">
                <div class="col-lg-8">
                    <input type="text" class="form-control search-input" id="searchInput" 
                           placeholder="ابحث عن مريض بالاسم أو العمر أو المستوى...">
                </div>
                <div class="col-lg-4">
                    <button class="btn btn-primary w-100" onclick="searchPatients()">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
        </div>

        <!-- قائمة المرضى -->
        <?php if (!empty($patients)): ?>
            <?php foreach ($patients as $patient): ?>
                <div class="patient-card" data-patient-info="<?php echo htmlspecialchars(strtolower($patient['name'] . ' ' . $patient['age'] . ' ' . $patient['diagnosis_level'])); ?>">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="d-flex align-items-center mb-3">
                                <div class="patient-avatar">
                                    <i class="fas fa-child"></i>
                                </div>
                                <div class="patient-info">
                                    <h4><?php echo htmlspecialchars($patient['name'] ?? 'غير محدد'); ?></h4>
                                    <p>
                                        العمر: <?php echo $patient['age'] ?? 'غير محدد'; ?> سنة |
                                        المستوى: <?php echo $patient['diagnosis_level'] ?? 'غير محدد'; ?>
                                    </p>
                                    <p>
                                        <i class="fas fa-user"></i> ولي الأمر: <?php echo htmlspecialchars($patient['parent_name']); ?>
                                        <?php if ($patient['parent_phone']): ?>
                                        | <i class="fas fa-phone"></i> <?php echo htmlspecialchars($patient['parent_phone']); ?>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="patient-stats">
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo $patient['consultation_count']; ?></div>
                                    <div class="stat-label">الاستشارات</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo $patient['appointment_count']; ?></div>
                                    <div class="stat-label">المواعيد</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo $patient['progress_level'] ?? 0; ?>%</div>
                                    <div class="stat-label">التقدم</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">
                                        <?php 
                                        if ($patient['last_consultation']) {
                                            $days = floor((time() - strtotime($patient['last_consultation'])) / (60 * 60 * 24));
                                            echo $days;
                                        } else {
                                            echo '-';
                                        }
                                        ?>
                                    </div>
                                    <div class="stat-label">أيام منذ آخر استشارة</div>
                                </div>
                            </div>
                            
                            <div class="progress-indicator">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small class="text-muted">مستوى التقدم العام</small>
                                    <small class="text-muted"><?php echo $patient['progress_level'] ?? 0; ?>%</small>
                                </div>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: <?php echo $patient['progress_level'] ?? 0; ?>%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <div class="d-grid gap-2">
                                <a href="patient-profile.php?id=<?php echo $patient['id']; ?>" class="btn btn-primary">
                                    <i class="fas fa-user"></i> الملف الشخصي
                                </a>
                                <a href="patient-history.php?id=<?php echo $patient['id']; ?>" class="btn btn-info">
                                    <i class="fas fa-history"></i> التاريخ الطبي
                                </a>
                                <a href="schedule-appointment.php?student_id=<?php echo $patient['id']; ?>" class="btn btn-warning">
                                    <i class="fas fa-calendar-plus"></i> حجز موعد
                                </a>
                                <a href="patient-reports.php?id=<?php echo $patient['id']; ?>" class="btn btn-success">
                                    <i class="fas fa-chart-bar"></i> التقارير
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد ملفات مرضى</h4>
                <p class="text-muted">ستظهر هنا ملفات المرضى الذين تتابع حالاتهم</p>
            </div>
        <?php endif; ?>

        <!-- إحصائيات سريعة -->
        <?php if (!empty($patients)): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="patient-card">
                    <h4><i class="fas fa-chart-pie"></i> إحصائيات سريعة</h4>
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-value"><?php echo count($patients); ?></div>
                                <div class="stat-label">إجمالي المرضى</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-value"><?php echo array_sum(array_column($patients, 'consultation_count')); ?></div>
                                <div class="stat-label">إجمالي الاستشارات</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-value"><?php echo array_sum(array_column($patients, 'appointment_count')); ?></div>
                                <div class="stat-label">إجمالي المواعيد</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-value">
                                    <?php 
                                    $avg_progress = count($patients) > 0 ? 
                                        round(array_sum(array_column($patients, 'progress_level')) / count($patients)) : 0;
                                    echo $avg_progress;
                                    ?>%
                                </div>
                                <div class="stat-label">متوسط التقدم</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    function searchPatients() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const patientCards = document.querySelectorAll('.patient-card');
        
        patientCards.forEach(card => {
            const patientInfo = card.getAttribute('data-patient-info');
            
            if (patientInfo.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
    
    // البحث عند الكتابة
    document.getElementById('searchInput').addEventListener('input', searchPatients);
    
    // تحريك أشرطة التقدم
    document.addEventListener('DOMContentLoaded', function() {
        const progressBars = document.querySelectorAll('.progress-fill');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
