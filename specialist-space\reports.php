<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كأخصائي
require_specialist();

$user = current_user();

// جلب الإحصائيات الشاملة
try {
    // إحصائيات الاستشارات
    $consultations_stats_query = "SELECT 
                                    COUNT(*) as total_consultations,
                                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_consultations,
                                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_consultations,
                                    COUNT(CASE WHEN urgency_level = 'urgent' THEN 1 END) as urgent_consultations,
                                    AVG(CASE WHEN status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) * 100 as completion_rate
                                  FROM consultations 
                                  WHERE specialist_id = :specialist_id";
    $consultations_stats_stmt = $db->prepare($consultations_stats_query);
    $consultations_stats_stmt->bindParam(':specialist_id', $user['id']);
    $consultations_stats_stmt->execute();
    $consultations_stats = $consultations_stats_stmt->fetch();

    // إحصائيات المواعيد
    $appointments_stats_query = "SELECT 
                                   COUNT(*) as total_appointments,
                                   COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_appointments,
                                   COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_appointments,
                                   COUNT(CASE WHEN appointment_date >= NOW() THEN 1 END) as upcoming_appointments
                                 FROM appointments 
                                 WHERE specialist_id = :specialist_id";
    $appointments_stats_stmt = $db->prepare($appointments_stats_query);
    $appointments_stats_stmt->bindParam(':specialist_id', $user['id']);
    $appointments_stats_stmt->execute();
    $appointments_stats = $appointments_stats_stmt->fetch();

    // إحصائيات المرضى
    $patients_stats_query = "SELECT 
                               COUNT(DISTINCT student_id) as total_patients,
                               AVG(CASE WHEN s.progress_level IS NOT NULL THEN s.progress_level ELSE 0 END) as avg_progress
                             FROM consultations c
                             LEFT JOIN students s ON c.student_id = s.id
                             WHERE c.specialist_id = :specialist_id";
    $patients_stats_stmt = $db->prepare($patients_stats_query);
    $patients_stats_stmt->bindParam(':specialist_id', $user['id']);
    $patients_stats_stmt->execute();
    $patients_stats = $patients_stats_stmt->fetch();

    // الاستشارات الشهرية (آخر 6 أشهر)
    $monthly_consultations_query = "SELECT 
                                      DATE_FORMAT(created_at, '%Y-%m') as month,
                                      COUNT(*) as count
                                    FROM consultations 
                                    WHERE specialist_id = :specialist_id 
                                    AND created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
                                    GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                                    ORDER BY month DESC";
    $monthly_consultations_stmt = $db->prepare($monthly_consultations_query);
    $monthly_consultations_stmt->bindParam(':specialist_id', $user['id']);
    $monthly_consultations_stmt->execute();
    $monthly_consultations = $monthly_consultations_stmt->fetchAll();

} catch (PDOException $e) {
    $consultations_stats = ['total_consultations' => 0, 'completed_consultations' => 0, 'pending_consultations' => 0, 'urgent_consultations' => 0, 'completion_rate' => 0];
    $appointments_stats = ['total_appointments' => 0, 'completed_appointments' => 0, 'cancelled_appointments' => 0, 'upcoming_appointments' => 0];
    $patients_stats = ['total_patients' => 0, 'avg_progress' => 0];
    $monthly_consultations = [];
}

// إعداد متغيرات الصفحة
$page_title = 'التقارير والإحصائيات';
$page_description = 'تقارير شاملة عن الأداء والإحصائيات';

// تضمين الهيدر
include '../includes/header.php';
?>

<style>
.reports-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.reports-header h1,
.reports-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

/* تأكيد ظهور الأيقونات */
.reports-header i,
.report-card i,
.metric-card i,
.btn i,
.btn-export i,
.list-unstyled i {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

.report-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: rgba(74, 144, 226, 0.3);
}

.metric-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border-top: 4px solid #4a90e2;
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.12);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.metric-label {
    color: #7f8c8d;
    font-size: 1rem;
    font-weight: 500;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 2rem 0;
}

.progress-ring {
    width: 120px;
    height: 120px;
    margin: 0 auto;
}

.progress-ring-circle {
    stroke: #e9ecef;
    stroke-width: 8;
    fill: transparent;
    r: 52;
    cx: 60;
    cy: 60;
}

.progress-ring-progress {
    stroke: #4a90e2;
    stroke-width: 8;
    fill: transparent;
    r: 52;
    cx: 60;
    cy: 60;
    stroke-dasharray: 326.73;
    stroke-dashoffset: 326.73;
    transform: rotate(-90deg);
    transform-origin: 60px 60px;
    transition: stroke-dashoffset 0.6s ease;
}

.export-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn-export {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-export:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
    color: white;
}
</style>

<!-- قسم العنوان -->
<section class="reports-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h1>
            <p class="lead">تقارير شاملة عن أدائك وإحصائياتك المهنية</p>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <!-- أزرار التصدير -->
        <div class="export-buttons">
            <button class="btn-export" onclick="exportToPDF()">
                <i class="fas fa-file-pdf"></i> تصدير PDF
            </button>
            <button class="btn-export" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </button>
            <button class="btn-export" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>

        <!-- الإحصائيات الرئيسية -->
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="metric-card">
                    <div class="metric-value"><?php echo $consultations_stats['total_consultations']; ?></div>
                    <div class="metric-label">إجمالي الاستشارات</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="metric-card">
                    <div class="metric-value"><?php echo $appointments_stats['total_appointments']; ?></div>
                    <div class="metric-label">إجمالي المواعيد</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="metric-card">
                    <div class="metric-value"><?php echo $patients_stats['total_patients']; ?></div>
                    <div class="metric-label">إجمالي المرضى</div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="metric-card">
                    <div class="metric-value"><?php echo round($patients_stats['avg_progress']); ?>%</div>
                    <div class="metric-label">متوسط التقدم</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- تقرير الاستشارات -->
            <div class="col-lg-6">
                <div class="report-card">
                    <h4><i class="fas fa-comments"></i> تقرير الاستشارات</h4>

                    <div class="row text-center mt-4">
                        <div class="col-4">
                            <div class="metric-value text-success"><?php echo $consultations_stats['completed_consultations']; ?></div>
                            <div class="metric-label">مكتملة</div>
                        </div>

                        <div class="col-4">
                            <div class="metric-value text-warning"><?php echo $consultations_stats['pending_consultations']; ?></div>
                            <div class="metric-label">معلقة</div>
                        </div>

                        <div class="col-4">
                            <div class="metric-value text-danger"><?php echo $consultations_stats['urgent_consultations']; ?></div>
                            <div class="metric-label">عاجلة</div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>معدل الإكمال</span>
                            <span><?php echo round($consultations_stats['completion_rate']); ?>%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: <?php echo round($consultations_stats['completion_rate']); ?>%; background: linear-gradient(90deg, #4a90e2, #2c5aa0);"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تقرير المواعيد -->
            <div class="col-lg-6">
                <div class="report-card">
                    <h4><i class="fas fa-calendar-check"></i> تقرير المواعيد</h4>
                    
                    <div class="row text-center mt-4">
                        <div class="col-4">
                            <div class="metric-value text-success"><?php echo $appointments_stats['completed_appointments']; ?></div>
                            <div class="metric-label">مكتملة</div>
                        </div>
                        
                        <div class="col-4">
                            <div class="metric-value text-primary"><?php echo $appointments_stats['upcoming_appointments']; ?></div>
                            <div class="metric-label">قادمة</div>
                        </div>
                        
                        <div class="col-4">
                            <div class="metric-value text-danger"><?php echo $appointments_stats['cancelled_appointments']; ?></div>
                            <div class="metric-label">ملغية</div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>معدل الحضور</span>
                            <span><?php echo $appointments_stats['total_appointments'] > 0 ? round(($appointments_stats['completed_appointments'] / $appointments_stats['total_appointments']) * 100) : 0; ?>%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: <?php echo $appointments_stats['total_appointments'] > 0 ? round(($appointments_stats['completed_appointments'] / $appointments_stats['total_appointments']) * 100) : 0; ?>%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الاستشارات الشهرية -->
        <div class="row">
            <div class="col-12">
                <div class="report-card">
                    <h4><i class="fas fa-chart-line"></i> الاستشارات الشهرية (آخر 6 أشهر)</h4>
                    
                    <?php if (!empty($monthly_consultations)): ?>
                    <div class="chart-container">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                    
                    <div class="row text-center">
                        <?php foreach (array_reverse($monthly_consultations) as $month_data): ?>
                        <div class="col-md-2">
                            <div class="metric-value text-primary"><?php echo $month_data['count']; ?></div>
                            <div class="metric-label"><?php echo date('M Y', strtotime($month_data['month'] . '-01')); ?></div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد بيانات كافية</h5>
                        <p class="text-muted">ستظهر هنا الإحصائيات الشهرية عند توفر البيانات</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- ملخص الأداء -->
        <div class="row">
            <div class="col-12">
                <div class="report-card">
                    <h4><i class="fas fa-trophy"></i> ملخص الأداء</h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>نقاط القوة:</h6>
                            <ul class="list-unstyled">
                                <?php if ($consultations_stats['completion_rate'] > 80): ?>
                                <li><i class="fas fa-check text-success"></i> معدل إكمال عالي للاستشارات</li>
                                <?php endif; ?>
                                
                                <?php if ($appointments_stats['total_appointments'] > 0 && ($appointments_stats['cancelled_appointments'] / $appointments_stats['total_appointments']) < 0.1): ?>
                                <li><i class="fas fa-check text-success"></i> معدل إلغاء منخفض للمواعيد</li>
                                <?php endif; ?>
                                
                                <?php if ($patients_stats['avg_progress'] > 70): ?>
                                <li><i class="fas fa-check text-success"></i> تقدم جيد للمرضى</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>مجالات التحسين:</h6>
                            <ul class="list-unstyled">
                                <?php if ($consultations_stats['pending_consultations'] > 5): ?>
                                <li><i class="fas fa-exclamation text-warning"></i> عدد كبير من الاستشارات المعلقة</li>
                                <?php endif; ?>
                                
                                <?php if ($consultations_stats['completion_rate'] < 60): ?>
                                <li><i class="fas fa-exclamation text-warning"></i> يمكن تحسين معدل إكمال الاستشارات</li>
                                <?php endif; ?>
                                
                                <?php if ($patients_stats['avg_progress'] < 50): ?>
                                <li><i class="fas fa-exclamation text-warning"></i> يمكن تحسين متوسط تقدم المرضى</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    function exportToPDF() {
        NibrassHelpers.showAlert('سيتم تطوير ميزة تصدير PDF قريباً', 'info');
    }
    
    function exportToExcel() {
        NibrassHelpers.showAlert('سيتم تطوير ميزة تصدير Excel قريباً', 'info');
    }
    
    // رسم المخطط الشهري
    document.addEventListener('DOMContentLoaded', function() {
        const monthlyData = " . json_encode(array_reverse($monthly_consultations)) . ";
        
        if (monthlyData.length > 0) {
            const ctx = document.getElementById('monthlyChart');
            if (ctx) {
                // يمكن إضافة مكتبة Chart.js هنا لرسم المخططات
                console.log('Monthly data:', monthlyData);
            }
        }
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
