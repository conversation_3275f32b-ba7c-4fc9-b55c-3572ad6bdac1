<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كولي أمر
require_parent();

$user = current_user();
$students = get_parent_students($user['id']);

// جلب تقارير التقدم للأطفال
$progress_reports = [];
foreach ($students as $student) {
    // جلب الأنشطة المكتملة
    $activities_query = "SELECT sa.*, sa.activity_title, sa.score, sa.completed_at
                        FROM student_activities sa
                        WHERE sa.student_id = :student_id
                        ORDER BY sa.completed_at DESC";
    $activities_stmt = $db->prepare($activities_query);
    $activities_stmt->bindParam(':student_id', $student['id']);
    $activities_stmt->execute();
    $activities = $activities_stmt->fetchAll();

    // حساب الإحصائيات
    $total_activities = count($activities);
    $avg_score = $total_activities > 0 ? array_sum(array_column($activities, 'score')) / $total_activities : 0;
    $total_time = $total_activities * 15; // تقدير 15 دقيقة لكل نشاط
    
    // جلب آخر تقييم من الأخصائي (إذا كان الجدول موجود)
    $latest_assessment = null;
    try {
        $assessment_query = "SELECT * FROM student_assessments
                            WHERE student_id = :student_id
                            ORDER BY created_at DESC LIMIT 1";
        $assessment_stmt = $db->prepare($assessment_query);
        $assessment_stmt->bindParam(':student_id', $student['id']);
        $assessment_stmt->execute();
        $latest_assessment = $assessment_stmt->fetch();
    } catch (PDOException $e) {
        // الجدول غير موجود، سنتجاهل هذا القسم
        $latest_assessment = null;
    }
    
    $progress_reports[] = [
        'student' => $student,
        'activities' => $activities,
        'stats' => [
            'total_activities' => $total_activities,
            'avg_score' => round($avg_score, 1),
            'total_time' => $total_time,
            'progress_level' => $student['progress_level'] ?? 0
        ],
        'latest_assessment' => $latest_assessment
    ];
}

// إعداد متغيرات الصفحة
$page_title = 'تقارير التقدم';
$page_description = 'متابعة تقدم أطفالكم وإنجازاتهم التعليمية';

// تضمين الهيدر
include '../includes/header.php';
?>

<style>
.progress-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.progress-header h1,
.progress-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.student-progress-card {
    background: white;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.student-progress-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #28a745, #20c997);
}

.student-progress-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(40, 167, 69, 0.15);
}

.student-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid #f8f9fa;
}

.student-avatar {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    background: linear-gradient(135deg, #28a745, #20c997);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-left: 1.5rem;
    box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
}

.student-info h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.student-info p {
    color: #7f8c8d;
    margin: 0;
    font-size: 1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-item {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: #e3f2fd;
    transform: translateY(-3px);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
}

.progress-section {
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.section-title i {
    margin-left: 0.5rem;
    color: #28a745;
}

.progress-bar-container {
    background: #e9ecef;
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-bar-custom {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 10px;
    transition: width 0.6s ease;
    position: relative;
}

.progress-bar-custom::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.activity-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #e3f2fd;
    transform: translateX(-5px);
}

.activity-info {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: #333;
    margin: 0 0 0.25rem 0;
}

.activity-date {
    color: #666;
    font-size: 0.8rem;
}

.activity-score {
    background: #28a745;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.assessment-card {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border-radius: 15px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.assessment-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.assessment-title i {
    margin-left: 0.5rem;
    color: #6f42c1;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
}

.empty-state i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@media (max-width: 768px) {
    .student-header {
        flex-direction: column;
        text-align: center;
    }
    
    .student-avatar {
        margin-left: 0;
        margin-bottom: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .activity-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
}
</style>

<!-- قسم العنوان -->
<section class="progress-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-chart-line"></i> تقارير التقدم</h1>
            <p class="lead">متابعة تقدم أطفالكم وإنجازاتهم التعليمية</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-child"></i> <?php echo count($students); ?> طفل
                </span>
                <span class="badge bg-light text-dark fs-6">
                    <i class="fas fa-trophy"></i> تقارير شاملة
                </span>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <?php if (!empty($progress_reports)): ?>
            <?php foreach ($progress_reports as $report): ?>
                <div class="student-progress-card">
                    <!-- معلومات الطالب -->
                    <div class="student-header">
                        <div class="student-avatar">
                            <i class="fas fa-child"></i>
                        </div>
                        <div class="student-info">
                            <h3><?php echo htmlspecialchars($report['student']['name'] ?? $report['student']['student_name']); ?></h3>
                            <p>
                                العمر: <?php echo $report['student']['age'] ?? 'غير محدد'; ?> سنة |
                                المستوى: <?php echo $report['student']['diagnosis_level'] ?? 'غير محدد'; ?>
                            </p>
                        </div>
                    </div>

                    <!-- الإحصائيات -->
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $report['stats']['total_activities']; ?></div>
                            <div class="stat-label">الأنشطة المكتملة</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $report['stats']['avg_score']; ?>%</div>
                            <div class="stat-label">متوسط النتائج</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?php echo round($report['stats']['total_time'] / 60); ?></div>
                            <div class="stat-label">دقائق التعلم</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?php echo $report['stats']['progress_level']; ?>%</div>
                            <div class="stat-label">مستوى التقدم</div>
                        </div>
                    </div>

                    <!-- شريط التقدم العام -->
                    <div class="progress-section">
                        <h4 class="section-title">
                            <i class="fas fa-chart-line"></i>
                            التقدم العام
                        </h4>
                        <div class="progress-bar-container">
                            <div class="progress-bar-custom" style="width: <?php echo $report['stats']['progress_level']; ?>%"></div>
                        </div>
                        <p class="text-muted">مستوى التقدم: <?php echo $report['stats']['progress_level']; ?>%</p>
                    </div>

                    <!-- الأنشطة الحديثة -->
                    <div class="progress-section">
                        <h4 class="section-title">
                            <i class="fas fa-tasks"></i>
                            الأنشطة الحديثة
                        </h4>
                        <?php if (!empty($report['activities'])): ?>
                            <?php foreach (array_slice($report['activities'], 0, 5) as $activity): ?>
                                <div class="activity-item">
                                    <div class="activity-info">
                                        <div class="activity-title">
                                            <?php echo htmlspecialchars($activity['activity_title'] ?? 'نشاط تعليمي'); ?>
                                        </div>
                                        <div class="activity-date">
                                            <?php echo date('d/m/Y H:i', strtotime($activity['completed_at'])); ?>
                                        </div>
                                    </div>
                                    <div class="activity-score">
                                        <?php echo $activity['score']; ?>%
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-muted">لا توجد أنشطة مكتملة بعد</p>
                        <?php endif; ?>
                    </div>

                    <!-- آخر تقييم من الأخصائي -->
                    <?php if ($report['latest_assessment']): ?>
                        <div class="assessment-card">
                            <h4 class="assessment-title">
                                <i class="fas fa-user-md"></i>
                                آخر تقييم من الأخصائي
                            </h4>
                            <p><strong>التاريخ:</strong> <?php echo date('d/m/Y', strtotime($report['latest_assessment']['created_at'])); ?></p>
                            <p><strong>الملاحظات:</strong> <?php echo htmlspecialchars($report['latest_assessment']['notes']); ?></p>
                            <p><strong>التوصيات:</strong> <?php echo htmlspecialchars($report['latest_assessment']['recommendations']); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-chart-line"></i>
                <h4>لا توجد تقارير متاحة</h4>
                <p>ابدأ بإضافة طفلك الأول لمتابعة تقدمه</p>
                <a href="add_student.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة طفل جديد
                </a>
            </div>
        <?php endif; ?>

        <!-- أزرار الإجراءات -->
        <div class="text-center mt-4">
            <button class="btn btn-outline-primary me-2" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة التقارير
            </button>
            <button class="btn btn-outline-secondary" onclick="exportReports()">
                <i class="fas fa-download"></i> تصدير PDF
            </button>
        </div>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    function exportReports() {
        NibrassHelpers.showAlert('سيتم تطوير ميزة التصدير قريباً', 'info');
    }
    
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // تحريك أشرطة التقدم
        const progressBars = document.querySelectorAll('.progress-bar-custom');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });
        
        // إضافة تأثيرات التمرير
        const cards = document.querySelectorAll('.student-progress-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
