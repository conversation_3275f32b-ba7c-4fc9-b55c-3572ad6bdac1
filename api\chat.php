<?php
/**
 * API للشات مع الذكاء الاصطناعي
 * AI Chat API using OpenAI
 */

require_once '../config.php';
require_once '../db.php';

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=utf-8');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['message']) || empty(trim($input['message']))) {
    echo json_encode(['success' => false, 'error' => 'Message is required']);
    exit;
}

$user_message = trim($input['message']);
$user_id = $input['user_id'] ?? null;

// إعداد السياق المتخصص في التوحد
$system_prompt = "أنت مساعد ذكي متخصص في اضطراب طيف التوحد، تعمل في منصة 'نبراس' التعليمية. 

مهامك:
1. تقديم معلومات دقيقة وموثوقة حول التوحد
2. تقديم نصائح عملية للأولياء والأخصائيين
3. اقتراح أنشطة تعليمية مناسبة
4. الإجابة بطريقة مفهومة ومتعاطفة
5. توجيه المستخدمين لخدمات المنصة عند الحاجة

قواعد مهمة:
- استخدم اللغة العربية دائماً
- كن متعاطفاً ومتفهماً
- قدم معلومات علمية موثوقة
- لا تقدم تشخيصات طبية
- وجه للاستشارة المتخصصة عند الحاجة
- اذكر خدمات منصة نبراس عند المناسبة";

try {
    // إعدادات OpenRouter.ai API
    $openrouter_api_key = '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';
    $openrouter_api_url = 'https://openrouter.ai/api/v1/chat/completions';
    $ai_model = 'deepseek/deepseek-chat-v3-0324:free';

    // إعداد البيانات لإرسالها إلى OpenRouter
    $data = [
        'model' => $ai_model,
        'messages' => [
            [
                'role' => 'system',
                'content' => $system_prompt
            ],
            [
                'role' => 'user',
                'content' => $user_message
            ]
        ],
        'max_tokens' => 500,
        'temperature' => 0.7,
        'top_p' => 1,
        'frequency_penalty' => 0,
        'presence_penalty' => 0
    ];

    // إعداد cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $openrouter_api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $openrouter_api_key,
        'HTTP-Referer: https://nibrass.local',
        'X-Title: Nibrass Educational Platform'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    if ($curl_error) {
        throw new Exception('cURL Error: ' . $curl_error);
    }

    if ($http_code !== 200) {
        throw new Exception('OpenRouter API Error: HTTP ' . $http_code);
    }

    $response_data = json_decode($response, true);

    if (!$response_data || !isset($response_data['choices'][0]['message']['content'])) {
        throw new Exception('Invalid response from OpenRouter');
    }

    $ai_response = $response_data['choices'][0]['message']['content'];

    // حفظ المحادثة في قاعدة البيانات (اختياري)
    try {
        $save_query = "INSERT INTO chat_history (user_id, user_message, ai_response, created_at) 
                       VALUES (:user_id, :user_message, :ai_response, NOW())";
        $save_stmt = $db->prepare($save_query);
        $save_stmt->bindParam(':user_id', $user_id);
        $save_stmt->bindParam(':user_message', $user_message);
        $save_stmt->bindParam(':ai_response', $ai_response);
        $save_stmt->execute();
    } catch (Exception $e) {
        // فشل في حفظ المحادثة - لكن لا نريد إيقاف الرد
        error_log('Failed to save chat history: ' . $e->getMessage());
    }

    // إرسال الرد
    echo json_encode([
        'success' => true,
        'response' => $ai_response
    ]);

} catch (Exception $e) {
    // في حالة فشل الاتصال بـ OpenAI، نقدم ردود جاهزة
    $fallback_responses = [
        'ما هو اضطراب طيف التوحد؟' => 'اضطراب طيف التوحد هو حالة نمائية تؤثر على التواصل والتفاعل الاجتماعي. يتميز بأنماط سلوكية متكررة واهتمامات محدودة. كل طفل مصاب بالتوحد فريد من نوعه وله نقاط قوة وتحديات مختلفة.',
        
        'كيف أتعامل مع نوبات الغضب؟' => 'للتعامل مع نوبات الغضب: 1) حافظ على هدوئك 2) تجنب المحفزات المعروفة 3) استخدم تقنيات التهدئة 4) وضع روتين ثابت 5) تعلم إشارات التحذير المبكرة. يمكنك طلب استشارة من أخصائيينا في منصة نبراس للحصول على نصائح مخصصة.',
        
        'ما هي أفضل الأنشطة التعليمية؟' => 'الأنشطة التعليمية المفيدة تشمل: الألعاب التفاعلية، الأنشطة الحسية، القصص المصورة، الأنشطة الفنية، والألعاب التعليمية. تصفح مكتبة نبراس للحصول على المزيد من الأنشطة المتخصصة.',
        
        'كيف أطور مهارات التواصل؟' => 'لتطوير مهارات التواصل: 1) استخدم لغة بسيطة وواضحة 2) امنح وقتاً كافياً للاستجابة 3) استخدم الإشارات البصرية 4) شجع على التواصل بأي شكل 5) اجعل التواصل ممتعاً. انضم لورشاتنا التدريبية لتعلم المزيد.',
        
        'ما هي علامات التوحد المبكرة؟' => 'العلامات المبكرة قد تشمل: تأخر في الكلام، تجنب التواصل البصري، صعوبة في التفاعل الاجتماعي، السلوكيات المتكررة، والحساسية للأصوات أو الأضواء. مهم جداً استشارة أخصائي للتقييم المناسب.'
    ];
    
    // البحث عن رد مناسب
    $response_text = 'شكراً لسؤالك. للأسف، لا أستطيع الإجابة بشكل مفصل الآن بسبب مشكلة تقنية. يمكنك:

1. طلب استشارة من أخصائيينا المتخصصين
2. تصفح مكتبة المنصة للحصول على معلومات مفيدة  
3. الانضمام لورشاتنا التدريبية
4. التواصل معنا مباشرة عبر صفحة "اتصل بنا"

نحن هنا لدعمك في رحلة تربية طفلك.';
    
    foreach ($fallback_responses as $question => $answer) {
        if (strpos($user_message, $question) !== false || 
            similar_text($user_message, $question) > strlen($question) * 0.6) {
            $response_text = $answer;
            break;
        }
    }
    
    echo json_encode([
        'success' => true,
        'response' => $response_text
    ]);
    
    // تسجيل الخطأ
    error_log('OpenRouter API Error: ' . $e->getMessage());
}
?>
