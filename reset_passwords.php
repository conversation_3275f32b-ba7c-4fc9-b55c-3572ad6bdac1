<?php
/**
 * سكريبت إعادة تعيين كلمات السر وإنشاء ملف بيانات المستخدمين
 * Reset Passwords and Generate Users Credentials File
 */

require_once 'config.php';
require_once 'db.php';

// كلمات السر الافتراضية الجديدة
$default_passwords = [
    'admin' => 'admin2024',
    'parent' => 'parent2024', 
    'specialist' => 'specialist2024',
    'student' => 'student2024'
];

// مصفوفة لحفظ بيانات المستخدمين
$users_data = [];

try {
    echo "<h2>🔄 بدء عملية إعادة تعيين كلمات السر...</h2>";
    
    // جلب جميع المستخدمين
    $query = "SELECT id, name, email, role, phone, is_active, created_at FROM users ORDER BY role, name";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<p>❌ لا توجد مستخدمين في قاعدة البيانات</p>";
        
        // إنشاء مستخدمين افتراضيين
        echo "<h3>📝 إنشاء مستخدمين افتراضيين...</h3>";
        
        $default_users = [
            [
                'name' => 'المدير العام',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'phone' => '+216 12 345 678'
            ],
            [
                'name' => 'د. أحمد محمد - أخصائي',
                'email' => '<EMAIL>', 
                'role' => 'specialist',
                'phone' => '+216 98 765 432'
            ],
            [
                'name' => 'أحمد الأب - ولي أمر',
                'email' => '<EMAIL>',
                'role' => 'parent', 
                'phone' => '+216 55 123 456'
            ],
            [
                'name' => 'محمد الطالب',
                'email' => '<EMAIL>',
                'role' => 'student',
                'phone' => '+216 77 987 654'
            ]
        ];
        
        foreach ($default_users as $user_data) {
            $password = $default_passwords[$user_data['role']];
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            $insert_query = "INSERT INTO users (name, email, password, role, phone, is_active) 
                           VALUES (:name, :email, :password, :role, :phone, 1)";
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->execute([
                ':name' => $user_data['name'],
                ':email' => $user_data['email'], 
                ':password' => $hashed_password,
                ':role' => $user_data['role'],
                ':phone' => $user_data['phone']
            ]);
            
            $users_data[] = [
                'id' => $db->lastInsertId(),
                'name' => $user_data['name'],
                'email' => $user_data['email'],
                'password' => $password,
                'role' => $user_data['role'],
                'phone' => $user_data['phone'],
                'status' => 'جديد',
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            echo "<p>✅ تم إنشاء: {$user_data['name']} ({$user_data['role']})</p>";
        }
        
    } else {
        echo "<h3>👥 تم العثور على " . count($users) . " مستخدم</h3>";
        
        // إعادة تعيين كلمات السر للمستخدمين الموجودين
        foreach ($users as $user) {
            $new_password = $default_passwords[$user['role']];
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            
            // تحديث كلمة السر
            $update_query = "UPDATE users SET password = :password WHERE id = :id";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->execute([
                ':password' => $hashed_password,
                ':id' => $user['id']
            ]);
            
            $users_data[] = [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'password' => $new_password,
                'role' => $user['role'],
                'phone' => $user['phone'] ?? 'غير محدد',
                'status' => $user['is_active'] ? 'نشط' : 'غير نشط',
                'created_at' => $user['created_at']
            ];
            
            echo "<p>🔄 تم تحديث كلمة السر لـ: {$user['name']} ({$user['role']})</p>";
        }
    }
    
    echo "<h3>✅ تم إعادة تعيين جميع كلمات السر بنجاح!</h3>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ: " . $e->getMessage() . "</p>";
    exit;
}

// إنشاء ملف بيانات المستخدمين
$credentials_content = "# بيانات تسجيل الدخول لمنصة نبراس\n";
$credentials_content .= "# Nibrass Platform Login Credentials\n";
$credentials_content .= "# تم إنشاؤه في: " . date('Y-m-d H:i:s') . "\n";
$credentials_content .= "# Generated on: " . date('Y-m-d H:i:s') . "\n\n";

$credentials_content .= "=" . str_repeat("=", 80) . "\n";
$credentials_content .= "                    بيانات المستخدمين - منصة نبراس\n";
$credentials_content .= "                    NIBRASS PLATFORM USERS DATA\n";
$credentials_content .= "=" . str_repeat("=", 80) . "\n\n";

// تجميع المستخدمين حسب النوع
$users_by_role = [];
foreach ($users_data as $user) {
    $users_by_role[$user['role']][] = $user;
}

$role_names = [
    'admin' => 'المديرين (Administrators)',
    'specialist' => 'الأخصائيين (Specialists)', 
    'parent' => 'أولياء الأمور (Parents)',
    'student' => 'الطلاب (Students)'
];

foreach ($role_names as $role => $role_title) {
    if (isset($users_by_role[$role])) {
        $credentials_content .= "\n" . str_repeat("-", 60) . "\n";
        $credentials_content .= "🔹 " . $role_title . "\n";
        $credentials_content .= str_repeat("-", 60) . "\n\n";
        
        foreach ($users_by_role[$role] as $user) {
            $credentials_content .= "👤 الاسم / Name: " . $user['name'] . "\n";
            $credentials_content .= "📧 البريد الإلكتروني / Email: " . $user['email'] . "\n";
            $credentials_content .= "🔑 كلمة السر / Password: " . $user['password'] . "\n";
            $credentials_content .= "📱 الهاتف / Phone: " . $user['phone'] . "\n";
            $credentials_content .= "📊 الحالة / Status: " . $user['status'] . "\n";
            $credentials_content .= "📅 تاريخ الإنشاء / Created: " . $user['created_at'] . "\n";
            $credentials_content .= "🆔 المعرف / ID: " . $user['id'] . "\n";
            $credentials_content .= "\n";
        }
    }
}

// إضافة معلومات إضافية
$credentials_content .= "\n" . str_repeat("=", 80) . "\n";
$credentials_content .= "                        معلومات إضافية\n";
$credentials_content .= "                      ADDITIONAL INFORMATION\n";
$credentials_content .= str_repeat("=", 80) . "\n\n";

$credentials_content .= "🌐 رابط المنصة / Platform URL: " . SITE_URL . "\n";
$credentials_content .= "🔐 صفحة تسجيل الدخول / Login Page: " . SITE_URL . "/login.php\n";
$credentials_content .= "📝 صفحة التسجيل / Registration Page: " . SITE_URL . "/register.php\n\n";

$credentials_content .= "📋 كلمات السر الافتراضية حسب النوع:\n";
$credentials_content .= "Default Passwords by Role:\n\n";
foreach ($default_passwords as $role => $password) {
    $credentials_content .= "• " . $role_names[$role] . ": " . $password . "\n";
}

$credentials_content .= "\n" . str_repeat("-", 60) . "\n";
$credentials_content .= "⚠️  ملاحظات أمنية مهمة / Important Security Notes:\n";
$credentials_content .= str_repeat("-", 60) . "\n";
$credentials_content .= "1. يُنصح بتغيير كلمات السر بعد أول تسجيل دخول\n";
$credentials_content .= "   It's recommended to change passwords after first login\n\n";
$credentials_content .= "2. احتفظ بهذا الملف في مكان آمن\n";
$credentials_content .= "   Keep this file in a secure location\n\n";
$credentials_content .= "3. لا تشارك كلمات السر مع أشخاص غير مخولين\n";
$credentials_content .= "   Don't share passwords with unauthorized persons\n\n";

// حفظ الملف
$filename = 'nibrass_users_credentials_' . date('Y-m-d_H-i-s') . '.txt';
file_put_contents($filename, $credentials_content);

echo "<h3>📄 تم إنشاء ملف بيانات المستخدمين: <a href='$filename' target='_blank'>$filename</a></h3>";

// عرض ملخص
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>📊 ملخص العملية / Operation Summary</h3>";
echo "<ul>";
echo "<li>✅ عدد المستخدمين المعالجين: " . count($users_data) . "</li>";
echo "<li>✅ تم إعادة تعيين جميع كلمات السر</li>";
echo "<li>✅ تم إنشاء ملف البيانات: $filename</li>";
echo "</ul>";
echo "</div>";

// عرض جدول المستخدمين
echo "<h3>👥 جدول المستخدمين الحالي</h3>";
echo "<table border='1' style='width: 100%; border-collapse: collapse; margin: 20px 0;'>";
echo "<tr style='background: #007bff; color: white;'>";
echo "<th style='padding: 10px;'>ID</th>";
echo "<th style='padding: 10px;'>الاسم</th>";
echo "<th style='padding: 10px;'>البريد الإلكتروني</th>";
echo "<th style='padding: 10px;'>كلمة السر</th>";
echo "<th style='padding: 10px;'>النوع</th>";
echo "<th style='padding: 10px;'>الهاتف</th>";
echo "<th style='padding: 10px;'>الحالة</th>";
echo "</tr>";

foreach ($users_data as $user) {
    $role_color = [
        'admin' => '#dc3545',
        'specialist' => '#28a745', 
        'parent' => '#007bff',
        'student' => '#ffc107'
    ];
    
    echo "<tr>";
    echo "<td style='padding: 8px; text-align: center;'>" . $user['id'] . "</td>";
    echo "<td style='padding: 8px;'>" . htmlspecialchars($user['name']) . "</td>";
    echo "<td style='padding: 8px;'>" . htmlspecialchars($user['email']) . "</td>";
    echo "<td style='padding: 8px; font-weight: bold; color: #dc3545;'>" . $user['password'] . "</td>";
    echo "<td style='padding: 8px; background: " . $role_color[$user['role']] . "; color: white; text-align: center;'>" . $user['role'] . "</td>";
    echo "<td style='padding: 8px;'>" . htmlspecialchars($user['phone']) . "</td>";
    echo "<td style='padding: 8px; text-align: center;'>" . $user['status'] . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🎉 تمت العملية بنجاح!</h4>";
echo "<p>يمكنك الآن استخدام البيانات الموجودة في الملف أو الجدول أعلاه لتسجيل الدخول إلى المنصة.</p>";
echo "<p><strong>رابط تسجيل الدخول:</strong> <a href='login.php' target='_blank'>" . SITE_URL . "/login.php</a></p>";
echo "</div>";
?>

<style>
body {
    font-family: 'Arial', sans-serif;
    margin: 20px;
    background: #f5f5f5;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

p {
    margin: 5px 0;
    padding: 5px;
}

table {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    background: white;
}

th {
    font-weight: bold;
}

tr:nth-child(even) {
    background: #f8f9fa;
}

tr:hover {
    background: #e3f2fd;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
