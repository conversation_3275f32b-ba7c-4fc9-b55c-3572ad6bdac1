<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كطالب
require_student();

$user = current_user();

// جلب معلومات الطالب
try {
    $student_info_query = "SELECT * FROM students WHERE user_id = :user_id";
    $student_info_stmt = $db->prepare($student_info_query);
    $student_info_stmt->bindParam(':user_id', $user['id']);
    $student_info_stmt->execute();
    $student_info = $student_info_stmt->fetch();
} catch (PDOException $e) {
    $student_info = null;
}

// جلب إحصائيات التقدم
try {
    $student_id = $student_info['id'] ?? 0;
    $progress_stats_query = "SELECT
                               COUNT(*) as total_activities,
                               SUM(points_earned) as total_points,
                               AVG(score_percentage) as avg_score,
                               COUNT(CASE WHEN completed_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as week_activities,
                               COUNT(CASE WHEN completed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as month_activities
                             FROM student_progress
                             WHERE student_id = :student_id";
    $progress_stats_stmt = $db->prepare($progress_stats_query);
    $progress_stats_stmt->bindParam(':student_id', $student_id);
    $progress_stats_stmt->execute();
    $progress_stats = $progress_stats_stmt->fetch();
} catch (PDOException $e) {
    $progress_stats = [
        'total_activities' => 0,
        'total_points' => 0,
        'avg_score' => 0,
        'week_activities' => 0,
        'month_activities' => 0
    ];
}

// جلب التقدم حسب المادة
try {
    $student_id = $student_info['id'] ?? 0;
    $subject_progress_query = "SELECT
                                 a.category,
                                 COUNT(sp.id) as completed_count,
                                 AVG(sp.score_percentage) as avg_score,
                                 SUM(sp.points_earned) as total_points
                               FROM student_progress sp
                               LEFT JOIN activities a ON sp.activity_id = a.id
                               WHERE sp.student_id = :student_id
                               GROUP BY a.category";
    $subject_progress_stmt = $db->prepare($subject_progress_query);
    $subject_progress_stmt->bindParam(':student_id', $student_id);
    $subject_progress_stmt->execute();
    $subject_progress = $subject_progress_stmt->fetchAll();
} catch (PDOException $e) {
    $subject_progress = [];
}

// جلب آخر الأنشطة المكتملة
try {
    $student_id = $student_info['id'] ?? 0;
    $recent_activities_query = "SELECT sp.*, a.title, a.category, a.difficulty_level
                               FROM student_progress sp
                               LEFT JOIN activities a ON sp.activity_id = a.id
                               WHERE sp.student_id = :student_id
                               ORDER BY sp.completed_at DESC
                               LIMIT 10";
    $recent_activities_stmt = $db->prepare($recent_activities_query);
    $recent_activities_stmt->bindParam(':student_id', $student_id);
    $recent_activities_stmt->execute();
    $recent_activities = $recent_activities_stmt->fetchAll();
} catch (PDOException $e) {
    $recent_activities = [];
}

// حساب المستوى
$level = floor($progress_stats['total_points'] / 100) + 1;
$points_to_next_level = 100 - ($progress_stats['total_points'] % 100);

// إعداد متغيرات الصفحة
$page_title = 'تقرير التقدم';
$page_description = 'تقرير مفصل عن تقدمك التعليمي';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
.progress-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.progress-header h1,
.progress-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.progress-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.progress-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: rgba(74, 144, 226, 0.3);
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: #e3f2fd;
    transform: translateY(-3px);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #4a90e2;
    margin-bottom: 0.5rem;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.stat-label {
    color: #666;
    font-size: 1rem;
    font-weight: 500;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.subject-progress {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.subject-progress:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.12);
}

.progress-bar-custom {
    height: 12px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.8s ease;
    position: relative;
}

.progress-fill.math { background: linear-gradient(90deg, #ff6b6b, #ee5a24); }
.progress-fill.language { background: linear-gradient(90deg, #4ecdc4, #44a08d); }
.progress-fill.science { background: linear-gradient(90deg, #45b7d1, #2980b9); }
.progress-fill.art { background: linear-gradient(90deg, #96ceb4, #27ae60); }

.activity-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #e3f2fd;
    transform: translateX(-5px);
}

.level-display {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    color: white;
    text-align: center;
    margin-bottom: 2rem;
}

.level-badge {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 2rem;
    font-weight: 700;
    backdrop-filter: blur(10px);
    border: 3px solid rgba(255, 255, 255, 0.3);
}

/* تأكيد ظهور الأيقونات */
.progress-header i,
.progress-card i,
.stat-item i,
.subject-progress i,
.activity-item i,
.btn i {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}
</style>

<!-- قسم العنوان -->
<section class="nibrass-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-chart-line"></i> تقرير التقدم</h1>
            <p class="lead">تقرير مفصل عن رحلتك التعليمية وإنجازاتك</p>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <!-- المستوى الحالي -->
        <div class="level-display">
            <div class="level-badge">
                <?php echo $level; ?>
            </div>
            <h3>المستوى <?php echo $level; ?></h3>
            <p>تحتاج <?php echo $points_to_next_level; ?> نقطة للوصول للمستوى التالي</p>
            
            <div class="progress-bar-custom">
                <div class="progress-fill math" style="width: <?php echo (100 - $points_to_next_level); ?>%"></div>
            </div>
        </div>

        <!-- الإحصائيات العامة -->
        <div class="progress-card">
            <h3><i class="fas fa-chart-bar"></i> الإحصائيات العامة</h3>
            
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-item">
                        <div class="stat-value"><?php echo $progress_stats['total_activities']; ?></div>
                        <div class="stat-label">الأنشطة المكتملة</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-item">
                        <div class="stat-value"><?php echo $progress_stats['total_points']; ?></div>
                        <div class="stat-label">إجمالي النقاط</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-item">
                        <div class="stat-value"><?php echo round($progress_stats['avg_score']); ?>%</div>
                        <div class="stat-label">متوسط الدرجات</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-item">
                        <div class="stat-value"><?php echo $progress_stats['week_activities']; ?></div>
                        <div class="stat-label">أنشطة هذا الأسبوع</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- التقدم حسب المادة -->
            <div class="col-lg-8">
                <div class="progress-card">
                    <h3><i class="fas fa-books"></i> التقدم حسب المادة</h3>
                    
                    <?php if (!empty($subject_progress)): ?>
                        <?php foreach ($subject_progress as $subject): ?>
                            <div class="subject-progress">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="mb-0">
                                        <i class="fas fa-<?php 
                                            echo $subject['category'] == 'math' ? 'calculator' : 
                                                ($subject['category'] == 'language' ? 'book-open' : 
                                                ($subject['category'] == 'science' ? 'flask' : 'palette'));
                                        ?>"></i>
                                        <?php 
                                        $subject_names = [
                                            'math' => 'الرياضيات',
                                            'language' => 'اللغة العربية',
                                            'science' => 'العلوم',
                                            'art' => 'الفنون'
                                        ];
                                        echo $subject_names[$subject['category']] ?? $subject['category'];
                                        ?>
                                    </h5>
                                    <span class="badge bg-primary"><?php echo round($subject['avg_score']); ?>%</span>
                                </div>
                                
                                <div class="progress-bar-custom">
                                    <div class="progress-fill <?php echo $subject['category']; ?>" 
                                         style="width: <?php echo round($subject['avg_score']); ?>%"></div>
                                </div>
                                
                                <div class="d-flex justify-content-between text-muted small">
                                    <span><i class="fas fa-tasks"></i> <?php echo $subject['completed_count']; ?> نشاط مكتمل</span>
                                    <span><i class="fas fa-star"></i> <?php echo $subject['total_points']; ?> نقطة</span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات تقدم</h5>
                            <p class="text-muted">ابدأ بحل الأنشطة لترى تقدمك هنا</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- آخر الأنشطة -->
            <div class="col-lg-4">
                <div class="progress-card">
                    <h3><i class="fas fa-history"></i> آخر الأنشطة</h3>
                    
                    <?php if (!empty($recent_activities)): ?>
                        <?php foreach (array_slice($recent_activities, 0, 5) as $activity): ?>
                            <div class="activity-item">
                                <h6 class="mb-1"><?php echo htmlspecialchars($activity['title']); ?></h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <?php echo date('d/m/Y', strtotime($activity['completed_at'])); ?>
                                    </small>
                                    <span class="badge bg-<?php echo $activity['score_percentage'] >= 80 ? 'success' : ($activity['score_percentage'] >= 60 ? 'warning' : 'danger'); ?>">
                                        <?php echo round($activity['score_percentage']); ?>%
                                    </span>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-star"></i> +<?php echo $activity['points_earned']; ?> نقطة
                                </small>
                            </div>
                        <?php endforeach; ?>
                        
                        <div class="text-center mt-3">
                            <a href="activities.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-gamepad"></i> المزيد من الأنشطة
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-gamepad fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">لا توجد أنشطة بعد</h6>
                            <p class="text-muted small">ابدأ أول نشاط لك!</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- العودة للوحة التحكم -->
        <div class="text-center mt-4">
            <a href="dashboard.php" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    // تحريك أشرطة التقدم
    document.addEventListener('DOMContentLoaded', function() {
        const progressBars = document.querySelectorAll('.progress-fill');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
