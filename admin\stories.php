<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كمدير
require_admin();

$user = current_user();

// معالجة الإجراءات
$success_message = '';
$error_message = '';

// معالجة إضافة قصة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_story'])) {
    check_csrf();
    
    $title = clean_input($_POST['title']);
    $description = clean_input($_POST['description']);
    $content = clean_input($_POST['content']);
    $cover_image = clean_input($_POST['cover_image']);
    $category = clean_input($_POST['category']);
    $difficulty_level = clean_input($_POST['difficulty_level']);
    $target_age = (int)$_POST['target_age'];
    $reading_time = (int)$_POST['reading_time'];
    
    if (empty($title) || empty($description) || empty($content)) {
        $error_message = "يرجى ملء جميع الحقول المطلوبة";
    } else {
        try {
            $insert_query = "INSERT INTO stories (title, description, content, cover_image, category, difficulty_level, target_age, reading_time) 
                            VALUES (:title, :description, :content, :cover_image, :category, :difficulty_level, :target_age, :reading_time)";
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':title', $title);
            $insert_stmt->bindParam(':description', $description);
            $insert_stmt->bindParam(':content', $content);
            $insert_stmt->bindParam(':cover_image', $cover_image);
            $insert_stmt->bindParam(':category', $category);
            $insert_stmt->bindParam(':difficulty_level', $difficulty_level);
            $insert_stmt->bindParam(':target_age', $target_age);
            $insert_stmt->bindParam(':reading_time', $reading_time);
            
            if ($insert_stmt->execute()) {
                $success_message = "تم إضافة القصة بنجاح";
            } else {
                $error_message = "فشل في إضافة القصة";
            }
        } catch (PDOException $e) {
            // التحقق من وجود الجدول
            if (strpos($e->getMessage(), "doesn't exist") !== false) {
                $error_message = "جدول القصص غير موجود. يرجى تشغيل إعداد الجداول المفقودة أولاً.";
            } else {
                $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
            }
        }
    }
}

// معالجة تفعيل/إلغاء تفعيل القصة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['toggle_story'])) {
    check_csrf();
    
    $story_id = (int)$_POST['story_id'];
    $current_status = (int)$_POST['current_status'];
    $new_status = $current_status ? 0 : 1;
    
    try {
        $update_query = "UPDATE stories SET is_active = :status WHERE id = :story_id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':status', $new_status);
        $update_stmt->bindParam(':story_id', $story_id);
        
        if ($update_stmt->execute()) {
            $action_text = $new_status ? 'تفعيل' : 'إلغاء تفعيل';
            $success_message = "تم {$action_text} القصة بنجاح";
        } else {
            $error_message = "فشل في تحديث حالة القصة";
        }
    } catch (PDOException $e) {
        $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
    }
}

// معالجة حذف القصة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_story'])) {
    check_csrf();
    
    $story_id = (int)$_POST['story_id'];
    
    try {
        $delete_query = "DELETE FROM stories WHERE id = :story_id";
        $delete_stmt = $db->prepare($delete_query);
        $delete_stmt->bindParam(':story_id', $story_id);
        
        if ($delete_stmt->execute()) {
            $success_message = "تم حذف القصة بنجاح";
        } else {
            $error_message = "فشل في حذف القصة";
        }
    } catch (PDOException $e) {
        $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
    }
}

// جلب القصص مع الفلترة والبحث
$search = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';
$difficulty_filter = $_GET['difficulty'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$limit = 12;
$offset = ($page - 1) * $limit;

try {
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(title LIKE :search OR description LIKE :search)";
        $params[':search'] = "%{$search}%";
    }
    
    if (!empty($category_filter)) {
        $where_conditions[] = "category = :category";
        $params[':category'] = $category_filter;
    }
    
    if (!empty($difficulty_filter)) {
        $where_conditions[] = "difficulty_level = :difficulty";
        $params[':difficulty'] = $difficulty_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // جلب القصص
    $stories_query = "SELECT * FROM stories {$where_clause} ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
    $stories_stmt = $db->prepare($stories_query);
    
    foreach ($params as $key => $value) {
        $stories_stmt->bindValue($key, $value);
    }
    $stories_stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stories_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $stories_stmt->execute();
    $stories = $stories_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب العدد الإجمالي
    $count_query = "SELECT COUNT(*) as total FROM stories {$where_clause}";
    $count_stmt = $db->prepare($count_query);
    
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    
    $count_stmt->execute();
    $total_stories = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_stories / $limit);
    
} catch (PDOException $e) {
    // التحقق من وجود الجدول
    try {
        $check_table = $db->query("SHOW TABLES LIKE 'stories'");
        if ($check_table->rowCount() == 0) {
            $error_message = "جدول القصص غير موجود. يرجى تشغيل إعداد الجداول المفقودة.";
        } else {
            $error_message = "خطأ في جلب القصص: " . $e->getMessage();
        }
    } catch (Exception $e2) {
        $error_message = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
    }

    $stories = [];
    $total_stories = 0;
    $total_pages = 0;
}

// إعداد متغيرات الصفحة
$page_title = 'إدارة القصص التفاعلية';
$page_description = 'إدارة القصص التفاعلية في منصة نبراس';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<!-- Additional Fonts for Enhanced Arabic Support -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

<style>
/* Enhanced Admin Styling - Consistent with Dashboard */
body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.admin-container {
    min-height: 100vh;
    padding: 2rem 0;
    background: #f8f9fa;
}

.admin-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Section - Unified Admin Header */
.admin-header {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 5rem;
}

.admin-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #333;
}

.admin-header p {
    opacity: 0.8;
    margin: 0;
    color: #333;
}

/* Form and Filter Sections */
.admin-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.section-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.search-filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1rem;
    align-items: end;
}

/* Story Cards Grid */
.stories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.story-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
}

.story-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.story-cover {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    position: relative;
}

.story-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.story-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.7);
    color: white;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.story-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.story-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.story-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    flex: 1;
}

.story-meta {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.85rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
}

.category-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: inline-block;
}

.category-badge.مغامرات { background: #e3f2fd; color: #1976d2; }
.category-badge.تعليمية { background: #e8f5e8; color: #2e7d32; }
.category-badge.خيالية { background: #f3e5f5; color: #7b1fa2; }
.category-badge.تاريخية { background: #fff3e0; color: #f57c00; }
.category-badge.دينية { background: #ffebee; color: #c62828; }
.category-badge.عامة { background: #f5f5f5; color: #424242; }

.story-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
    margin-top: auto;
}

.status-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #28a745;
}

.status-indicator.inactive {
    background: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stories-grid {
        grid-template-columns: 1fr;
    }
    
    .search-filters {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .story-actions {
        flex-direction: column;
    }
}
</style>

<?php include 'includes/admin-nav.php'; ?>

<div class="admin-container">
    <div class="admin-content container mx-auto">
        <!-- رأس الصفحة -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-book-open"></i> إدارة القصص التفاعلية</h1>
                    <p>إدارة القصص التفاعلية في منصة نبراس</p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                <?php if (strpos($error_message, "جدول القصص غير موجود") !== false): ?>
                    <div class="mt-3">
                        <a href="../create_stories_table.php" class="btn btn-warning">
                            <i class="fas fa-database"></i> إنشاء جدول القصص
                        </a>
                        <a href="../setup_missing_tables.php" class="btn btn-info">
                            <i class="fas fa-tools"></i> إعداد جميع الجداول المفقودة
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- إضافة قصة جديدة -->
        <div class="admin-section">
            <h3 class="section-title">
                <i class="fas fa-plus-circle"></i> إضافة قصة تفاعلية جديدة
            </h3>
            
            <form method="POST">
                <?php echo csrf_field(); ?>
                
                <div class="form-grid">
                    <div>
                        <label class="form-label">عنوان القصة *</label>
                        <input type="text" name="title" class="form-control" required>
                    </div>
                    
                    <div>
                        <label class="form-label">رابط صورة الغلاف</label>
                        <input type="url" name="cover_image" class="form-control" placeholder="https://...">
                    </div>
                    
                    <div>
                        <label class="form-label">الفئة *</label>
                        <select name="category" class="form-select" required>
                            <option value="">اختر الفئة</option>
                            <option value="مغامرات">مغامرات</option>
                            <option value="تعليمية">تعليمية</option>
                            <option value="خيالية">خيالية</option>
                            <option value="تاريخية">تاريخية</option>
                            <option value="دينية">دينية</option>
                            <option value="عامة">عامة</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="form-label">مستوى الصعوبة</label>
                        <select name="difficulty_level" class="form-select">
                            <option value="easy">سهل</option>
                            <option value="medium">متوسط</option>
                            <option value="hard">صعب</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="form-label">العمر المستهدف</label>
                        <input type="number" name="target_age" class="form-control" value="6" min="3" max="18">
                    </div>
                    
                    <div>
                        <label class="form-label">وقت القراءة (دقيقة)</label>
                        <input type="number" name="reading_time" class="form-control" value="10" min="1" max="60">
                    </div>
                </div>
                
                <div class="mt-3">
                    <label class="form-label">وصف القصة *</label>
                    <textarea name="description" class="form-control" rows="3" required></textarea>
                </div>
                
                <div class="mt-3">
                    <label class="form-label">محتوى القصة *</label>
                    <textarea name="content" class="form-control" rows="8" required placeholder="اكتب محتوى القصة هنا..."></textarea>
                </div>
                
                <div class="mt-3">
                    <button type="submit" name="add_story" class="nibrass-btn nibrass-btn-success">
                        <i class="fas fa-plus"></i> إضافة القصة
                    </button>
                </div>
            </form>
        </div>

        <!-- فلاتر البحث -->
        <div class="admin-section">
            <form method="GET" class="search-filters">
                <div>
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث في العنوان أو الوصف..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div>
                    <label class="form-label">الفئة</label>
                    <select name="category" class="form-select">
                        <option value="">جميع الفئات</option>
                        <option value="مغامرات" <?php echo $category_filter === 'مغامرات' ? 'selected' : ''; ?>>مغامرات</option>
                        <option value="تعليمية" <?php echo $category_filter === 'تعليمية' ? 'selected' : ''; ?>>تعليمية</option>
                        <option value="خيالية" <?php echo $category_filter === 'خيالية' ? 'selected' : ''; ?>>خيالية</option>
                        <option value="تاريخية" <?php echo $category_filter === 'تاريخية' ? 'selected' : ''; ?>>تاريخية</option>
                        <option value="دينية" <?php echo $category_filter === 'دينية' ? 'selected' : ''; ?>>دينية</option>
                        <option value="عامة" <?php echo $category_filter === 'عامة' ? 'selected' : ''; ?>>عامة</option>
                    </select>
                </div>
                
                <div>
                    <label class="form-label">الصعوبة</label>
                    <select name="difficulty" class="form-select">
                        <option value="">جميع المستويات</option>
                        <option value="easy" <?php echo $difficulty_filter === 'easy' ? 'selected' : ''; ?>>سهل</option>
                        <option value="medium" <?php echo $difficulty_filter === 'medium' ? 'selected' : ''; ?>>متوسط</option>
                        <option value="hard" <?php echo $difficulty_filter === 'hard' ? 'selected' : ''; ?>>صعب</option>
                    </select>
                </div>
                
                <div>
                    <button type="submit" class="nibrass-btn nibrass-btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>

        <!-- شبكة القصص -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3 style="color: black;"><i class="fas fa-list"></i> قائمة القصص</h3>
            <div style="color: black; opacity: 0.9;">
                إجمالي النتائج: <?php echo number_format($total_stories); ?>
            </div>
        </div>

        <?php if (empty($stories)): ?>
            <div class="admin-section text-center py-5">
                <i class="fas fa-book-open fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد قصص</h4>
                <p class="text-muted">لم يتم العثور على قصص مطابقة لمعايير البحث</p>
            </div>
        <?php else: ?>
            <div class="stories-grid">
                <?php foreach ($stories as $story): ?>
                    <div class="story-card">
                        <div class="status-indicator <?php echo $story['is_active'] ? 'active' : 'inactive'; ?>"></div>
                        
                        <div class="story-cover">
                            <?php if (!empty($story['cover_image'])): ?>
                                <img src="<?php echo htmlspecialchars($story['cover_image']); ?>" alt="<?php echo htmlspecialchars($story['title']); ?>">
                            <?php else: ?>
                                <i class="fas fa-book"></i>
                            <?php endif; ?>
                            <div class="story-overlay">
                                <i class="fas fa-book-reader"></i>
                            </div>
                        </div>
                        
                        <div class="story-content">
                            <div class="story-title"><?php echo htmlspecialchars($story['title']); ?></div>
                            
                            <span class="category-badge <?php echo str_replace(' ', '-', $story['category']); ?>">
                                <?php echo htmlspecialchars($story['category']); ?>
                            </span>
                            
                            <div class="story-description">
                                <?php echo htmlspecialchars($story['description']); ?>
                            </div>
                            
                            <div class="story-meta">
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span><?php echo $story['reading_time']; ?> دقيقة</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-child"></i>
                                    <span>عمر <?php echo $story['target_age']; ?> سنة</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-eye"></i>
                                    <span><?php echo number_format($story['views_count']); ?> قراءة</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-signal"></i>
                                    <span><?php 
                                        $difficulties = ['easy' => 'سهل', 'medium' => 'متوسط', 'hard' => 'صعب'];
                                        echo $difficulties[$story['difficulty_level']] ?? $story['difficulty_level'];
                                    ?></span>
                                </div>
                            </div>
                            
                            <div class="story-actions">
                                <!-- تفعيل/إلغاء تفعيل -->
                                <form method="POST" style="display: inline;">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="story_id" value="<?php echo $story['id']; ?>">
                                    <input type="hidden" name="current_status" value="<?php echo $story['is_active']; ?>">
                                    <button type="submit" name="toggle_story" 
                                            class="nibrass-btn <?php echo $story['is_active'] ? 'nibrass-btn-warning' : 'nibrass-btn-success'; ?> btn-sm"
                                            onclick="return confirm('هل أنت متأكد من تغيير حالة هذه القصة؟')">
                                        <i class="fas <?php echo $story['is_active'] ? 'fa-pause' : 'fa-play'; ?>"></i>
                                    </button>
                                </form>

                                <!-- حذف -->
                                <form method="POST" style="display: inline;">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="story_id" value="<?php echo $story['id']; ?>">
                                    <button type="submit" name="delete_story" 
                                            class="nibrass-btn nibrass-btn-danger btn-sm"
                                            onclick="return confirm('هل أنت متأكد من حذف هذه القصة؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// تضمين الفوتر
include '../includes/footer.php';
?>
