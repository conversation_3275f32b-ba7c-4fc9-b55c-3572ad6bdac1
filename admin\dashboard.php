<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كمدير
require_admin();

$user = current_user();

// جلب الإحصائيات العامة
try {
    // عدد المستخدمين مع التحقق من وجود الجدول
    $users_query = "SELECT
                        COUNT(*) as total_users,
                        SUM(CASE WHEN role = 'student' THEN 1 ELSE 0 END) as students,
                        SUM(CASE WHEN role = 'parent' THEN 1 ELSE 0 END) as parents,
                        SUM(CASE WHEN role = 'specialist' THEN 1 ELSE 0 END) as specialists,
                        SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admins
                    FROM users WHERE is_active = 1";
    $users_stmt = $db->prepare($users_query);
    $users_stmt->execute();
    $users_stats = $users_stmt->fetch(PDO::FETCH_ASSOC);

    // عدد الأنشطة والمحتوى مع التحقق من وجود الجداول
    $content_stats = [];

    // عدد الأنشطة
    try {
        $activities_query = "SELECT COUNT(*) as count FROM activities WHERE is_active = 1";
        $activities_stmt = $db->prepare($activities_query);
        $activities_stmt->execute();
        $content_stats['activities'] = $activities_stmt->fetch(PDO::FETCH_ASSOC)['count'];
    } catch (PDOException $e) {
        $content_stats['activities'] = 0;
    }

    // عدد الفيديوهات
    try {
        $videos_query = "SELECT COUNT(*) as count FROM educational_videos WHERE is_active = 1";
        $videos_stmt = $db->prepare($videos_query);
        $videos_stmt->execute();
        $content_stats['videos'] = $videos_stmt->fetch(PDO::FETCH_ASSOC)['count'];
    } catch (PDOException $e) {
        $content_stats['videos'] = 0;
    }

    // عدد القصص
    try {
        $stories_query = "SELECT COUNT(*) as count FROM stories WHERE is_active = 1";
        $stories_stmt = $db->prepare($stories_query);
        $stories_stmt->execute();
        $content_stats['stories'] = $stories_stmt->fetch(PDO::FETCH_ASSOC)['count'];
    } catch (PDOException $e) {
        $content_stats['stories'] = 0;
    }

    // عدد الموارد
    try {
        $resources_query = "SELECT COUNT(*) as count FROM educational_resources WHERE is_active = 1";
        $resources_stmt = $db->prepare($resources_query);
        $resources_stmt->execute();
        $content_stats['resources'] = $resources_stmt->fetch(PDO::FETCH_ASSOC)['count'];
    } catch (PDOException $e) {
        $content_stats['resources'] = 0;
    }

    // إحصائيات التقدم
    try {
        $progress_query = "SELECT
                            COUNT(*) as total_progress,
                            COALESCE(AVG(score_percentage), 0) as avg_score,
                            COALESCE(SUM(points_earned), 0) as total_points
                           FROM student_progress";
        $progress_stmt = $db->prepare($progress_query);
        $progress_stmt->execute();
        $progress_stats = $progress_stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $progress_stats = ['total_progress' => 0, 'avg_score' => 0, 'total_points' => 0];
    }

    // الأنشطة الحديثة
    try {
        $recent_activities_query = "SELECT sp.*, u.name as student_name, a.title as activity_title
                                   FROM student_progress sp
                                   JOIN users u ON u.id = (SELECT user_id FROM students WHERE id = sp.student_id LIMIT 1)
                                   LEFT JOIN activities a ON sp.activity_id = a.id
                                   ORDER BY sp.completed_at DESC
                                   LIMIT 10";
        $recent_activities_stmt = $db->prepare($recent_activities_query);
        $recent_activities_stmt->execute();
        $recent_activities = $recent_activities_stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $recent_activities = [];
    }

    // إحصائيات الإشعارات
    try {
        $notifications_query = "SELECT
                                COUNT(*) as total_notifications,
                                SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_notifications
                               FROM notifications";
        $notifications_stmt = $db->prepare($notifications_query);
        $notifications_stmt->execute();
        $notifications_stats = $notifications_stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $notifications_stats = ['total_notifications' => 0, 'unread_notifications' => 0];
    }

} catch (PDOException $e) {
    $error_message = "خطأ في جلب الإحصائيات: " . $e->getMessage();
    $users_stats = ['total_users' => 0, 'students' => 0, 'parents' => 0, 'specialists' => 0, 'admins' => 0];
    $content_stats = ['activities' => 0, 'videos' => 0, 'stories' => 0, 'resources' => 0];
    $progress_stats = ['total_progress' => 0, 'avg_score' => 0, 'total_points' => 0];
    $notifications_stats = ['total_notifications' => 0, 'unread_notifications' => 0];
    $recent_activities = [];
}

// إعداد متغيرات الصفحة
$page_title = 'لوحة تحكم المدير';
$page_description = 'إدارة شاملة لمنصة نبراس التعليمية';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<!-- Additional Fonts for Enhanced Arabic Support -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

<style>
/* Admin Dashboard Modern Styles with Enhanced Arabic RTL Support */

/* تحسين مظهر أزرار الإجراءات السريعة */
.btn-lg {
    padding: 1.5rem 1rem;
    border-radius: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: none;
    font-weight: 600;
    text-decoration: none;
}

.btn-lg:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    text-decoration: none;
}

.btn-lg i {
    transition: all 0.3s ease;
}

.btn-lg:hover i {
    transform: scale(1.1);
}

/* ألوان مخصصة للأزرار */
.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f, #a8e6cf);
}

.btn-info {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.btn-secondary {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
}

.btn-dark {
    background: linear-gradient(135deg, #2d3436, #636e72);
}

.btn-light {
    background: linear-gradient(135deg, #ddd6fe, #e0e7ff);
    color: #4c1d95;
    border: 2px solid #8b5cf6;
}

.btn-light:hover {
    color: #4c1d95;
}

.btn-purple {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.btn-purple:hover {
    color: white;
}
body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: #f8f9fa !important;
    min-height: 100vh;
    margin: 0;
    padding-top: 70px; /* إضافة padding علوي لتجنب التداخل مع navbar الثابت */
    direction: rtl;
    text-align: right;
}

/* Header Section - Unified Admin Header */
.admin-header {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 5rem;
}

.admin-header h1,
.admin-header p,
.admin-header .lead {
    color: #333 !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    margin: 0;
}

.admin-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.admin-header p,
.admin-header .lead {
    font-size: 1rem;
    opacity: 0.8;
}

/* Enhanced Dashboard Cards with Better Arabic RTL Support */
.dashboard-card {
    background: white;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.08);
    padding: 2.5rem;
    margin-bottom: 2.5rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(40, 167, 69, 0.1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);

}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
    border-radius: 25px 25px 0 0;
}

.dashboard-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(40, 167, 69, 0.15);
    border-color: rgba(40, 167, 69, 0.3);
}

.card-header-custom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid rgba(40, 167, 69, 0.1);
    position: relative;
}

.card-header-custom::after {
    content: '';
    position: absolute;
    bottom: -2px;
    right: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 2px;
}

.card-icon {
    width: 70px;
    height: 70px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white !important;
    margin-left: 1.5rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    flex-shrink: 0;
    background-color: black;
}

.card-icon:hover {
    transform: scale(1.1) rotate(5deg);
}

.card-icon i {
    color: white !important;
    font-size: 1.8rem;
    line-height: 1;
}

.card-icon.success {
    /* background: linear-gradient(135deg, #28a745, #20c997); */
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.card-icon.info {
    /* background: linear-gradient(135deg, #17a2b8, #138496); */
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
}

.card-icon.warning {
    /* background: linear-gradient(135deg, #ffc107, #fd7e14); */
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
}

.card-icon.primary {
    /* background: linear-gradient(135deg, #4a90e2, #2c5aa0); */
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

.card-icon.danger {
    /* background: linear-gradient(135deg, #dc3545, #c82333); */
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
}

.card-title-custom {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.card-subtitle {
    color: #7f8c8d;
    font-size: 1rem;
    margin-top: 0.5rem;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    font-weight: 400;
}

/* Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #28a745, #20c997);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(40, 167, 69, 0.15);
    border-color: rgba(40, 167, 69, 0.3);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white !important;
    margin: 0 auto 1rem auto;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    flex-shrink: 0;
}

.stat-icon i {
    color: white !important;
    font-size: 1.5rem;
    line-height: 1;
}

.stat-icon.green {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.stat-icon.blue {
    background: linear-gradient(135deg, #17a2b8, #138496);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
}

.stat-icon.orange {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
}

.stat-icon.purple {
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    box-shadow: 0 8px 25px rgba(111, 66, 193, 0.3);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.stat-label {
    color: #7f8c8d;
    font-size: 1rem;
    font-weight: 500;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

/* Consultation Cards */
.consultation-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border-right: 4px solid transparent;
}

.consultation-card:hover {
    transform: translateX(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.consultation-card.urgent {
    border-right-color: #dc3545;
}

.consultation-card.normal {
    border-right-color: #ffc107;
}

.consultation-card.routine {
    border-right-color: #28a745;
}

.consultation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.consultation-title {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    font-size: 1.1rem;
}

.urgency-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.urgency-urgent { background: #f8d7da; color: #721c24; }
.urgency-normal { background: #fff3cd; color: #856404; }
.urgency-routine { background: #d4edda; color: #155724; }

.consultation-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin: 1rem 0;
    font-size: 0.9rem;
    color: #666;
}

.consultation-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn-action {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-accept {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.btn-accept:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
    color: white;
}

.btn-respond {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-respond:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
    color: white;
}

.btn-schedule {
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    color: white;
}

.btn-schedule:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(111, 66, 193, 0.3);
    color: white;
}

.nibrass-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255,255,255,0.2);
}

.nibrass-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #4a90e2, #2c5aa0);
}

.nibrass-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.nibrass-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.nibrass-card-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    margin-left: 1rem;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

.nibrass-card-icon.users { background: linear-gradient(135deg, #4a90e2, #2c5aa0); }
.nibrass-card-icon.content { background: linear-gradient(135deg, #28a745, #20c997); }
.nibrass-card-icon.progress { background: linear-gradient(135deg, #ffc107, #fd7e14); }
.nibrass-card-icon.notifications { background: linear-gradient(135deg, #dc3545, #e83e8c); }

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.stat-number {
    font-size: 2.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    line-height: 1;
}

.stat-label {
    color: #6c757d;
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.stat-details {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-label {
    color: #6c757d;
}

.detail-value {
    font-weight: 600;
    color: #2c3e50;
}





/* تأكيد ظهور الأيقونات */
.fas, .far, .fab, .fal {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .consultation-header,
    .card-header-custom {
        flex-direction: column;
        text-align: center;
    }

    .consultation-actions {
        justify-content: center;
    }

    .card-icon {
        margin-left: 0;
        margin-bottom: 1rem;
    }

    .nibrass-header h1 {
        font-size: 2rem;
    }

    .nibrass-header .lead {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .stat-value {
        font-size: 2.2rem;
    }
}
</style>

<?php include 'includes/admin-nav.php'; ?>

<!-- قسم العنوان -->
<div class="container mx-auto">
    <div class="admin-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="fas fa-tachometer-alt"></i> مرحباً <?php echo htmlspecialchars($user['name']); ?></h1>
                <p>إدارة شاملة لمنصة نبراس التعليمية - لوحة تحكم المدير</p>
                <div class="mt-3">
                    <span class="badge bg-light text-dark fs-6">
                        <i class="fas fa-shield-alt"></i> مدير النظام
                    </span>
                </div>
            </div>
            <div>
                <div class="text-center">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h4><?php echo number_format($users_stats['total_users']); ?></h4>
                                <small>إجمالي المستخدمين</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <i class="fas fa-book fa-2x mb-2"></i>
                                <h4><?php echo number_format($content_stats['activities'] + $content_stats['videos'] + $content_stats['stories'] + $content_stats['resources']); ?></h4>
                                <small>المحتوى التعليمي</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- رسائل الخطأ -->
<?php if (isset($error_message)): ?>
    <div class="container mx-auto">
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
        </div>
    </div>
<?php endif; ?>

<!-- الإحصائيات -->
<section class="section">
    <div class="container mx-auto">
        <!-- الإجراءات السريعة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon success">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">الإجراءات السريعة</h3>
                                <p class="card-subtitle">الوصول السريع للخدمات الإدارية الأساسية</p>
                            </div>
                        </div>
                    </div>

                    <div class="row text-center">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="users.php" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-users fa-2x mb-2 d-block"></i>
                                إدارة المستخدمين
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="activities.php" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-gamepad fa-2x mb-2 d-block"></i>
                                إدارة الأنشطة
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="videos.php" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-video fa-2x mb-2 d-block"></i>
                                إدارة الفيديوهات
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="stories.php" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-book-open fa-2x mb-2 d-block"></i>
                                إدارة القصص
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="resources.php" class="btn btn-purple btn-lg w-100">
                                <i class="fas fa-folder fa-2x mb-2 d-block"></i>
                                إدارة الموارد
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="achievements.php" class="btn btn-danger btn-lg w-100">
                                <i class="fas fa-trophy fa-2x mb-2 d-block"></i>
                                إدارة الإنجازات
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="notifications.php" class="btn btn-dark btn-lg w-100">
                                <i class="fas fa-bell fa-2x mb-2 d-block"></i>
                                إدارة الإشعارات
                            </a>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="settings.php" class="btn btn-light btn-lg w-100 text-dark">
                                <i class="fas fa-cog fa-2x mb-2 d-block"></i>
                                إعدادات النظام
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon green">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-value"><?php echo number_format($users_stats['total_users']); ?></div>
                <div class="stat-label">إجمالي المستخدمين</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon blue">
                    <i class="fas fa-book"></i>
                </div>
                <div class="stat-value"><?php echo number_format($content_stats['activities'] + $content_stats['videos'] + $content_stats['stories'] + $content_stats['resources']); ?></div>
                <div class="stat-label">المحتوى التعليمي</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon orange">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-value"><?php echo number_format($progress_stats['total_progress']); ?></div>
                <div class="stat-label">الأنشطة المكتملة</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon purple">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="stat-value"><?php echo number_format($notifications_stats['total_notifications']); ?></div>
                <div class="stat-label">إجمالي الإشعارات</div>
            </div>
        </div>
    </div>
</section>

<!-- المحتوى الرئيسي -->
<section class="section">
    <div class="container mx-auto">
        <div class="row">
            <!-- إحصائيات المستخدمين -->
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon success">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">إدارة المستخدمين</h3>
                                <p class="card-subtitle">إحصائيات تفصيلية للمستخدمين النشطين</p>
                            </div>
                        </div>
                    </div>

                    <div class="stat-details">
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-graduation-cap"></i> طلاب</span>
                            <span class="detail-value"><?php echo number_format($users_stats['students']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-user-friends"></i> أولياء أمور</span>
                            <span class="detail-value"><?php echo number_format($users_stats['parents']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-user-md"></i> أخصائيين</span>
                            <span class="detail-value"><?php echo number_format($users_stats['specialists']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-user-shield"></i> مديرين</span>
                            <span class="detail-value"><?php echo number_format($users_stats['admins']); ?></span>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <a href="users.php" class="btn btn-success">
                            <i class="fas fa-users"></i> إدارة المستخدمين
                        </a>
                    </div>
                </div>
            </div>

            <!-- إحصائيات المحتوى -->
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon info">
                                <i class="fas fa-book"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">المحتوى التعليمي</h3>
                                <p class="card-subtitle">إحصائيات تفصيلية للمحتوى المتاح</p>
                            </div>
                        </div>
                    </div>

                    <div class="stat-details">
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-gamepad"></i> أنشطة تفاعلية</span>
                            <span class="detail-value"><?php echo number_format($content_stats['activities']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-video"></i> فيديوهات تعليمية</span>
                            <span class="detail-value"><?php echo number_format($content_stats['videos']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-book-open"></i> قصص تفاعلية</span>
                            <span class="detail-value"><?php echo number_format($content_stats['stories']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-folder"></i> موارد تعليمية</span>
                            <span class="detail-value"><?php echo number_format($content_stats['resources']); ?></span>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <a href="activities.php" class="btn btn-info">
                            <i class="fas fa-book"></i> إدارة المحتوى
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- تقدم الطلاب -->
            <div class="col-lg-8">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon warning">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">الأنشطة الحديثة</h3>
                                <p class="card-subtitle">آخر الأنشطة المكتملة من قبل الطلاب</p>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($recent_activities)): ?>
                        <?php foreach ($recent_activities as $activity): ?>
                            <div class="consultation-card">
                                <div class="consultation-header">
                                    <h5 class="consultation-title">
                                        <i class="fas fa-user text-success"></i>
                                        <?php echo htmlspecialchars($activity['student_name']); ?>
                                        أكمل نشاط "<?php echo htmlspecialchars($activity['activity_title'] ?? 'نشاط غير محدد'); ?>"
                                    </h5>
                                    <span class="urgency-badge urgency-routine">
                                        <?php echo number_format($activity['score_percentage'], 1); ?>%
                                    </span>
                                </div>

                                <div class="consultation-meta">
                                    <span><i class="fas fa-calendar-alt"></i> <?php echo date('d/m/Y H:i', strtotime($activity['completed_at'])); ?></span>
                                    <span><i class="fas fa-clock"></i> وقت الإنجاز: <?php echo $activity['time_spent_minutes']; ?> دقيقة</span>
                                    <span><i class="fas fa-star"></i> النقاط: <?php echo $activity['points_earned']; ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="text-center mt-3">
                            <a href="reports.php" class="btn btn-warning">
                                <i class="fas fa-chart-bar"></i> عرض جميع التقارير
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أنشطة حديثة</h5>
                            <p class="text-muted">سيتم عرض آخر الأنشطة المكتملة هنا</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- إحصائيات التقدم والإشعارات -->
            <div class="col-lg-4">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon primary">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">إحصائيات التقدم</h3>
                                <p class="card-subtitle">تفاصيل أداء الطلاب</p>
                            </div>
                        </div>
                    </div>

                    <div class="stat-details">
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-percentage"></i> متوسط النجاح</span>
                            <span class="detail-value"><?php echo number_format($progress_stats['avg_score'], 1); ?>%</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-star"></i> إجمالي النقاط</span>
                            <span class="detail-value"><?php echo number_format($progress_stats['total_points']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-trophy"></i> متوسط النقاط</span>
                            <span class="detail-value"><?php echo $progress_stats['total_progress'] > 0 ? number_format($progress_stats['total_points'] / $progress_stats['total_progress'], 1) : '0'; ?></span>
                        </div>
                    </div>
                </div>

                <div class="dashboard-card mt-4">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon danger">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">نظام الإشعارات</h3>
                                <p class="card-subtitle">حالة الإشعارات</p>
                            </div>
                        </div>
                    </div>

                    <div class="stat-details">
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-envelope"></i> غير مقروءة</span>
                            <span class="detail-value"><?php echo number_format($notifications_stats['unread_notifications']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-envelope-open"></i> مقروءة</span>
                            <span class="detail-value"><?php echo number_format($notifications_stats['total_notifications'] - $notifications_stats['unread_notifications']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-clock"></i> وقت الخادم</span>
                            <span class="detail-value"><?php echo date('H:i'); ?></span>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <a href="notifications.php" class="btn btn-danger">
                            <i class="fas fa-bell"></i> إدارة الإشعارات
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="row">
            <div class="col-md-6">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon" style="box-shadow: 0 8px 25px rgba(111, 66, 193, 0.3);">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">معلومات النظام</h3>
                                <p class="card-subtitle">تفاصيل الخادم والنظام</p>
                            </div>
                        </div>
                    </div>
                    <div class="stat-details">
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-code"></i> إصدار PHP</span>
                            <span class="detail-value"><?php echo PHP_VERSION; ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-memory"></i> استخدام الذاكرة</span>
                            <span class="detail-value"><?php echo number_format(memory_get_usage(true) / 1024 / 1024, 2); ?> MB</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-hdd"></i> مساحة القرص</span>
                            <span class="detail-value"><?php echo number_format(disk_free_space('.') / 1024 / 1024 / 1024, 2); ?> GB</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-globe"></i> المنطقة الزمنية</span>
                            <span class="detail-value"><?php echo date_default_timezone_get(); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="dashboard-card">
                    <div class="card-header-custom">
                        <div class="d-flex align-items-center">
                            <div class="card-icon" style="box-shadow: 0 8px 25px rgba(32, 201, 151, 0.3);">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div>
                                <h3 class="card-title-custom">روابط سريعة</h3>
                                <p class="card-subtitle">أدوات إدارية مفيدة</p>
                            </div>
                        </div>
                    </div>
                    <div class="d-grid gap-2">
                        <a href="../setup_missing_tables.php" class="btn btn-success">
                            <i class="fas fa-database"></i> إعداد قاعدة البيانات
                        </a>
                        <a href="../index.php" class="btn btn-info" target="_blank">
                            <i class="fas fa-external-link-alt"></i> عرض المنصة
                        </a>
                        <a href="backup.php" class="btn btn-warning">
                            <i class="fas fa-download"></i> نسخة احتياطية
                        </a>
                        <a href="logs.php" class="btn btn-secondary">
                            <i class="fas fa-file-alt"></i> سجلات النظام
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript للتحسينات التفاعلية -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات التمرير للكروت
    const cards = document.querySelectorAll('.dashboard-card, .stat-card, .consultation-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // تحديث الوقت كل دقيقة
    setInterval(function() {
        const timeElements = document.querySelectorAll('[data-time]');
        timeElements.forEach(element => {
            const timestamp = element.getAttribute('data-time');
            const date = new Date(timestamp);
            const now = new Date();
            const diff = Math.floor((now - date) / 1000 / 60);

            if (diff < 60) {
                element.textContent = diff + ' دقيقة مضت';
            } else if (diff < 1440) {
                element.textContent = Math.floor(diff / 60) + ' ساعة مضت';
            }
        });
    }, 60000);

    // إضافة تأثيرات للأزرار
    const actionButtons = document.querySelectorAll('.btn-action, .btn');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // إضافة تأثير النقر
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });

    // تحديث الوقت في الإشعارات
    function updateTime() {
        const now = new Date();
        const timeElements = document.querySelectorAll('.detail-value');
        timeElements.forEach(element => {
            if (element.textContent.includes(':')) {
                element.textContent = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
        });
    }

    setInterval(updateTime, 1000);
});
</script>

<?php
// تضمين الفوتر
include '../includes/footer.php';
?>
