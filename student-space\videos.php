<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كطالب
require_student();

$user = current_user();

// جلب معلومات الطالب
try {
    $student_info_query = "SELECT * FROM students WHERE user_id = :user_id";
    $student_info_stmt = $db->prepare($student_info_query);
    $student_info_stmt->bindParam(':user_id', $user['id']);
    $student_info_stmt->execute();
    $student_info = $student_info_stmt->fetch();
} catch (PDOException $e) {
    $student_info = null;
}

// جلب الفيديوهات التعليمية
try {
    // التحقق من وجود جدول educational_videos أولاً
    $check_table = $db->query("SHOW TABLES LIKE 'educational_videos'");
    if ($check_table->rowCount() == 0) {
        throw new Exception("جدول الفيديوهات غير موجود");
    }

    $student_age = $student_info['age'] ?? 10;
    $videos_query = "SELECT * FROM educational_videos WHERE is_active = 1 AND target_age <= :age ORDER BY category ASC, created_at DESC";
    $videos_stmt = $db->prepare($videos_query);
    $videos_stmt->bindParam(':age', $student_age);
    $videos_stmt->execute();
    $videos = $videos_stmt->fetchAll();

    // إذا لم توجد فيديوهات، استخدم البيانات التجريبية
    if (empty($videos)) {
        throw new Exception("لا توجد فيديوهات في قاعدة البيانات");
    }

} catch (Exception $e) {
    // إنشاء فيديوهات تجريبية إذا لم توجد في قاعدة البيانات
    $videos = [
        [
            'id' => 1,
            'title' => 'تعلم الحروف العربية',
            'description' => 'فيديو تعليمي ممتع لتعلم الحروف العربية بالصوت والصورة',
            'thumbnail' => '../images/video1.jpg',
            'duration' => '15:30',
            'category' => 'لغة عربية',
            'difficulty_level' => 'easy',
            'views_count' => 1250
        ],
        [
            'id' => 2,
            'title' => 'الأرقام والعد',
            'description' => 'تعلم الأرقام من 1 إلى 10 بطريقة تفاعلية ومسلية',
            'thumbnail' => '../images/video2.jpg',
            'duration' => '12:45',
            'category' => 'رياضيات',
            'difficulty_level' => 'easy',
            'views_count' => 980
        ],
        [
            'id' => 3,
            'title' => 'الألوان والأشكال',
            'description' => 'اكتشف عالم الألوان والأشكال الهندسية الأساسية',
            'thumbnail' => '../images/video3.jpg',
            'duration' => '18:20',
            'category' => 'فنون',
            'difficulty_level' => 'easy',
            'views_count' => 1500
        ],
        [
            'id' => 4,
            'title' => 'عالم الحيوانات',
            'description' => 'رحلة ممتعة لاكتشاف الحيوانات وأصواتها',
            'thumbnail' => '../images/video4.jpg',
            'duration' => '20:15',
            'category' => 'علوم',
            'difficulty_level' => 'medium',
            'views_count' => 2100
        ],
        [
            'id' => 5,
            'title' => 'قصص الأنبياء للأطفال',
            'description' => 'قصص تعليمية هادفة من سير الأنبياء عليهم السلام',
            'thumbnail' => '../images/video5.jpg',
            'duration' => '25:00',
            'category' => 'دينية',
            'difficulty_level' => 'medium',
            'views_count' => 1800
        ],
        [
            'id' => 6,
            'title' => 'تجارب علمية بسيطة',
            'description' => 'تجارب علمية آمنة وممتعة يمكن تطبيقها في المنزل',
            'thumbnail' => '../images/video6.jpg',
            'duration' => '22:30',
            'category' => 'علوم',
            'difficulty_level' => 'medium',
            'views_count' => 1350
        ]
    ];
}

// إعداد متغيرات الصفحة
$page_title = 'الفيديوهات التعليمية';
$page_description = 'مكتبة فيديوهات تعليمية ممتعة ومفيدة';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
.videos-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.videos-header h1,
.videos-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.video-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
    border: 2px solid transparent;
    position: relative;
}

.video-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    border-color: rgba(74, 144, 226, 0.3);
}

.video-thumbnail {
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.video-thumbnail::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="videoPattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23videoPattern)"/></svg>');
    opacity: 0.3;
}

.play-button {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #4a90e2;
    z-index: 2;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.play-button:hover {
    transform: scale(1.1);
    background: white;
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
}

.video-duration {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
    font-size: 0.8rem;
    z-index: 2;
}

.video-content {
    padding: 1.5rem;
}

.video-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.video-description {
    color: #7f8c8d;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.video-meta {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.category-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    background: #e3f2fd;
    color: #1976d2;
}

.difficulty-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-easy { background: #d4edda; color: #155724; }
.difficulty-medium { background: #fff3cd; color: #856404; }
.difficulty-hard { background: #f8d7da; color: #721c24; }

.video-stats {
    display: flex;
    justify-content: between;
    align-items: center;
    text-muted;
    font-size: 0.8rem;
}

.featured-video {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border-radius: 25px;
    padding: 2rem;
    color: white;
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
}

.featured-video::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="featuredPattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23featuredPattern)"/></svg>');
    opacity: 0.3;
}

.featured-content {
    position: relative;
    z-index: 2;
}

.featured-icon {
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 2.5rem;
    backdrop-filter: blur(10px);
    border: 3px solid rgba(255, 255, 255, 0.3);
}

/* تأكيد ظهور الأيقونات */
.videos-header i,
.video-card i,
.featured-video i,
.btn i {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}
</style>

<!-- قسم العنوان -->
<section class="nibrass-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-video"></i> الفيديوهات التعليمية</h1>
            <p class="lead">شاهد وتعلم مع مجموعة رائعة من الفيديوهات التعليمية الممتعة</p>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <!-- الفيديو المميز -->
        <div class="featured-video">
            <div class="featured-content">
                <div class="featured-icon">
                    <i class="fas fa-play"></i>
                </div>
                <h2>فيديو اليوم المميز</h2>
                <p class="lead">شاهد فيديو جديد كل يوم مليء بالمعلومات المفيدة والمتعة</p>
                <a href="watch-video.php?id=1" class="btn btn-light btn-lg">
                    <i class="fas fa-play"></i> شاهد الآن
                </a>
            </div>
        </div>

        <!-- مكتبة الفيديوهات -->
        <div class="row">
            <div class="col-12 mb-4">
                <h2><i class="fas fa-film"></i> مكتبة الفيديوهات</h2>
                <p class="text-muted">اختر الفيديو الذي يناسب اهتماماتك ومستواك</p>
            </div>
        </div>

        <div class="row">
            <?php foreach ($videos as $video): ?>
                <div class="col-lg-6 col-xl-4">
                    <div class="video-card">
                        <div class="video-thumbnail">
                            <div class="play-button" onclick="playVideo(<?php echo $video['id']; ?>)">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="video-duration"><?php echo $video['duration']; ?></div>
                        </div>
                        
                        <div class="video-content">
                            <h4 class="video-title"><?php echo htmlspecialchars($video['title']); ?></h4>
                            <p class="video-description"><?php echo htmlspecialchars($video['description']); ?></p>
                            
                            <div class="video-meta">
                                <span class="category-badge"><?php echo htmlspecialchars($video['category']); ?></span>
                                <span class="difficulty-badge difficulty-<?php echo $video['difficulty_level']; ?>">
                                    <?php 
                                    $difficulty_labels = [
                                        'easy' => 'سهل',
                                        'medium' => 'متوسط',
                                        'hard' => 'صعب'
                                    ];
                                    echo $difficulty_labels[$video['difficulty_level']] ?? 'سهل';
                                    ?>
                                </span>
                            </div>
                            
                            <div class="video-stats">
                                <span><i class="fas fa-eye"></i> <?php echo number_format($video['views_count']); ?> مشاهدة</span>
                                <span><i class="fas fa-clock"></i> <?php echo $video['duration']; ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- إحصائيات المشاهدة -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="video-card">
                    <div class="video-content text-center">
                        <h3><i class="fas fa-chart-bar"></i> إحصائيات المشاهدة</h3>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center p-3">
                                    <i class="fas fa-video fa-2x text-primary mb-2"></i>
                                    <h4 class="text-primary">0</h4>
                                    <small class="text-muted">فيديوهات مشاهدة</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3">
                                    <i class="fas fa-clock fa-2x text-success mb-2"></i>
                                    <h4 class="text-success">0</h4>
                                    <small class="text-muted">دقائق مشاهدة</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3">
                                    <i class="fas fa-star fa-2x text-warning mb-2"></i>
                                    <h4 class="text-warning">0</h4>
                                    <small class="text-muted">نقاط المشاهدة</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3">
                                    <i class="fas fa-trophy fa-2x text-info mb-2"></i>
                                    <h4 class="text-info">0</h4>
                                    <small class="text-muted">شارات المشاهدة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- العودة للوحة التحكم -->
        <div class="text-center mt-4">
            <a href="dashboard.php" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    // تشغيل الفيديو
    function playVideo(videoId) {
        // يمكن تطوير هذه الوظيفة لاحقاً لتشغيل الفيديوهات الفعلية
        alert('🎬 سيتم تشغيل الفيديو قريباً... استعد للمتعة والتعلم!');
        
        // مثال على إعادة التوجيه لصفحة مشاهدة الفيديو
        // window.location.href = 'watch-video.php?id=' + videoId;
    }
    
    // تأثيرات التمرير للكروت
    document.addEventListener('DOMContentLoaded', function() {
        const videoCards = document.querySelectorAll('.video-card');
        videoCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
        
        // تأثير على أزرار التشغيل
        const playButtons = document.querySelectorAll('.play-button');
        playButtons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
