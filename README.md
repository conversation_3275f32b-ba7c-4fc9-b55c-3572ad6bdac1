# منصة نبراس - Nibrass Platform

منصة تعليمية متخصصة في دعم الأطفال ذوي اضطراب طيف التوحد وأسرهم

## 🌟 نظرة عامة

منصة نبراس هي منصة تعليمية شاملة مصممة خصيصاً لدعم الأطفال ذوي اضطراب طيف التوحد، وتوفير الموارد والأدوات اللازمة لأسرهم والمختصين العاملين معهم.

## ✨ المميزات الرئيسية

### 🎯 فضاءات متخصصة
- **فضاء التلاميذ**: أنشطة تفاعلية وألعاب تعليمية
- **فضاء الأولياء**: استشارات ودعم نفسي
- **فضاء الأخصائيين**: موارد مهنية وتبادل خبرات

### 📚 المحتوى التعليمي
- مكتبة رقمية شاملة
- مقالات علمية متخصصة
- بودكاست تعليمي
- قصص تفاعلية
- فيديوهات توثيقية

### 🎨 الإبداع والتفاعل
- ورشات تفاعلية
- أنشطة إبداعية
- نظام المكافآت والتحفيز
- مؤتمرات وفعاليات

### 🤖 الذكاء الاصطناعي
- مساعد ذكي للدعم الفوري
- إجابات متخصصة حول التوحد
- دعم متعدد اللغات

## 🛠️ التقنيات المستخدمة

- **Backend**: PHP 8.0+
- **Database**: MySQL 8.0+
- **Frontend**: HTML5, CSS3, JavaScript
- **Framework**: Bootstrap 5 RTL
- **Icons**: Font Awesome 6
- **AI Integration**: OpenAI API

## 📋 متطلبات التشغيل

- PHP 8.0 أو أحدث
- MySQL 8.0 أو أحدث
- Apache/Nginx Web Server
- مفتاح OpenAI API

## 🚀 التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/nibrass-platform.git
cd nibrass-platform
```

### 2. إعداد قاعدة البيانات
1. قم بتشغيل XAMPP أو WAMP
2. افتح المتصفح واذهب إلى: `http://localhost/Nibrass/setup.php`
3. اتبع التعليمات لإعداد قاعدة البيانات

### 3. تكوين الإعدادات
قم بتعديل ملف `config.php` وضع الإعدادات المناسبة:
```php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'nibrass_platform');
define('DB_USER', 'root');
define('DB_PASS', '');

// مفتاح OpenAI
define('OPENAI_API_KEY', 'your-openai-api-key');
```


### 4. تشغيل المشروع
افتح المتصفح واذهب إلى: `http://localhost/Nibrass`

## 👤 بيانات تسجيل الدخول الافتراضية

**المدير العام:**
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`

## 📁 هيكل المشروع

```
Nibrass/
├── config.php              # إعدادات المشروع
├── db.php                  # اتصال قاعدة البيانات
├── index.php               # الصفحة الرئيسية
├── setup.php               # إعداد قاعدة البيانات
├── style.css               # التصميم الأساسي
├── images/                 # مجلد الصور
├── uploads/                # مجلد الملفات المرفوعة
│   ├── images/
│   ├── documents/
│   ├── audio/
│   └── video/
├── includes/               # ملفات PHP المساعدة
├── admin/                  # لوحة الإدارة
├── student-space/          # فضاء التلاميذ
├── parent-space/           # فضاء الأولياء
├── specialist-space/       # فضاء الأخصائيين
└── api/                    # واجهات برمجة التطبيقات
```

## 🎨 التصميم والواجهة

- دعم كامل للغة العربية (RTL)
- تصميم متجاوب لجميع الأجهزة
- ألوان مريحة ومناسبة للأطفال
- خطوط عربية واضحة (Cairo, Tajawal)
- أيقونات تفاعلية وجذابة

## 🔧 المراحل القادمة

### المرحلة 1: ✅ الهيكل الأساسي
- [x] إعداد ملفات PHP الأساسية
- [x] إنشاء قاعدة البيانات
- [x] تصميم الواجهة الأساسية

### المرحلة 2: 🔄 تطوير الفضاءات
- [ ] فضاء التلاميذ
- [ ] فضاء الأولياء
- [ ] فضاء الأخصائيين

### المرحلة 3: 📚 المحتوى
- [ ] مكتبة المنصة
- [ ] قسم الإبداع
- [ ] الورشات التفاعلية
- [ ] نظام الهدايا

### المرحلة 4: 🤖 الذكاء الاصطناعي
- [ ] تطوير شات الدعم
- [ ] ربط OpenAI API
- [ ] تخصيص الردود

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير المنصة! يرجى:

1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: [www.nibrass.com](http://www.nibrass.com)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

نشكر جميع المساهمين والداعمين لهذا المشروع الهادف لخدمة الأطفال ذوي اضطراب طيف التوحد وأسرهم.

---

**منصة نبراس - إضاءة طريق التعلم والنمو** ✨
