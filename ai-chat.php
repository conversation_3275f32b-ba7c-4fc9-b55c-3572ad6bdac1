<?php
require_once 'config.php';
require_once 'db.php';

// إعداد متغيرات الصفحة
$page_title = 'المساعد الذكي - نبراس';
$page_description = 'تحدث مع المساعد الذكي للحصول على الدعم والمشورة المتخصصة';

// تضمين الهيدر
include 'includes/header.php';
?>

<style>
.ai-hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
}

.ai-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.ai-hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.ai-hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease;
}

.ai-hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease 0.2s both;
}

.ai-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ai-feature-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    animation: fadeInUp 1s ease 0.4s both;
}

.ai-feature-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.2);
}

.ai-feature-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: #ffd700;
}

.ai-cta-section {
    background: white;
    padding: 4rem 0;
    text-align: center;
}

.ai-demo-card {
    background: #f8f9fa;
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.demo-chat {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin: 1rem 0;
    max-width: 400px;
    margin: 1rem auto;
}

.demo-message {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.demo-message.user {
    flex-direction: row-reverse;
}

.demo-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.demo-message.user .demo-avatar {
    background: var(--primary-color);
}

.demo-message.ai .demo-avatar {
    background: var(--nibrass-green);
}

.demo-content {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    border-radius: 15px;
    max-width: 70%;
}

.demo-message.user .demo-content {
    background: var(--primary-color);
    color: white;
}

.login-prompt {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    margin: 2rem 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.pulse-animation {
    animation: pulse 2s infinite;
}
</style>

<!-- القسم الرئيسي -->
<section class="ai-hero-section">
    <div class="container">
        <div class="ai-hero-content">
            <div class="ai-hero-title">
                <i class="fas fa-robot pulse-animation"></i>
                المساعد الذكي - نبراس
            </div>
            <p class="ai-hero-subtitle">
                مساعدك الذكي المتخصص في دعم الأطفال ذوي اضطراب طيف التوحد وأسرهم
            </p>
            
            <div class="ai-features-grid">
                <div class="ai-feature-card">
                    <div class="ai-feature-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h4>محادثة تفاعلية</h4>
                    <p>تحدث مع المساعد الذكي واحصل على إجابات فورية لجميع استفساراتك</p>
                </div>
                
                <div class="ai-feature-card">
                    <div class="ai-feature-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h4>نصائح متخصصة</h4>
                    <p>احصل على نصائح وإرشادات متخصصة في التعامل مع اضطراب طيف التوحد</p>
                </div>
                
                <div class="ai-feature-card">
                    <div class="ai-feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4>متاح 24/7</h4>
                    <p>المساعد الذكي متاح على مدار الساعة لتقديم الدعم والمساعدة</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم العرض التوضيحي -->
<section class="ai-cta-section">
    <div class="container">
        <h2 class="section-title">
            <i class="fas fa-play-circle text-primary"></i>
            جرب المساعد الذكي الآن
        </h2>
        <p class="lead">اكتشف كيف يمكن للمساعد الذكي أن يساعدك في رحلة دعم طفلك</p>
        
        <div class="ai-demo-card">
            <h4>مثال على المحادثة</h4>
            <div class="demo-chat">
                <div class="demo-message ai">
                    <div class="demo-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="demo-content">
                        مرحباً! أنا نبراس، المساعد الذكي. كيف يمكنني مساعدتك اليوم؟
                    </div>
                </div>
                
                <div class="demo-message user">
                    <div class="demo-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="demo-content">
                        أريد نصائح للتعامل مع طفلي ذو التوحد
                    </div>
                </div>
                
                <div class="demo-message ai">
                    <div class="demo-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="demo-content">
                        بالطبع! إليك بعض النصائح المهمة للتعامل مع طفلك...
                    </div>
                </div>
            </div>
        </div>
        
        <?php if (isset($_SESSION['user_id'])): ?>
            <div class="text-center">
                <a href="<?php echo get_url('parent-space/ai-chat.php'); ?>" class="btn btn-ai-primary btn-lg">
                    <i class="fas fa-robot"></i>
                    ابدأ المحادثة الآن
                </a>
            </div>
        <?php else: ?>
            <div class="login-prompt">
                <h4>
                    <i class="fas fa-sign-in-alt"></i>
                    سجل دخولك للوصول للمساعد الذكي
                </h4>
                <p>يجب تسجيل الدخول أولاً للتحدث مع المساعد الذكي والاستفادة من جميع الميزات</p>
                <div class="mt-3">
                    <a href="<?php echo get_url('login.php'); ?>" class="btn btn-light btn-lg me-2">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </a>
                    <a href="<?php echo get_url('register.php'); ?>" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-user-plus"></i>
                        إنشاء حساب جديد
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php
// تضمين الفوتر
include 'includes/footer.php';
?>
