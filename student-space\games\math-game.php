<?php
require_once '../../includes/auth.php';
require_once '../../db.php';

// التحقق من تسجيل الدخول كطالب
require_student();

$user = current_user();

// جلب معلومات الطالب
try {
    $student_info_query = "SELECT * FROM students WHERE user_id = :user_id";
    $student_info_stmt = $db->prepare($student_info_query);
    $student_info_stmt->bindParam(':user_id', $user['id']);
    $student_info_stmt->execute();
    $student_info = $student_info_stmt->fetch();
} catch (PDOException $e) {
    $student_info = null;
}

// معالجة حفظ النتيجة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_score'])) {
    check_csrf();
    
    $score = (int)$_POST['score'];
    $total_questions = (int)$_POST['total_questions'];
    $time_spent = (int)$_POST['time_spent'];
    $difficulty = clean_input($_POST['difficulty']);
    
    $score_percentage = ($score / $total_questions) * 100;
    $points_earned = $score * ($difficulty === 'hard' ? 15 : ($difficulty === 'medium' ? 10 : 5));
    
    try {
        $save_progress_query = "INSERT INTO student_progress 
                               (student_id, activity_id, score_percentage, points_earned, time_spent_minutes, is_completed) 
                               VALUES (:student_id, 1, :score_percentage, :points_earned, :time_spent, 1)";
        $save_progress_stmt = $db->prepare($save_progress_query);
        $student_id = $student_info['id'] ?? 0;
        $save_progress_stmt->bindParam(':student_id', $student_id);
        $save_progress_stmt->bindParam(':score_percentage', $score_percentage);
        $save_progress_stmt->bindParam(':points_earned', $points_earned);
        $save_progress_stmt->bindParam(':time_spent', $time_spent);
        $save_progress_stmt->execute();
        
        $success_message = "تم حفظ نتيجتك! حصلت على {$points_earned} نقطة.";
    } catch (PDOException $e) {
        $error_message = "خطأ في حفظ النتيجة: " . $e->getMessage();
    }
}

// إعداد متغيرات الصفحة
$page_title = 'لعبة الرياضيات المرحة';
$page_description = 'تعلم الرياضيات بطريقة ممتعة وتفاعلية';

// تضمين الهيدر
include '../../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../../css/nibrass-unified.css" rel="stylesheet">

<style>
.math-game-container {
    /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
    min-height: 100vh;
    padding: 2rem 0;
}

.game-card {
    background: white;
    border-radius: 25px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    padding: 2rem;
    margin: 1rem 0;
    transition: all 0.3s ease;
}

.game-header {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #4a90e2, #4a90e2);
    color: white;
    border-radius: 20px;
    margin-bottom: 2rem;
        margin-top: 80px;

}

.question-display {
    font-size: 3rem;
    font-weight: bold;
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
    margin: 2rem 0;
    color: #2c3e50;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.answer-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin: 2rem 0;
}

.answer-btn {
    padding: 1.5rem;
    font-size: 1.5rem;
    font-weight: bold;
    border: 3px solid #e9ecef;
    border-radius: 15px;
    background: white;
    color: #2c3e50;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.answer-btn:hover {
    background: #4a90e2;
    color: white;
    border-color: #4a90e2;
    transform: translateY(-3px);
}

.answer-btn.correct {
    background: #28a745;
    color: white;
    border-color: #28a745;
    animation: correctAnswer 0.6s ease;
}

.answer-btn.incorrect {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
    animation: incorrectAnswer 0.6s ease;
}

@keyframes correctAnswer {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes incorrectAnswer {
    0% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
    100% { transform: translateX(0); }
}

.game-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin: 2rem 0;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #4a90e2;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.difficulty-selector {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 2rem 0;
}

.difficulty-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    background: white;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.difficulty-btn.active {
    background: #4a90e2;
    color: white;
    border-color: #4a90e2;
}

.game-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 2rem 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4a90e2, #2c5aa0);
    transition: width 0.3s ease;
    border-radius: 10px;
}

.celebration {
    text-align: center;
    padding: 3rem;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: 20px;
    margin: 2rem 0;
    display: none;
}

.celebration.show {
    display: block;
    animation: celebrationPop 0.8s ease;
}

@keyframes celebrationPop {
    0% { transform: scale(0.8); opacity: 0; }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); opacity: 1; }
}

/* تأكيد ظهور الأيقونات */
.fas, .far, .fab, .fal {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

@media (max-width: 768px) {
    .answer-options {
        grid-template-columns: 1fr;
    }
    
    .game-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .question-display {
        font-size: 2rem;
        padding: 1rem;
    }
    
    .difficulty-selector {
        flex-direction: column;
        align-items: center;
    }
}
</style>

<div class="math-game-container">
    <div class="container">
        <!-- رأس اللعبة -->
        <div class="game-header">
            <h1><i class="fas fa-calculator"></i> لعبة الرياضيات المرحة</h1>
            <p>تعلم الرياضيات بطريقة ممتعة وتفاعلية!</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success text-center">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- بطاقة اللعبة -->
        <div class="game-card">
            <!-- اختيار مستوى الصعوبة -->
            <div class="difficulty-selector">
                <button class="difficulty-btn active" data-difficulty="easy">
                    <i class="fas fa-smile"></i> سهل (1-10)
                </button>
                <button class="difficulty-btn" data-difficulty="medium">
                    <i class="fas fa-meh"></i> متوسط (1-50)
                </button>
                <button class="difficulty-btn" data-difficulty="hard">
                    <i class="fas fa-brain"></i> صعب (1-100)
                </button>
            </div>

            <!-- إحصائيات اللعبة -->
            <div class="game-stats">
                <div class="stat-item">
                    <div class="stat-value" id="score">0</div>
                    <div class="stat-label">النقاط</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="question-number">1</div>
                    <div class="stat-label">السؤال</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="correct-answers">0</div>
                    <div class="stat-label">إجابات صحيحة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="timer">0</div>
                    <div class="stat-label">الوقت (ثانية)</div>
                </div>
            </div>

            <!-- شريط التقدم -->
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
            </div>

            <!-- عرض السؤال -->
            <div class="question-display" id="question-display">
                اضغط "ابدأ اللعبة" للبدء
            </div>

            <!-- خيارات الإجابة -->
            <div class="answer-options" id="answer-options" style="display: none;">
                <button class="answer-btn" id="option1"></button>
                <button class="answer-btn" id="option2"></button>
                <button class="answer-btn" id="option3"></button>
                <button class="answer-btn" id="option4"></button>
            </div>

            <!-- أزرار التحكم -->
            <div class="game-controls">
                <button class="nibrass-btn nibrass-btn-primary nibrass-btn-lg" id="start-game">
                    <i class="fas fa-play"></i> ابدأ اللعبة
                </button>
                <button class="nibrass-btn nibrass-btn-warning nibrass-btn-lg" id="restart-game" style="display: none;">
                    <i class="fas fa-redo"></i> إعادة البدء
                </button>
                <button class="nibrass-btn nibrass-btn-success nibrass-btn-lg" id="save-score" style="display: none;">
                    <i class="fas fa-save"></i> حفظ النتيجة
                </button>
            </div>
        </div>

        <!-- قسم الاحتفال -->
        <div class="celebration" id="celebration">
            <i class="fas fa-trophy fa-4x mb-3"></i>
            <h2>أحسنت! لعبة رائعة!</h2>
            <p id="final-score"></p>
            <div class="mt-3">
                <i class="fas fa-star text-warning"></i>
                <i class="fas fa-star text-warning"></i>
                <i class="fas fa-star text-warning"></i>
            </div>
        </div>

        <!-- العودة للوحة التحكم -->
        <div class="text-center mt-4">
            <a href="../dashboard.php" class="nibrass-btn nibrass-btn-outline-primary">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>
</div>

<!-- نموذج حفظ النتيجة -->
<form id="score-form" method="POST" style="display: none;">
    <?php echo csrf_field(); ?>
    <input type="hidden" name="save_score" value="1">
    <input type="hidden" name="score" id="final-score-input">
    <input type="hidden" name="total_questions" id="total-questions-input">
    <input type="hidden" name="time_spent" id="time-spent-input">
    <input type="hidden" name="difficulty" id="difficulty-input">
</form>

<script>
// متغيرات اللعبة
let gameState = {
    currentQuestion: 1,
    totalQuestions: 10,
    score: 0,
    correctAnswers: 0,
    difficulty: 'easy',
    startTime: null,
    gameActive: false,
    currentAnswer: null
};

let gameTimer;

// عناصر DOM
const questionDisplay = document.getElementById('question-display');
const answerOptions = document.getElementById('answer-options');
const startBtn = document.getElementById('start-game');
const restartBtn = document.getElementById('restart-game');
const saveBtn = document.getElementById('save-score');
const celebration = document.getElementById('celebration');

// إحصائيات
const scoreElement = document.getElementById('score');
const questionNumberElement = document.getElementById('question-number');
const correctAnswersElement = document.getElementById('correct-answers');
const timerElement = document.getElementById('timer');
const progressFill = document.getElementById('progress-fill');

// أزرار مستوى الصعوبة
const difficultyBtns = document.querySelectorAll('.difficulty-btn');
const answerBtns = document.querySelectorAll('.answer-btn');

// مستمعي الأحداث
startBtn.addEventListener('click', startGame);
restartBtn.addEventListener('click', restartGame);
saveBtn.addEventListener('click', saveScore);

difficultyBtns.forEach(btn => {
    btn.addEventListener('click', function() {
        if (!gameState.gameActive) {
            difficultyBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            gameState.difficulty = this.dataset.difficulty;
        }
    });
});

answerBtns.forEach(btn => {
    btn.addEventListener('click', function() {
        if (gameState.gameActive) {
            checkAnswer(parseInt(this.textContent));
        }
    });
});

// بدء اللعبة
function startGame() {
    gameState = {
        currentQuestion: 1,
        totalQuestions: 10,
        score: 0,
        correctAnswers: 0,
        difficulty: document.querySelector('.difficulty-btn.active').dataset.difficulty,
        startTime: Date.now(),
        gameActive: true,
        currentAnswer: null
    };
    
    startBtn.style.display = 'none';
    restartBtn.style.display = 'inline-block';
    answerOptions.style.display = 'grid';
    celebration.classList.remove('show');
    
    // تعطيل أزرار الصعوبة
    difficultyBtns.forEach(btn => btn.disabled = true);
    
    // بدء المؤقت
    gameTimer = setInterval(updateTimer, 1000);
    
    generateQuestion();
}

// إعادة بدء اللعبة
function restartGame() {
    clearInterval(gameTimer);
    difficultyBtns.forEach(btn => btn.disabled = false);
    
    startBtn.style.display = 'inline-block';
    restartBtn.style.display = 'none';
    saveBtn.style.display = 'none';
    answerOptions.style.display = 'none';
    celebration.classList.remove('show');
    
    questionDisplay.textContent = 'اضغط "ابدأ اللعبة" للبدء';
    
    // إعادة تعيين الإحصائيات
    updateStats();
    progressFill.style.width = '0%';
}

// توليد سؤال جديد
function generateQuestion() {
    const ranges = {
        easy: 10,
        medium: 50,
        hard: 100
    };
    
    const maxNum = ranges[gameState.difficulty];
    const num1 = Math.floor(Math.random() * maxNum) + 1;
    const num2 = Math.floor(Math.random() * maxNum) + 1;
    const operations = ['+', '-', '*'];
    const operation = operations[Math.floor(Math.random() * operations.length)];
    
    let answer;
    let questionText;
    
    switch(operation) {
        case '+':
            answer = num1 + num2;
            questionText = `${num1} + ${num2} = ؟`;
            break;
        case '-':
            if (num1 < num2) [num1, num2] = [num2, num1]; // تأكد من أن النتيجة موجبة
            answer = num1 - num2;
            questionText = `${num1} - ${num2} = ؟`;
            break;
        case '*':
            const smallNum1 = Math.floor(Math.random() * 10) + 1;
            const smallNum2 = Math.floor(Math.random() * 10) + 1;
            answer = smallNum1 * smallNum2;
            questionText = `${smallNum1} × ${smallNum2} = ؟`;
            break;
    }
    
    gameState.currentAnswer = answer;
    questionDisplay.textContent = questionText;
    
    // توليد خيارات الإجابة
    generateAnswerOptions(answer);
    updateStats();
}

// توليد خيارات الإجابة
function generateAnswerOptions(correctAnswer) {
    const options = [correctAnswer];
    
    // توليد إجابات خاطئة
    while (options.length < 4) {
        const wrongAnswer = correctAnswer + Math.floor(Math.random() * 20) - 10;
        if (wrongAnswer > 0 && !options.includes(wrongAnswer)) {
            options.push(wrongAnswer);
        }
    }
    
    // خلط الخيارات
    for (let i = options.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [options[i], options[j]] = [options[j], options[i]];
    }
    
    // عرض الخيارات
    answerBtns.forEach((btn, index) => {
        btn.textContent = options[index];
        btn.className = 'answer-btn';
        btn.disabled = false;
    });
}

// فحص الإجابة
function checkAnswer(selectedAnswer) {
    const isCorrect = selectedAnswer === gameState.currentAnswer;
    
    // تعطيل جميع الأزرار
    answerBtns.forEach(btn => btn.disabled = true);
    
    // تمييز الإجابة الصحيحة والخاطئة
    answerBtns.forEach(btn => {
        const btnAnswer = parseInt(btn.textContent);
        if (btnAnswer === gameState.currentAnswer) {
            btn.classList.add('correct');
        } else if (btnAnswer === selectedAnswer && !isCorrect) {
            btn.classList.add('incorrect');
        }
    });
    
    if (isCorrect) {
        gameState.score += gameState.difficulty === 'hard' ? 15 : (gameState.difficulty === 'medium' ? 10 : 5);
        gameState.correctAnswers++;
    }
    
    // الانتقال للسؤال التالي بعد تأخير
    setTimeout(() => {
        if (gameState.currentQuestion < gameState.totalQuestions) {
            gameState.currentQuestion++;
            generateQuestion();
        } else {
            endGame();
        }
    }, 1500);
}

// تحديث الإحصائيات
function updateStats() {
    scoreElement.textContent = gameState.score;
    questionNumberElement.textContent = gameState.currentQuestion;
    correctAnswersElement.textContent = gameState.correctAnswers;
    
    const progress = (gameState.currentQuestion - 1) / gameState.totalQuestions * 100;
    progressFill.style.width = progress + '%';
}

// تحديث المؤقت
function updateTimer() {
    if (gameState.startTime) {
        const elapsed = Math.floor((Date.now() - gameState.startTime) / 1000);
        timerElement.textContent = elapsed;
    }
}

// إنهاء اللعبة
function endGame() {
    clearInterval(gameTimer);
    gameState.gameActive = false;
    
    const finalTime = Math.floor((Date.now() - gameState.startTime) / 1000);
    const percentage = Math.round((gameState.correctAnswers / gameState.totalQuestions) * 100);
    
    questionDisplay.textContent = 'انتهت اللعبة!';
    answerOptions.style.display = 'none';
    
    // عرض النتيجة النهائية
    document.getElementById('final-score').textContent = 
        `حصلت على ${gameState.score} نقطة من أصل ${gameState.totalQuestions * (gameState.difficulty === 'hard' ? 15 : (gameState.difficulty === 'medium' ? 10 : 5))} نقطة (${percentage}%)`;
    
    celebration.classList.add('show');
    saveBtn.style.display = 'inline-block';
    
    // تحضير بيانات الحفظ
    document.getElementById('final-score-input').value = gameState.correctAnswers;
    document.getElementById('total-questions-input').value = gameState.totalQuestions;
    document.getElementById('time-spent-input').value = Math.ceil(finalTime / 60); // بالدقائق
    document.getElementById('difficulty-input').value = gameState.difficulty;
}

// حفظ النتيجة
function saveScore() {
    document.getElementById('score-form').submit();
}
</script>

<?php
// تضمين الفوتر
include '../../includes/footer.php';
?>
