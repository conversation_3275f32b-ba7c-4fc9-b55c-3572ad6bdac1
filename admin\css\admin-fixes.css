/* Nibrass Admin Panel - Visual Design Fixes */
/* This file contains fixes for background, layout, spacing, and consistency issues */

/* ===== GLOBAL FIXES ===== */

/* Force consistent background across all admin pages */
html, body {
    font-family: 'Cairo', '<PERSON><PERSON><PERSON>', sans-serif !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
    direction: rtl !important;
    text-align: right !important;
}

/* Fix body alignment and RTL support */
body {
    overflow-x: hidden;
    line-height: 1.6;
}

/* Container width and alignment fixes */
.admin-container {
    min-height: 100vh;
    padding: 2rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.admin-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* ===== HEADER FIXES ===== */

/* Consistent header styling across all admin pages */
.nibrass-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0) !important;
    color: white;
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    box-shadow: 0 15px 35px rgba(74, 144, 226, 0.3);
    position: relative;
    overflow: hidden;
}

.nibrass-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Title positioning and spacing fixes */
.nibrass-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    margin-top: 0;
    position: relative;
    z-index: 2;
}

.nibrass-header p {
    opacity: 0.9;
    margin: 0;
    position: relative;
    z-index: 2;
}

/* ===== SECTION FIXES ===== */

/* Consistent section styling and spacing */
.admin-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.admin-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4a90e2, #2c5aa0);
}

/* Section title fixes */
.section-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1.5rem;
    margin-top: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
}

/* ===== FORM FIXES ===== */

/* Form grid and spacing fixes */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.search-filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1rem;
    align-items: end;
}

/* Form controls consistency */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: block;
}

/* ===== CARD FIXES ===== */

/* Consistent card styling */
.nibrass-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.nibrass-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.nibrass-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.nibrass-card-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-left: 1rem;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

/* ===== GRID FIXES ===== */

/* Grid alignment and spacing fixes */
.videos-grid, .stories-grid, .resources-grid, .achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.achievements-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

/* ===== BUTTON FIXES ===== */

/* Consistent button styling */
.nibrass-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.nibrass-btn:hover {
    transform: translateY(-2px);
    text-decoration: none;
}

.nibrass-btn-primary {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white;
}

.nibrass-btn-primary:hover {
    background: linear-gradient(135deg, #2c5aa0, #1e3a5f);
    color: white;
}

.nibrass-btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.nibrass-btn-success:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    color: white;
}

.nibrass-btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.nibrass-btn-warning:hover {
    background: linear-gradient(135deg, #fd7e14, #e55a00);
    color: white;
}

.nibrass-btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.nibrass-btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
    color: white;
}

.nibrass-btn-outline-light {
    background: transparent;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.nibrass-btn-outline-light:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    border-color: rgba(255,255,255,0.5);
}

/* ===== ALERT FIXES ===== */

/* Alert styling consistency */
.alert {
    border-radius: 10px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f1b0b7);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

/* ===== RESPONSIVE FIXES ===== */

/* Mobile responsiveness fixes */
@media (max-width: 768px) {
    .admin-content {
        padding: 0 0.5rem;
    }
    
    .nibrass-header {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .nibrass-header h1 {
        font-size: 1.5rem;
    }
    
    .admin-section {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .search-filters {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .videos-grid, .stories-grid, .resources-grid, .achievements-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .nibrass-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* ===== ANIMATION FIXES ===== */

/* Smooth animations for better UX */
.admin-section, .nibrass-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* ===== UTILITY FIXES ===== */

/* Text alignment and spacing utilities */
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-left { text-align: left !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.py-4 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }
.py-5 { padding-top: 3rem !important; padding-bottom: 3rem !important; }

/* Color utilities */
.text-muted { color: #6c757d !important; }
.text-primary { color: #4a90e2 !important; }
.text-success { color: #28a745 !important; }
.text-warning { color: #ffc107 !important; }
.text-danger { color: #dc3545 !important; }
