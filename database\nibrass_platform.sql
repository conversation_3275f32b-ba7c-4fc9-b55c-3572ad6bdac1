-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 20, 2025 at 12:08 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.0.30

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `nibrass_platform`
--

-- --------------------------------------------------------

--
-- Table structure for table `achievements`
--

CREATE TABLE `achievements` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(100) DEFAULT 'fas fa-trophy',
  `badge_image` varchar(500) DEFAULT NULL,
  `category` enum('تعليمي','إبداعي','اجتماعي','رياضي','تقني','أخلاقي','عام') DEFAULT 'عام',
  `type` enum('نقاط','مهمة','وقت','تكرار','مستوى') DEFAULT 'نقاط',
  `difficulty_level` enum('easy','medium','hard','expert') DEFAULT 'easy',
  `points_required` int(11) DEFAULT 0,
  `tasks_required` int(11) DEFAULT 1,
  `time_required` int(11) DEFAULT 0,
  `repetitions_required` int(11) DEFAULT 1,
  `level_required` int(11) DEFAULT 1,
  `reward_points` int(11) DEFAULT 10,
  `reward_badge` varchar(255) DEFAULT NULL,
  `reward_certificate` tinyint(1) DEFAULT 0,
  `reward_gift` varchar(255) DEFAULT NULL,
  `target_age_min` int(11) DEFAULT 3,
  `target_age_max` int(11) DEFAULT 18,
  `prerequisites` text DEFAULT NULL,
  `unlock_conditions` text DEFAULT NULL,
  `is_hidden` tinyint(1) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `display_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `achievements`
--

INSERT INTO `achievements` (`id`, `title`, `description`, `icon`, `badge_image`, `category`, `type`, `difficulty_level`, `points_required`, `tasks_required`, `time_required`, `repetitions_required`, `level_required`, `reward_points`, `reward_badge`, `reward_certificate`, `reward_gift`, `target_age_min`, `target_age_max`, `prerequisites`, `unlock_conditions`, `is_hidden`, `is_active`, `display_order`, `created_at`, `updated_at`) VALUES
(1, 'أول خطوة', 'مبروك! لقد سجلت دخولك لأول مرة إلى منصة نبراس', 'fas fa-baby', NULL, 'عام', 'مهمة', 'easy', 0, 1, 0, 1, 1, 10, 'شارة البداية', 0, NULL, 3, 18, NULL, 'تسجيل الدخول لأول مرة', 0, 1, 1, '2025-07-15 22:09:46', '2025-07-15 22:09:46'),
(2, 'عاشق الرياضيات', 'أكمل 10 أنشطة رياضية بنجاح', 'fas fa-calculator', NULL, 'تعليمي', 'تكرار', 'easy', 0, 1, 0, 10, 1, 50, 'شارة الرياضيات', 0, NULL, 5, 12, NULL, 'إكمال 10 أنشطة رياضية', 0, 1, 2, '2025-07-15 22:09:46', '2025-07-15 22:09:46'),
(3, 'قارئ نهم', 'اقرأ 5 قصص تفاعلية كاملة', 'fas fa-book-reader', NULL, 'تعليمي', 'تكرار', 'easy', 0, 1, 0, 5, 1, 30, 'شارة القراءة', 1, NULL, 4, 15, NULL, 'قراءة 5 قصص كاملة', 0, 1, 3, '2025-07-15 22:09:46', '2025-07-15 22:09:46'),
(4, 'فنان مبدع', 'أنشئ 3 رسومات في استوديو الرسم', 'fas fa-palette', NULL, 'إبداعي', 'تكرار', 'easy', 0, 1, 0, 3, 1, 25, 'شارة الفن', 0, NULL, 3, 16, NULL, 'إنشاء 3 رسومات', 0, 1, 4, '2025-07-15 22:09:46', '2025-07-15 22:09:46'),
(5, 'جامع النقاط', 'اجمع 100 نقطة من الأنشطة المختلفة', 'fas fa-coins', NULL, 'عام', 'نقاط', 'medium', 100, 1, 0, 1, 1, 20, 'شارة النقاط', 0, NULL, 5, 18, NULL, 'جمع 100 نقطة', 0, 1, 5, '2025-07-15 22:09:46', '2025-07-15 22:09:46'),
(6, 'عالم صغير', 'شاهد 10 فيديوهات علمية تعليمية', 'fas fa-microscope', NULL, 'تعليمي', 'تكرار', 'medium', 0, 1, 0, 10, 1, 40, 'شارة العلوم', 1, NULL, 6, 16, NULL, 'مشاهدة 10 فيديوهات علمية', 0, 1, 6, '2025-07-15 22:09:46', '2025-07-15 22:09:46'),
(7, 'صديق مساعد', 'ساعد زميلاً في حل مشكلة أو نشاط', 'fas fa-hands-helping', NULL, 'اجتماعي', 'مهمة', 'medium', 0, 1, 0, 1, 1, 35, 'شارة المساعدة', 0, NULL, 5, 18, NULL, 'مساعدة زميل', 0, 1, 7, '2025-07-15 22:09:46', '2025-07-15 22:09:46'),
(8, 'مثابر لا يستسلم', 'أكمل نشاطاً صعباً بعد 3 محاولات', 'fas fa-mountain', NULL, 'أخلاقي', 'مهمة', 'hard', 0, 1, 0, 1, 1, 60, 'شارة المثابرة', 1, NULL, 7, 18, NULL, 'إكمال نشاط صعب بعد محاولات متعددة', 0, 1, 8, '2025-07-15 22:09:46', '2025-07-15 22:09:46'),
(9, 'نجم الأسبوع', 'كن الأكثر نشاطاً لمدة أسبوع كامل', 'fas fa-star', NULL, 'عام', 'وقت', 'hard', 0, 1, 7, 1, 1, 100, 'شارة النجم', 1, 'هدية خاصة', 5, 18, NULL, 'النشاط اليومي لمدة أسبوع', 0, 1, 9, '2025-07-15 22:09:46', '2025-07-15 22:09:46'),
(10, 'خبير التقنية', 'استخدم جميع ميزات المنصة التفاعلية', 'fas fa-laptop-code', NULL, 'تقني', 'مهمة', 'expert', 0, 1, 0, 1, 1, 150, 'شارة التقنية', 1, NULL, 8, 18, NULL, 'استخدام جميع ميزات المنصة', 0, 1, 10, '2025-07-15 22:09:46', '2025-07-15 22:09:46');

-- --------------------------------------------------------

--
-- Table structure for table `activities`
--

CREATE TABLE `activities` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `category` enum('math','language','science','art','social') DEFAULT 'math',
  `difficulty_level` enum('easy','medium','hard') DEFAULT 'easy',
  `target_age` int(11) DEFAULT 5,
  `duration_minutes` int(11) DEFAULT 15,
  `points_reward` int(11) DEFAULT 10,
  `rating` decimal(2,1) DEFAULT 5.0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `activities`
--

INSERT INTO `activities` (`id`, `title`, `description`, `category`, `difficulty_level`, `target_age`, `duration_minutes`, `points_reward`, `rating`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'لعبة الرياضيات المرحة', 'تعلم الجمع والطرح بطريقة ممتعة وتفاعلية', 'math', 'easy', 5, 15, 50, 5.0, 1, '2025-07-15 21:38:21', '2025-07-15 21:38:21'),
(2, 'عالم الحروف والكلمات', 'تعلم الحروف والكلمات بطريقة شيقة', 'language', 'easy', 4, 20, 40, 4.5, 1, '2025-07-15 21:38:21', '2025-07-15 21:38:21'),
(3, 'اكتشاف العلوم', 'تجارب علمية بسيطة وممتعة للأطفال', 'science', 'medium', 6, 25, 60, 4.8, 1, '2025-07-15 21:38:21', '2025-07-15 21:38:21'),
(4, 'الرسم والتلوين', 'أطلق إبداعك في الرسم والتلوين', 'art', 'easy', 3, 30, 35, 4.2, 1, '2025-07-15 21:38:21', '2025-07-15 21:38:21');

-- --------------------------------------------------------

--
-- Table structure for table `ai_chat_messages`
--

CREATE TABLE `ai_chat_messages` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `sender` enum('user','ai') NOT NULL,
  `session_id` varchar(100) DEFAULT NULL,
  `tokens_used` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ai_chat_messages`
--

INSERT INTO `ai_chat_messages` (`id`, `user_id`, `message`, `sender`, `session_id`, `tokens_used`, `created_at`) VALUES
(1, 3, 'كيف يمكنني تحسين التواصل مع طفلي؟', 'user', NULL, NULL, '2025-07-13 11:03:51'),
(2, 3, 'تحسين التواصل مع طفلك أمر مهم جداً. إليك بعض النصائح:\\n\\n1. استخدم لغة بسيطة وواضحة\\n2. امنح طفلك وقتاً كافياً للاستجابة\\n3. استخدم الإشارات البصرية والصور\\n4. كن صبوراً ومتفهماً\\n5. احتفل بكل تقدم مهما كان صغيراً\\n\\nهل تريد المزيد من التفاصيل حول أي من هذه النقاط؟', 'ai', NULL, NULL, '2025-07-13 11:03:51'),
(3, 3, 'شكرا لك', 'user', NULL, NULL, '2025-07-13 11:03:56'),
(4, 3, 'عفوًا يا أحمد، أنا هنا لأقدم لك كل الدعم والمساعدة التي تحتاجها. إذا كان لديك أي أسئلة أو استفسارات حول اضطراب طيف التوحد أو أي تحديات تواجهها مع طفلك، فلا تتردد في طرحها. أنا هنا لأقدم لك النصائح والموارد التي قد تساعدك في رحلتك. تذكر أنك لست وحدك، وهناك دائمًا حلول وإمكانيات للتحسين. كيف يمكنني مساعدتك اليوم؟', 'ai', NULL, NULL, '2025-07-13 11:04:00'),
(5, 3, 'من انت', 'user', NULL, NULL, '2025-07-13 11:04:07'),
(6, 3, 'مرحباً أحمد محمد، أنا \"نبراس\"، مساعدك الذكي المتخصص في دعم أولياء أمور الأطفال ذوي اضطراب طيف التوحد. أنا جزء من منصة نبراس التعليمية، وأنا هنا لتقديم المشورة والدعم لك في رحلتك مع طفلك. يمكنني مساعدتك في:\n\n- الإجابة على أسئلتك حول اضطراب طيف التوحد\n- تقديم نصائح تربوية وتعليمية\n- توجيهك إلى الموارد المناسبة\n- تقديم الدعم العاطفي والمعنوي\n\nإذا كان لديك أي سؤال أو تحتاج إلى مساعدة، فلا تتردد في طرحه. أنا هنا لدعمك. 😊', 'ai', NULL, NULL, '2025-07-13 11:04:13'),
(7, 11, 'كيف اعرض ان ابني مصاب بالتوحد', 'user', NULL, NULL, '2025-07-15 22:48:56'),
(8, 11, 'اضطراب طيف التوحد هو حالة نمائية تؤثر على التواصل والتفاعل الاجتماعي. كل طفل فريد وله نقاط قوة خاصة به. المهم هو فهم احتياجات طفلك وتوفير الدعم المناسب له.', 'ai', NULL, NULL, '2025-07-15 22:48:56'),
(9, 11, 'شكرا لك', 'user', NULL, NULL, '2025-07-15 22:49:05'),
(10, 11, 'عذراً أحمد الأب، هناك مشكلة في المصادقة مع الخدمة الذكية. يرجى المحاولة لاحقاً أو التواصل مع الدعم التقني.', 'ai', NULL, NULL, '2025-07-15 22:49:06');

-- --------------------------------------------------------

--
-- Table structure for table `appointments`
--

CREATE TABLE `appointments` (
  `id` int(11) NOT NULL,
  `consultation_id` int(11) DEFAULT NULL,
  `parent_id` int(11) NOT NULL,
  `specialist_id` int(11) NOT NULL,
  `student_id` int(11) DEFAULT NULL,
  `appointment_type` enum('consultation','follow_up','assessment','therapy') NOT NULL DEFAULT 'consultation',
  `appointment_date` datetime NOT NULL,
  `duration_minutes` int(11) NOT NULL DEFAULT 60,
  `location` varchar(255) DEFAULT NULL,
  `meeting_link` varchar(500) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('scheduled','confirmed','completed','cancelled','no_show') NOT NULL DEFAULT 'scheduled',
  `reminder_sent` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `appointments`
--

INSERT INTO `appointments` (`id`, `consultation_id`, `parent_id`, `specialist_id`, `student_id`, `appointment_type`, `appointment_date`, `duration_minutes`, `location`, `meeting_link`, `notes`, `status`, `reminder_sent`, `created_at`, `updated_at`) VALUES
(1, NULL, 11, 7, 2, 'consultation', '2025-07-16 03:05:08', 60, NULL, NULL, NULL, 'cancelled', 0, '2025-07-13 02:05:08', '2025-07-15 22:52:40');

-- --------------------------------------------------------

--
-- Table structure for table `articles`
--

CREATE TABLE `articles` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `author_id` int(11) NOT NULL,
  `category` varchar(100) DEFAULT NULL,
  `featured_image` varchar(255) DEFAULT NULL,
  `is_published` tinyint(1) DEFAULT 0,
  `views` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `articles`
--

INSERT INTO `articles` (`id`, `title`, `content`, `author_id`, `category`, `featured_image`, `is_published`, `views`, `created_at`, `updated_at`) VALUES
(1, 'فهم اضطراب طيف التوحد', 'اضطراب طيف التوحد هو حالة نمائية تؤثر على التواصل والتفاعل الاجتماعي. يتميز بأنماط سلوكية متكررة واهتمامات محدودة. من المهم فهم أن كل طفل مصاب بالتوحد فريد من نوعه وله نقاط قوة وتحديات مختلفة.', 1, 'تعليمي', NULL, 1, 0, '2025-07-12 18:09:58', '2025-07-12 18:09:58'),
(2, 'استراتيجيات التواصل مع الأطفال ذوي التوحد', 'التواصل الفعال مع الأطفال ذوي التوحد يتطلب صبراً وفهماً. استخدم لغة بسيطة وواضحة، وامنح الطفل وقتاً كافياً للاستجابة. الإشارات البصرية والروتين المنتظم يمكن أن يساعدا كثيراً في تحسين التواصل.', 1, 'نصائح', NULL, 1, 0, '2025-07-12 18:09:58', '2025-07-12 18:09:58'),
(3, 'أهمية اللعب في تطوير مهارات الأطفال', 'اللعب ليس مجرد متعة للأطفال، بل هو وسيلة مهمة للتعلم وتطوير المهارات. للأطفال ذوي التوحد، يمكن أن يساعد اللعب المنظم في تطوير المهارات الاجتماعية والحركية والمعرفية.', 1, 'تطوير المهارات', NULL, 1, 0, '2025-07-12 18:09:58', '2025-07-12 18:09:58');

-- --------------------------------------------------------

--
-- Table structure for table `chat_history`
--

CREATE TABLE `chat_history` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `user_message` text NOT NULL,
  `ai_response` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `consultations`
--

CREATE TABLE `consultations` (
  `id` int(11) NOT NULL,
  `parent_id` int(11) NOT NULL,
  `student_id` int(11) DEFAULT NULL,
  `specialist_id` int(11) DEFAULT NULL,
  `consultation_type` enum('behavioral','educational','medical','developmental') NOT NULL,
  `urgency_level` enum('urgent','normal','routine') NOT NULL DEFAULT 'normal',
  `subject` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `preferred_date` date DEFAULT NULL,
  `preferred_time` enum('morning','afternoon','evening') DEFAULT NULL,
  `status` enum('pending','assigned','scheduled','in_progress','completed','cancelled') NOT NULL DEFAULT 'pending',
  `assigned_at` timestamp NULL DEFAULT NULL,
  `scheduled_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `consultations`
--

INSERT INTO `consultations` (`id`, `parent_id`, `student_id`, `specialist_id`, `consultation_type`, `urgency_level`, `subject`, `description`, `preferred_date`, `preferred_time`, `status`, `assigned_at`, `scheduled_at`, `completed_at`, `created_at`, `updated_at`) VALUES
(1, 11, 2, 7, 'behavioral', 'normal', 'صعوبة في التواصل الاجتماعي', 'طفلي يواجه صعوبة في التفاعل مع الأطفال الآخرين في المدرسة ولا يشارك في الأنشطة الجماعية', NULL, NULL, 'assigned', '2025-07-15 22:52:13', NULL, NULL, '2025-07-13 02:05:07', '2025-07-15 22:52:13'),
(2, 3, 4, 4, 'educational', 'normal', 'ن دعني أتحقق من أن المل', 'أضيف أيضاً طريقة بد', '2025-07-22', 'morning', 'pending', NULL, NULL, NULL, '2025-07-13 12:10:27', '2025-07-13 12:10:27');

-- --------------------------------------------------------

--
-- Table structure for table `consultation_attachments`
--

CREATE TABLE `consultation_attachments` (
  `id` int(11) NOT NULL,
  `consultation_id` int(11) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) NOT NULL DEFAULT 0,
  `mime_type` varchar(100) DEFAULT NULL,
  `uploaded_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `consultation_notifications`
--

CREATE TABLE `consultation_notifications` (
  `id` int(11) NOT NULL,
  `consultation_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `notification_type` enum('new_consultation','response_received','appointment_scheduled','appointment_reminder','status_update') NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `consultation_ratings`
--

CREATE TABLE `consultation_ratings` (
  `id` int(11) NOT NULL,
  `consultation_id` int(11) NOT NULL,
  `parent_id` int(11) NOT NULL,
  `specialist_id` int(11) NOT NULL,
  `rating` int(11) NOT NULL CHECK (`rating` >= 1 and `rating` <= 5),
  `feedback` text DEFAULT NULL,
  `would_recommend` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `consultation_responses`
--

CREATE TABLE `consultation_responses` (
  `id` int(11) NOT NULL,
  `consultation_id` int(11) NOT NULL,
  `specialist_id` int(11) NOT NULL,
  `response_text` text NOT NULL,
  `is_private` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `educational_videos`
--

CREATE TABLE `educational_videos` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `thumbnail` varchar(500) DEFAULT NULL,
  `video_url` varchar(500) DEFAULT NULL,
  `duration` varchar(20) DEFAULT NULL,
  `category` enum('لغة عربية','رياضيات','علوم','فنون','دينية','عامة') DEFAULT 'عامة',
  `difficulty_level` enum('easy','medium','hard') DEFAULT 'easy',
  `target_age` int(11) DEFAULT 6,
  `views_count` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `educational_videos`
--

INSERT INTO `educational_videos` (`id`, `title`, `description`, `thumbnail`, `video_url`, `duration`, `category`, `difficulty_level`, `target_age`, `views_count`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'تعلم الحروف العربية', 'فيديو تعليمي ممتع لتعلم الحروف العربية بالصوت والصورة. يساعد الأطفال على تعلم نطق الحروف وكتابتها بطريقة تفاعلية.', 'images/videos/arabic-letters.jpg', 'https://www.youtube.com/embed/dQw4w9WgXcQ', '15:30', 'لغة عربية', 'easy', 5, 1250, 1, '2025-07-15 21:55:06', '2025-07-15 21:55:06'),
(2, 'الأرقام والعد من 1 إلى 10', 'تعلم الأرقام من 1 إلى 10 بطريقة تفاعلية ومسلية مع الأغاني والألعاب التعليمية.', 'images/videos/numbers.jpg', 'https://www.youtube.com/embed/dQw4w9WgXcQ', '12:45', 'رياضيات', 'easy', 4, 980, 1, '2025-07-15 21:55:06', '2025-07-15 21:55:06'),
(3, 'الألوان والأشكال الهندسية', 'اكتشف عالم الألوان والأشكال الهندسية الأساسية مع شخصيات كرتونية محببة للأطفال.', 'images/videos/colors-shapes.jpg', 'https://www.youtube.com/embed/dQw4w9WgXcQ', '18:20', 'فنون', 'easy', 3, 1500, 1, '2025-07-15 21:55:06', '2025-07-15 21:55:06'),
(4, 'عالم الحيوانات وأصواتها', 'رحلة ممتعة لاكتشاف الحيوانات المختلفة وأصواتها وبيئاتها الطبيعية.', 'images/videos/animals.jpg', 'https://www.youtube.com/embed/dQw4w9WgXcQ', '20:15', 'علوم', 'medium', 6, 2100, 1, '2025-07-15 21:55:06', '2025-07-15 21:55:06'),
(5, 'قصص الأنبياء للأطفال', 'قصص تعليمية هادفة من سير الأنبياء عليهم السلام مقدمة بأسلوب مناسب للأطفال.', 'images/videos/prophets.jpg', 'https://www.youtube.com/embed/dQw4w9WgXcQ', '25:00', 'دينية', 'medium', 8, 1800, 1, '2025-07-15 21:55:06', '2025-07-15 21:55:06'),
(6, 'تعلم الجمع والطرح', 'تعلم عمليات الجمع والطرح البسيطة باستخدام الألعاب والأمثلة التفاعلية.', 'images/videos/math-operations.jpg', 'https://www.youtube.com/embed/dQw4w9WgXcQ', '22:10', 'رياضيات', 'medium', 7, 1650, 1, '2025-07-15 21:55:06', '2025-07-15 21:55:06'),
(7, 'الفصول الأربعة', 'تعرف على الفصول الأربعة وخصائص كل فصل والتغيرات التي تحدث في الطبيعة.', 'images/videos/seasons.jpg', 'https://www.youtube.com/embed/dQw4w9WgXcQ', '16:40', 'علوم', 'easy', 5, 1320, 1, '2025-07-15 21:55:06', '2025-07-15 21:55:06'),
(8, 'الرسم والتلوين للمبتدئين', 'تعلم أساسيات الرسم والتلوين مع تقنيات بسيطة ومناسبة للأطفال.', 'images/videos/drawing.jpg', 'https://www.youtube.com/embed/dQw4w9WgXcQ', '19:30', 'فنون', 'easy', 6, 1100, 1, '2025-07-15 21:55:06', '2025-07-15 21:55:06'),
(9, 'آداب الطعام والشراب', 'تعلم آداب الطعام والشراب في الإسلام بطريقة تفاعلية ومناسبة للأطفال.', 'images/videos/eating-manners.jpg', 'https://www.youtube.com/embed/dQw4w9WgXcQ', '14:20', 'دينية', 'easy', 5, 950, 1, '2025-07-15 21:55:06', '2025-07-15 21:55:06'),
(10, 'تعلم كتابة الحروف', 'تعلم كتابة الحروف العربية خطوة بخطوة مع التدريب على الخط الصحيح.', 'images/videos/writing.jpg', 'https://www.youtube.com/embed/dQw4w9WgXcQ', '28:15', 'لغة عربية', 'medium', 7, 1750, 1, '2025-07-15 21:55:06', '2025-07-15 21:55:06');

-- --------------------------------------------------------

--
-- Table structure for table `feedback`
--

CREATE TABLE `feedback` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` enum('question','consultation','feedback','complaint') DEFAULT 'question',
  `status` enum('pending','answered','closed') DEFAULT 'pending',
  `admin_response` text DEFAULT NULL,
  `responded_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `media`
--

CREATE TABLE `media` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_type` enum('image','audio','video','document','pdf') NOT NULL,
  `category` varchar(100) DEFAULT NULL,
  `uploaded_by` int(11) NOT NULL,
  `file_size` int(11) DEFAULT NULL,
  `downloads` int(11) DEFAULT 0,
  `is_public` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `media`
--

INSERT INTO `media` (`id`, `title`, `description`, `file_path`, `file_type`, `category`, `uploaded_by`, `file_size`, `downloads`, `is_public`, `created_at`) VALUES
(1, 'دليل الأولياء للتوحد', 'دليل شامل للأولياء حول كيفية التعامل مع الطفل ذي التوحد', 'uploads/documents/autism_parents_guide.pdf', 'pdf', 'أدلة', 1, 2048000, 0, 1, '2025-07-12 18:09:58'),
(2, 'أنشودة الألوان', 'أنشودة تعليمية لتعلم الألوان بطريقة ممتعة', 'uploads/audio/colors_song.mp3', 'audio', 'أناشيد', 1, 5120000, 0, 1, '2025-07-12 18:09:58'),
(3, 'فيديو تعليمي: المهارات الاجتماعية', 'فيديو يوضح كيفية تطوير المهارات الاجتماعية', 'uploads/video/social_skills.mp4', 'video', 'فيديوهات تعليمية', 1, 15360000, 0, 1, '2025-07-12 18:09:58');

-- --------------------------------------------------------

--
-- Table structure for table `news`
--

CREATE TABLE `news` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `news`
--

INSERT INTO `news` (`id`, `title`, `content`, `is_active`, `created_at`) VALUES
(1, 'مرحباً بكم في منصة نبراس', 'نرحب بكم في منصة نبراس التعليمية المتخصصة في دعم الأطفال ذوي اضطراب طيف التوحد', 1, '2025-07-12 17:46:05'),
(2, 'مرحباً بكم في منصة نبراس', 'نرحب بكم في منصة نبراس التعليمية المتخصصة في دعم الأطفال ذوي اضطراب طيف التوحد', 1, '2025-07-12 18:09:45'),
(3, 'ورشة تدريبية جديدة حول التعامل مع السلوكيات التحدية - التسجيل مفتوح الآن', 'ورشة تدريبية جديدة حول التعامل مع السلوكيات التحدية - التسجيل مفتوح الآن', 1, '2025-07-12 18:09:58'),
(4, 'إطلاق مكتبة الكتب الصوتية المتخصصة في التوحد', 'إطلاق مكتبة الكتب الصوتية المتخصصة في التوحد', 1, '2025-07-12 18:09:58'),
(5, 'مؤتمر نبراس السنوي للتوحد - 15 ديسمبر 2024', 'مؤتمر نبراس السنوي للتوحد - 15 ديسمبر 2024', 1, '2025-07-12 18:09:58'),
(6, 'تطبيق نبراس الجديد متاح الآن على متجر التطبيقات', 'تطبيق نبراس الجديد متاح الآن على متجر التطبيقات', 1, '2025-07-12 18:09:58'),
(7, 'برنامج الدعم النفسي للأسر - جلسات مجانية كل أسبوع', 'برنامج الدعم النفسي للأسر - جلسات مجانية كل أسبوع', 1, '2025-07-12 18:09:58'),
(8, 'مرحباً بكم في منصة نبراس', 'نرحب بكم في منصة نبراس التعليمية المتخصصة في دعم الأطفال ذوي اضطراب طيف التوحد', 1, '2025-07-13 01:35:42');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` enum('info','success','warning','error','announcement') DEFAULT 'info',
  `target_audience` enum('all','parents','students','specialists','admins') DEFAULT 'all',
  `priority` enum('low','medium','high','urgent') DEFAULT 'medium',
  `is_active` tinyint(1) DEFAULT 1,
  `start_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `end_date` timestamp NULL DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `title`, `message`, `type`, `target_audience`, `priority`, `is_active`, `start_date`, `end_date`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'مرحباً بكم في منصة نبراس', 'نرحب بكم في منصة نبراس التعليمية المتخصصة في دعم الأطفال ذوي الاحتياجات الخاصة', 'announcement', 'all', 'high', 1, '2025-07-15 21:38:21', NULL, NULL, '2025-07-15 21:38:21', '2025-07-15 21:38:21'),
(2, 'تحديث جديد للمنصة', 'تم إضافة ميزات جديدة لتحسين تجربة التعلم', 'info', 'all', 'medium', 1, '2025-07-15 21:38:21', NULL, NULL, '2025-07-15 21:38:21', '2025-07-15 21:38:21'),
(3, 'نصائح للآباء', 'تذكروا أن التشجيع والصبر هما مفتاح نجاح أطفالكم في التعلم', 'info', 'parents', 'low', 1, '2025-07-15 21:38:21', NULL, NULL, '2025-07-15 21:38:21', '2025-07-15 21:38:21');

-- --------------------------------------------------------

--
-- Table structure for table `platform_settings`
--

CREATE TABLE `platform_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(255) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('string','number','boolean','json') DEFAULT 'string',
  `category` varchar(100) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `platform_settings`
--

INSERT INTO `platform_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `category`, `description`, `is_public`, `created_at`, `updated_at`) VALUES
(1, 'platform_name', 'منصة نبراس التعليمية', 'string', 'general', 'اسم المنصة', 1, '2025-07-15 21:38:21', '2025-07-15 21:38:21'),
(2, 'platform_description', 'منصة تعليمية متخصصة في دعم الأطفال ذوي الاحتياجات الخاصة', 'string', 'general', 'وصف المنصة', 1, '2025-07-15 21:38:21', '2025-07-15 21:38:21'),
(3, 'platform_version', '1.0.0', 'string', 'system', 'إصدار المنصة', 0, '2025-07-15 21:38:21', '2025-07-15 21:38:21'),
(4, 'maintenance_mode', 'true', 'boolean', 'system', 'وضع الصيانة', 0, '2025-07-15 21:38:21', '2025-07-15 22:56:16'),
(5, 'registration_enabled', 'true', 'boolean', 'user', 'تفعيل التسجيل الجديد', 0, '2025-07-15 21:38:21', '2025-07-15 21:38:21'),
(6, 'contact_email', '<EMAIL>', 'string', 'contact', 'البريد الإلكتروني للتواصل', 1, '2025-07-15 21:38:21', '2025-07-15 21:38:21'),
(7, 'support_phone', '+966123456789', 'string', 'contact', 'رقم الهاتف للدعم', 1, '2025-07-15 21:38:21', '2025-07-15 21:38:21'),
(8, 'site_name', 'نبراس', 'string', 'general', NULL, 0, '2025-07-15 22:56:16', '2025-07-15 22:56:16'),
(9, 'site_description', 'منصة تعليمية تفاعلية للأطفال', 'string', 'general', NULL, 0, '2025-07-15 22:56:16', '2025-07-15 22:56:16'),
(11, 'contact_phone', '+966123456789', 'string', 'general', NULL, 0, '2025-07-15 22:56:16', '2025-07-15 22:56:16'),
(12, 'max_file_upload_size', '10485760', 'string', 'general', NULL, 0, '2025-07-15 22:56:16', '2025-07-15 22:56:16'),
(13, 'session_timeout', '1440', 'string', 'general', NULL, 0, '2025-07-15 22:56:16', '2025-07-15 22:56:16'),
(15, 'user_registration', 'true', 'string', 'general', NULL, 0, '2025-07-15 22:56:16', '2025-07-15 22:56:16'),
(16, 'ai_chat_enabled', 'true', 'string', 'general', NULL, 0, '2025-07-15 22:56:16', '2025-07-15 22:56:16'),
(17, 'notifications_enabled', 'true', 'string', 'general', NULL, 0, '2025-07-15 22:56:16', '2025-07-15 22:56:16'),
(18, 'email_notifications', 'false', 'string', 'general', NULL, 0, '2025-07-15 22:56:16', '2025-07-15 22:56:16'),
(19, 'auto_backup', 'true', 'string', 'general', NULL, 0, '2025-07-15 22:56:16', '2025-07-15 22:56:16');

-- --------------------------------------------------------

--
-- Table structure for table `progress_reports`
--

CREATE TABLE `progress_reports` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `specialist_id` int(11) NOT NULL,
  `report_period_start` date NOT NULL,
  `report_period_end` date NOT NULL,
  `overall_progress` enum('excellent','good','satisfactory','needs_improvement') NOT NULL,
  `behavioral_notes` text DEFAULT NULL,
  `educational_notes` text DEFAULT NULL,
  `social_notes` text DEFAULT NULL,
  `recommendations` text DEFAULT NULL,
  `goals_for_next_period` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `stories`
--

CREATE TABLE `stories` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `content` longtext DEFAULT NULL,
  `thumbnail` varchar(500) DEFAULT NULL,
  `category` enum('تعليمية','مغامرات','خيالية','علمية','دينية','أخلاقية','عامة') DEFAULT 'عامة',
  `difficulty_level` enum('easy','medium','hard') DEFAULT 'easy',
  `target_age` int(11) DEFAULT 6,
  `duration_minutes` int(11) DEFAULT 10,
  `reading_level` varchar(50) DEFAULT 'مبتدئ',
  `language` varchar(10) DEFAULT 'ar',
  `author` varchar(255) DEFAULT NULL,
  `illustrator` varchar(255) DEFAULT NULL,
  `moral_lesson` text DEFAULT NULL,
  `keywords` text DEFAULT NULL,
  `is_interactive` tinyint(1) DEFAULT 1,
  `has_audio` tinyint(1) DEFAULT 0,
  `audio_url` varchar(500) DEFAULT NULL,
  `views_count` int(11) DEFAULT 0,
  `likes_count` int(11) DEFAULT 0,
  `is_featured` tinyint(1) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `stories`
--

INSERT INTO `stories` (`id`, `title`, `description`, `content`, `thumbnail`, `category`, `difficulty_level`, `target_age`, `duration_minutes`, `reading_level`, `language`, `author`, `illustrator`, `moral_lesson`, `keywords`, `is_interactive`, `has_audio`, `audio_url`, `views_count`, `likes_count`, `is_featured`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'الأرنب الذكي والثعلب المكار', 'قصة مشوقة عن أرنب صغير ذكي يتعلم كيف يتعامل مع الثعلب المكار ويحمي نفسه بالذكاء والحكمة', 'في غابة جميلة، كان يعيش أرنب صغير ذكي اسمه أرنوب. كان أرنوب يحب اللعب والقفز بين الأشجار. في يوم من الأيام، قابل ثعلباً مكاراً يريد أن يخدعه. لكن أرنوب كان ذكياً جداً، فاستخدم عقله وتمكن من الهروب من الثعلب. تعلم أرنوب أن الذكاء أقوى من القوة، وأن التفكير الجيد يحل أي مشكلة.', 'images/stories/smart-rabbit.jpg', 'تعليمية', 'easy', 5, 8, 'مبتدئ', 'ar', 'فريق نبراس التعليمي', NULL, 'الذكاء والحكمة أهم من القوة', 'ذكاء، حكمة، صداقة، حيوانات', 1, 0, NULL, 1250, 98, 0, 1, '2025-07-15 22:03:29', '2025-07-15 22:03:29'),
(2, 'مغامرة سارة في الفضاء', 'رحلة مثيرة مع الطفلة سارة التي تحلم بأن تصبح رائدة فضاء وتكتشف الكواكب والنجوم', 'سارة طفلة صغيرة تحب النجوم والكواكب. في ليلة صافية، حلمت سارة أنها رائدة فضاء تسافر بين النجوم. زارت القمر ولعبت مع الفضائيين الودودين، وتعلمت عن الشمس والكواكب. عندما استيقظت، قررت سارة أن تدرس بجد لتحقق حلمها وتصبح رائدة فضاء حقيقية.', 'images/stories/space-adventure.jpg', 'علمية', 'medium', 7, 12, 'متوسط', 'ar', 'د. أحمد الفضائي', NULL, 'الأحلام تتحقق بالعمل والاجتهاد', 'فضاء، نجوم، أحلام، علوم', 1, 0, NULL, 980, 87, 0, 1, '2025-07-15 22:03:29', '2025-07-15 22:03:29'),
(3, 'الغابة السحرية وأصدقاء الطبيعة', 'قصة خيالية عن طفلة تكتشف غابة سحرية مليئة بالحيوانات المتكلمة والأشجار الحكيمة', 'لينا طفلة تحب الطبيعة. في يوم من الأيام، وجدت طريقاً سرياً يؤدي إلى غابة سحرية. في هذه الغابة، تتكلم الحيوانات وتغني الأشجار. التقت لينا بالدب الطيب والعصفور المرح والشجرة الحكيمة. تعلمت لينا أهمية المحافظة على البيئة وحب الطبيعة.', 'images/stories/magic-forest.jpg', 'خيالية', 'easy', 6, 10, 'مبتدئ', 'ar', 'نورا الخيال', NULL, 'المحافظة على البيئة واجب على الجميع', 'طبيعة، بيئة، حيوانات، سحر', 1, 0, NULL, 1500, 125, 0, 1, '2025-07-15 22:03:29', '2025-07-15 22:03:29'),
(4, 'البحث عن الكنز المفقود', 'مغامرة شيقة مع مجموعة من الأطفال يبحثون عن كنز مفقود ويتعلمون قيمة التعاون', 'أحمد وفاطمة وعلي أصدقاء يحبون المغامرات. وجدوا خريطة قديمة تؤدي إلى كنز مفقود. بدأوا رحلة البحث معاً، وواجهوا تحديات كثيرة. تعلموا أن التعاون والصداقة أهم من أي كنز، وأن العمل الجماعي يحقق المستحيل.', 'images/stories/treasure-hunt.jpg', 'مغامرات', 'medium', 8, 15, 'متوسط', 'ar', 'كابتن مغامر', NULL, 'التعاون والصداقة أغلى من أي كنز', 'مغامرة، تعاون، صداقة، كنز', 1, 0, NULL, 2100, 156, 0, 1, '2025-07-15 22:03:29', '2025-07-15 22:03:29'),
(5, 'قصة النبي يوسف عليه السلام', 'قصة تعليمية مبسطة عن النبي يوسف عليه السلام وصبره وحكمته', 'يوسف عليه السلام كان نبياً كريماً، أحبه والده يعقوب عليه السلام كثيراً. واجه يوسف تحديات كثيرة في حياته، لكنه صبر وتوكل على الله. بفضل صبره وحكمته، أصبح وزيراً في مصر وساعد الناس في سنوات الجفاف. تعلمنا من قصة يوسف أهمية الصبر والتوكل على الله.', 'images/stories/prophet-yusuf.jpg', 'دينية', 'medium', 9, 18, 'متوسط', 'ar', 'الشيخ محمد الحكيم', NULL, 'الصبر والتوكل على الله يؤديان إلى النجاح', 'أنبياء، صبر، حكمة، إيمان', 1, 0, NULL, 1800, 142, 0, 1, '2025-07-15 22:03:29', '2025-07-15 22:03:29'),
(6, 'الصدق طريق النجاح', 'قصة أخلاقية عن طفل يتعلم أهمية الصدق في جميع المواقف', 'خالد طفل طيب، لكنه أحياناً يقول أكاذيب صغيرة. في يوم من الأيام، كسر خالد إناء أمه بالخطأ، وخاف أن يخبرها الحقيقة. لكن ضميره لم يتركه مرتاحاً. قرر خالد أن يقول الصدق لأمه، فسامحته وشكرته على صدقه. تعلم خالد أن الصدق دائماً هو الطريق الصحيح.', 'images/stories/honesty.jpg', 'أخلاقية', 'easy', 6, 8, 'مبتدئ', 'ar', 'أستاذة سلمى', NULL, 'الصدق أساس الأخلاق الحميدة', 'صدق، أخلاق، ضمير، تربية', 1, 0, NULL, 1320, 108, 0, 1, '2025-07-15 22:03:29', '2025-07-15 22:03:29'),
(7, 'رحلة إلى عالم الديناصورات', 'مغامرة علمية مشوقة لاكتشاف عالم الديناصورات وتاريخ الأرض القديم', 'ماجد يحب الديناصورات كثيراً. في المتحف، وجد آلة زمن سحرية نقلته إلى عصر الديناصورات. التقى بديناصورات مختلفة: الطيبة والمفترسة. تعلم ماجد عن أنواع الديناصورات وكيف عاشت قديماً. عندما عاد إلى زمنه، قرر أن يصبح عالم آثار ليكتشف المزيد عن التاريخ.', 'images/stories/dinosaurs.jpg', 'علمية', 'medium', 8, 14, 'متوسط', 'ar', 'د. سامي الأثري', NULL, 'حب العلم والاستطلاع يفتح آفاق المعرفة', 'ديناصورات، تاريخ، علوم، اكتشاف', 1, 0, NULL, 1650, 134, 0, 1, '2025-07-15 22:03:29', '2025-07-15 22:03:29'),
(8, 'النملة المجتهدة والجندب الكسول', 'قصة تعليمية كلاسيكية عن أهمية العمل والاجتهاد مقابل الكسل', 'في حديقة جميلة، كانت تعيش نملة مجتهدة وجندب كسول. النملة تعمل طوال الصيف لتجمع الطعام للشتاء، بينما الجندب يلعب ويغني فقط. عندما جاء الشتاء البارد، كان لدى النملة طعام كثير، أما الجندب فكان جائعاً. ساعدت النملة الطيبة الجندب، وتعلم الجندب أهمية العمل والاجتهاد.', 'images/stories/ant-grasshopper.jpg', 'تعليمية', 'easy', 5, 7, 'مبتدئ', 'ar', 'حكايات الجدة', NULL, 'العمل والاجتهاد أساس النجاح في الحياة', 'عمل، اجتهاد، كسل، حكمة', 1, 0, NULL, 1100, 89, 0, 1, '2025-07-15 22:03:29', '2025-07-15 22:03:29');

-- --------------------------------------------------------

--
-- Table structure for table `students`
--

CREATE TABLE `students` (
  `id` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `age` int(11) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `parent_id` int(11) NOT NULL,
  `birth_date` date DEFAULT NULL,
  `diagnosis_level` enum('mild','moderate','severe') DEFAULT NULL,
  `interests` text DEFAULT NULL,
  `progress_level` int(11) DEFAULT 0,
  `weekly_stars` int(11) DEFAULT 0,
  `total_stars` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `students`
--

INSERT INTO `students` (`id`, `name`, `age`, `user_id`, `parent_id`, `birth_date`, `diagnosis_level`, `interests`, `progress_level`, `weekly_stars`, `total_stars`, `created_at`) VALUES
(2, 'محمد أحمد', 8, 12, 11, NULL, 'moderate', NULL, 75, 12, 156, '2025-07-13 02:05:07'),
(3, 'سارة أحمد', 6, 13, 11, NULL, 'mild', NULL, 85, 15, 203, '2025-07-13 02:05:07'),
(4, NULL, NULL, 14, 3, '2019-01-07', 'moderate', '', 0, 0, 0, '2025-07-13 11:22:37');

-- --------------------------------------------------------

--
-- Table structure for table `student_achievements`
--

CREATE TABLE `student_achievements` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `achievement_id` int(11) NOT NULL,
  `earned_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `progress_percentage` decimal(5,2) DEFAULT 0.00,
  `current_points` int(11) DEFAULT 0,
  `current_tasks` int(11) DEFAULT 0,
  `current_time_spent` int(11) DEFAULT 0,
  `current_repetitions` int(11) DEFAULT 0,
  `is_completed` tinyint(1) DEFAULT 0,
  `completion_date` timestamp NULL DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `student_activities`
--

CREATE TABLE `student_activities` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `activity_id` int(11) DEFAULT NULL,
  `activity_type` enum('game','exercise','assessment','therapy_session') NOT NULL,
  `activity_title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `score` int(11) DEFAULT NULL,
  `max_score` int(11) DEFAULT NULL,
  `duration_minutes` int(11) DEFAULT NULL,
  `completed_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `student_activities`
--

INSERT INTO `student_activities` (`id`, `student_id`, `activity_id`, `activity_type`, `activity_title`, `description`, `score`, `max_score`, `duration_minutes`, `completed_at`, `created_at`) VALUES
(1, 2, NULL, 'game', 'لعبة الألوان', 'تعلم الألوان الأساسية', 85, 100, 20, '2025-07-11 02:05:08', '2025-07-13 02:05:08'),
(2, 2, NULL, 'exercise', 'تمرين التركيز', 'تمارين لتحسين التركيز والانتباه', 78, 100, 20, '2025-07-12 02:05:08', '2025-07-13 02:05:08'),
(3, 2, NULL, 'game', 'لعبة الحروف', 'تعلم الحروف الأبجدية', 92, 100, 20, '2025-07-10 02:05:08', '2025-07-13 02:05:08'),
(4, 2, NULL, 'assessment', 'تقييم المهارات الاجتماعية', 'تقييم شامل للمهارات الاجتماعية', 88, 100, 20, '2025-07-08 02:05:08', '2025-07-13 02:05:08');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','parent','specialist','student') NOT NULL DEFAULT 'parent',
  `specialization` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `password`, `role`, `specialization`, `phone`, `avatar`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'المدير العام', '<EMAIL>', '$2y$10$DK48vtAwXQiJYPQh7vlf.OYQrs.L.7SB6vy7g9477g6F4Fmar0V0S', 'admin', NULL, NULL, NULL, 1, '2025-07-12 17:46:05', '2025-07-13 12:21:58'),
(3, 'أحمد محمد', '<EMAIL>', '$2y$10$nwPQwbpBfZH4wJMnQLXsqeInEmK833BACRp0uqapS8wx/GIz89m7i', 'parent', NULL, '+216 12 345 678', NULL, 1, '2025-07-12 18:09:57', '2025-07-13 12:21:58'),
(4, 'فاطمة علي', '<EMAIL>', '$2y$10$3HXOL5toX1q8GuRR.dsmeuEMR7tnFU92QAw2xwTzl7Y.jkwHXoh0C', 'specialist', NULL, '+216 98 765 432', NULL, 1, '2025-07-12 18:09:57', '2025-07-13 12:21:59'),
(5, 'محمد أحمد', '<EMAIL>', '$2y$10$qJJslJHV6IEDkkffIC0LruL5IETBCcGyypFf00U1yQ6nf892GTPtm', 'student', NULL, '', NULL, 1, '2025-07-12 18:09:57', '2025-07-13 12:21:59'),
(7, 'أحمد محمد', '<EMAIL>', '$2y$10$KFSjWpseB.Wg9Sg5pePKgOe5MLsU5envqAYs/CosrX9qlnKaRWf1i', 'specialist', 'اضطراب طيف التوحد', NULL, NULL, 1, '2025-07-13 02:02:55', '2025-07-13 16:26:54'),
(8, 'فاطمة علي', '<EMAIL>', '$2y$10$wtQHY.Q7fNhl1bLnFhqSCu89fCVaqBciJueIKLBfQZf24NQvzka52', 'specialist', 'علاج النطق واللغة', NULL, NULL, 1, '2025-07-13 02:02:55', '2025-07-13 16:26:59'),
(9, 'د. محمد حسن', '<EMAIL>', '$2y$10$9g/wiVzm04ZqmgLZfLJMiei6X95nUMb2FVZ74WKNNAyFrq0HgWLeu', 'specialist', 'العلاج السلوكي', NULL, NULL, 1, '2025-07-13 02:02:55', '2025-07-13 12:21:58'),
(10, 'د. سارة أحمد', '<EMAIL>', '$2y$10$BYwpH6NxIIRfb93JBYLg.ualECUXChVuA/4pEYCjFkEv1397hYFI6', 'specialist', 'التربية الخاصة', NULL, NULL, 1, '2025-07-13 02:02:55', '2025-07-13 12:21:58'),
(11, 'أحمد الأب', '<EMAIL>', '$2y$10$cdpyIiVlc5NEMB7a1Z0YVOy5kvSeTHn.phOhEg5Bg9RlZK7XDZTZ6', 'parent', NULL, NULL, NULL, 1, '2025-07-13 02:04:12', '2025-07-13 12:21:58'),
(12, 'محمد أحمد', '<EMAIL>', '$2y$10$qPe.mFdv5tZMIuqqhcFabuDFRJy3sV1016xOPu3qLYzwwS8/OGJjW', 'student', NULL, NULL, NULL, 1, '2025-07-13 02:05:07', '2025-07-13 12:21:59'),
(13, 'سارة أحمد', '<EMAIL>', '$2y$10$AroflSAtddvjmZ/oyol7y.ZHtEJH.QIRevDP37LBbsZ.WBZYGbNDK', 'student', NULL, NULL, NULL, 1, '2025-07-13 02:05:07', '2025-07-13 12:21:59'),
(14, 'ياسين', '<EMAIL>', '$2y$10$qED4KOUsnTZGIUgpZGVZvuShRKRbRQ9vVwVlCDiNqYw922d2Keo9K', 'student', NULL, NULL, NULL, 1, '2025-07-13 11:22:37', '2025-07-13 12:21:59');

-- --------------------------------------------------------

--
-- Table structure for table `user_notifications`
--

CREATE TABLE `user_notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `notification_id` int(11) NOT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `workshops`
--

CREATE TABLE `workshops` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `date` datetime NOT NULL,
  `duration` int(11) DEFAULT NULL,
  `max_participants` int(11) DEFAULT NULL,
  `instructor_id` int(11) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `workshops`
--

INSERT INTO `workshops` (`id`, `title`, `description`, `date`, `duration`, `max_participants`, `instructor_id`, `is_active`, `created_at`) VALUES
(1, 'ورشة التواصل البصري', 'تعلم كيفية تطوير مهارات التواصل البصري لدى الأطفال ذوي التوحد', '2024-12-20 10:00:00', 120, 20, 1, 1, '2025-07-12 18:09:58'),
(2, 'ورشة إدارة السلوك', 'استراتيجيات فعالة لإدارة السلوكيات التحدية', '2024-12-25 14:00:00', 90, 15, 1, 1, '2025-07-12 18:09:58'),
(3, 'ورشة الأنشطة الحسية', 'أنشطة حسية مفيدة لتهدئة وتحفيز الأطفال', '2024-12-30 16:00:00', 60, 25, 1, 1, '2025-07-12 18:09:58');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `achievements`
--
ALTER TABLE `achievements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_difficulty_level` (`difficulty_level`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_display_order` (`display_order`);

--
-- Indexes for table `activities`
--
ALTER TABLE `activities`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_difficulty_level` (`difficulty_level`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `ai_chat_messages`
--
ALTER TABLE `ai_chat_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_session_id` (`session_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `appointments`
--
ALTER TABLE `appointments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_parent_id` (`parent_id`),
  ADD KEY `idx_specialist_id` (`specialist_id`),
  ADD KEY `idx_appointment_date` (`appointment_date`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `articles`
--
ALTER TABLE `articles`
  ADD PRIMARY KEY (`id`),
  ADD KEY `author_id` (`author_id`);

--
-- Indexes for table `chat_history`
--
ALTER TABLE `chat_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_created` (`user_id`,`created_at`);

--
-- Indexes for table `consultations`
--
ALTER TABLE `consultations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_parent_id` (`parent_id`),
  ADD KEY `idx_specialist_id` (`specialist_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_urgency` (`urgency_level`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `consultation_attachments`
--
ALTER TABLE `consultation_attachments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_consultation_id` (`consultation_id`);

--
-- Indexes for table `consultation_notifications`
--
ALTER TABLE `consultation_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_is_read` (`is_read`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `consultation_ratings`
--
ALTER TABLE `consultation_ratings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_consultation_id` (`consultation_id`),
  ADD KEY `idx_specialist_id` (`specialist_id`),
  ADD KEY `idx_rating` (`rating`);

--
-- Indexes for table `consultation_responses`
--
ALTER TABLE `consultation_responses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_consultation_id` (`consultation_id`),
  ADD KEY `idx_specialist_id` (`specialist_id`);

--
-- Indexes for table `educational_videos`
--
ALTER TABLE `educational_videos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_target_age` (`target_age`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `feedback`
--
ALTER TABLE `feedback`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `responded_by` (`responded_by`);

--
-- Indexes for table `media`
--
ALTER TABLE `media`
  ADD PRIMARY KEY (`id`),
  ADD KEY `uploaded_by` (`uploaded_by`);

--
-- Indexes for table `news`
--
ALTER TABLE `news`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_target_audience` (`target_audience`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_start_date` (`start_date`);

--
-- Indexes for table `platform_settings`
--
ALTER TABLE `platform_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `idx_setting_key` (`setting_key`),
  ADD KEY `idx_category` (`category`);

--
-- Indexes for table `progress_reports`
--
ALTER TABLE `progress_reports`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_student_id` (`student_id`),
  ADD KEY `idx_specialist_id` (`specialist_id`),
  ADD KEY `idx_report_period` (`report_period_start`,`report_period_end`);

--
-- Indexes for table `stories`
--
ALTER TABLE `stories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_target_age` (`target_age`),
  ADD KEY `idx_difficulty_level` (`difficulty_level`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_is_featured` (`is_featured`);

--
-- Indexes for table `students`
--
ALTER TABLE `students`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `parent_id` (`parent_id`);

--
-- Indexes for table `student_achievements`
--
ALTER TABLE `student_achievements`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_student_achievement` (`student_id`,`achievement_id`),
  ADD KEY `idx_student_id` (`student_id`),
  ADD KEY `idx_achievement_id` (`achievement_id`),
  ADD KEY `idx_is_completed` (`is_completed`),
  ADD KEY `idx_earned_at` (`earned_at`);

--
-- Indexes for table `student_activities`
--
ALTER TABLE `student_activities`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_student_id` (`student_id`),
  ADD KEY `idx_activity_type` (`activity_type`),
  ADD KEY `idx_completed_at` (`completed_at`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `user_notifications`
--
ALTER TABLE `user_notifications`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_notification` (`user_id`,`notification_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_notification_id` (`notification_id`),
  ADD KEY `idx_is_read` (`is_read`);

--
-- Indexes for table `workshops`
--
ALTER TABLE `workshops`
  ADD PRIMARY KEY (`id`),
  ADD KEY `instructor_id` (`instructor_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `achievements`
--
ALTER TABLE `achievements`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `activities`
--
ALTER TABLE `activities`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `ai_chat_messages`
--
ALTER TABLE `ai_chat_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `appointments`
--
ALTER TABLE `appointments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `articles`
--
ALTER TABLE `articles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `chat_history`
--
ALTER TABLE `chat_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `consultations`
--
ALTER TABLE `consultations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `consultation_attachments`
--
ALTER TABLE `consultation_attachments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `consultation_notifications`
--
ALTER TABLE `consultation_notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `consultation_ratings`
--
ALTER TABLE `consultation_ratings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `consultation_responses`
--
ALTER TABLE `consultation_responses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `educational_videos`
--
ALTER TABLE `educational_videos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `feedback`
--
ALTER TABLE `feedback`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `media`
--
ALTER TABLE `media`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `news`
--
ALTER TABLE `news`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `platform_settings`
--
ALTER TABLE `platform_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `progress_reports`
--
ALTER TABLE `progress_reports`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `stories`
--
ALTER TABLE `stories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `students`
--
ALTER TABLE `students`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `student_achievements`
--
ALTER TABLE `student_achievements`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `student_activities`
--
ALTER TABLE `student_activities`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `user_notifications`
--
ALTER TABLE `user_notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `workshops`
--
ALTER TABLE `workshops`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `articles`
--
ALTER TABLE `articles`
  ADD CONSTRAINT `articles_ibfk_1` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `chat_history`
--
ALTER TABLE `chat_history`
  ADD CONSTRAINT `chat_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `feedback`
--
ALTER TABLE `feedback`
  ADD CONSTRAINT `feedback_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `feedback_ibfk_2` FOREIGN KEY (`responded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `media`
--
ALTER TABLE `media`
  ADD CONSTRAINT `media_ibfk_1` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `students`
--
ALTER TABLE `students`
  ADD CONSTRAINT `students_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `students_ibfk_2` FOREIGN KEY (`parent_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `workshops`
--
ALTER TABLE `workshops`
  ADD CONSTRAINT `workshops_ibfk_1` FOREIGN KEY (`instructor_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
