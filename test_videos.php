<?php
/**
 * اختبار جدول الفيديوهات التعليمية
 * Test Educational Videos Table
 */

require_once 'config.php';
require_once 'db.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار الفيديوهات التعليمية - منصة نبراس</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }";
echo ".test-container { max-width: 1200px; margin: 2rem auto; padding: 2rem; }";
echo ".test-card { background: white; border-radius: 15px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }";
echo ".status-success { color: #28a745; }";
echo ".status-error { color: #dc3545; }";
echo ".status-warning { color: #ffc107; }";
echo ".video-card { border: 1px solid #ddd; border-radius: 10px; padding: 1rem; margin-bottom: 1rem; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='test-container'>";
echo "<div class='test-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-video'></i> اختبار جدول الفيديوهات التعليمية</h1>";

// اختبار وجود الجدول
echo "<h3><i class='fas fa-database'></i> اختبار وجود الجدول</h3>";
try {
    $check_table = $db->query("SHOW TABLES LIKE 'educational_videos'");
    if ($check_table->rowCount() > 0) {
        echo "<p class='status-success'><i class='fas fa-check'></i> جدول educational_videos: موجود</p>";
        
        // اختبار بنية الجدول
        $describe_result = $db->query("DESCRIBE educational_videos");
        $columns = $describe_result->fetchAll();
        
        echo "<h4>بنية الجدول:</h4>";
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
        echo "<tbody>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        
    } else {
        echo "<p class='status-error'><i class='fas fa-times'></i> جدول educational_videos: غير موجود</p>";
        echo "<div class='alert alert-warning'>";
        echo "<h5>الجدول غير موجود!</h5>";
        echo "<p>يمكنك إنشاء الجدول باستخدام أحد الخيارات التالية:</p>";
        echo "<a href='create_videos_table.php' class='btn btn-primary me-2'><i class='fas fa-plus'></i> إنشاء جدول الفيديوهات</a>";
        echo "<a href='setup_missing_tables.php' class='btn btn-secondary'><i class='fas fa-tools'></i> إعداد جميع الجداول</a>";
        echo "</div>";
        echo "</div></div></body></html>";
        exit;
    }
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> خطأ في التحقق من الجدول: " . $e->getMessage() . "</p>";
    echo "</div></div></body></html>";
    exit;
}

// اختبار البيانات
echo "<h3><i class='fas fa-list'></i> اختبار البيانات</h3>";
try {
    // إحصائيات عامة
    $stats_query = "SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                        COUNT(DISTINCT category) as categories,
                        AVG(views_count) as avg_views
                    FROM educational_videos";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result->fetch();
    
    echo "<div class='row mb-4'>";
    echo "<div class='col-md-3'><div class='text-center'><h4 class='text-primary'>" . $stats['total'] . "</h4><p>إجمالي الفيديوهات</p></div></div>";
    echo "<div class='col-md-3'><div class='text-center'><h4 class='text-success'>" . $stats['active'] . "</h4><p>فيديوهات نشطة</p></div></div>";
    echo "<div class='col-md-3'><div class='text-center'><h4 class='text-info'>" . $stats['categories'] . "</h4><p>فئات مختلفة</p></div></div>";
    echo "<div class='col-md-3'><div class='text-center'><h4 class='text-warning'>" . number_format($stats['avg_views']) . "</h4><p>متوسط المشاهدات</p></div></div>";
    echo "</div>";
    
    // إحصائيات الفئات
    echo "<h4>إحصائيات الفئات:</h4>";
    $categories_query = "SELECT category, COUNT(*) as count, AVG(views_count) as avg_views FROM educational_videos GROUP BY category ORDER BY count DESC";
    $categories_result = $db->query($categories_query);
    $categories = $categories_result->fetchAll();
    
    echo "<table class='table table-striped'>";
    echo "<thead><tr><th>الفئة</th><th>عدد الفيديوهات</th><th>متوسط المشاهدات</th></tr></thead>";
    echo "<tbody>";
    foreach ($categories as $category) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($category['category']) . "</td>";
        echo "<td>" . $category['count'] . "</td>";
        echo "<td>" . number_format($category['avg_views']) . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // عرض بعض الفيديوهات
    echo "<h4>عينة من الفيديوهات:</h4>";
    $videos_query = "SELECT * FROM educational_videos ORDER BY views_count DESC LIMIT 5";
    $videos_result = $db->query($videos_query);
    $videos = $videos_result->fetchAll();
    
    if (!empty($videos)) {
        foreach ($videos as $video) {
            echo "<div class='video-card'>";
            echo "<div class='row'>";
            echo "<div class='col-md-8'>";
            echo "<h5>" . htmlspecialchars($video['title']) . "</h5>";
            echo "<p class='text-muted'>" . htmlspecialchars(substr($video['description'], 0, 100)) . "...</p>";
            echo "<div class='d-flex gap-3'>";
            echo "<span class='badge bg-primary'>" . htmlspecialchars($video['category']) . "</span>";
            echo "<span class='badge bg-secondary'>" . htmlspecialchars($video['difficulty_level']) . "</span>";
            echo "<span class='badge bg-info'>" . $video['target_age'] . " سنوات</span>";
            echo "<span class='badge bg-success'>" . number_format($video['views_count']) . " مشاهدة</span>";
            echo "</div>";
            echo "</div>";
            echo "<div class='col-md-4 text-end'>";
            echo "<p><i class='fas fa-clock'></i> " . htmlspecialchars($video['duration']) . "</p>";
            echo "<p><i class='fas fa-calendar'></i> " . date('Y-m-d', strtotime($video['created_at'])) . "</p>";
            echo "<p><i class='fas fa-" . ($video['is_active'] ? 'check text-success' : 'times text-danger') . "'></i> " . ($video['is_active'] ? 'نشط' : 'غير نشط') . "</p>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-info'>";
        echo "<i class='fas fa-info-circle'></i> لا توجد فيديوهات في الجدول";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> خطأ في جلب البيانات: " . $e->getMessage() . "</p>";
}

echo "</div>";

// اختبار الوظائف
echo "<div class='test-card'>";
echo "<h2><i class='fas fa-cogs'></i> اختبار الوظائف</h2>";

// اختبار إدراج فيديو جديد
echo "<h4>اختبار إدراج فيديو جديد:</h4>";
try {
    $test_video = [
        'title' => 'فيديو اختبار - ' . date('Y-m-d H:i:s'),
        'description' => 'هذا فيديو اختبار تم إنشاؤه تلقائياً للتحقق من عمل النظام',
        'category' => 'عامة',
        'difficulty_level' => 'easy',
        'target_age' => 5,
        'duration' => '10:00',
        'views_count' => 0
    ];
    
    $insert_query = "INSERT INTO educational_videos (title, description, category, difficulty_level, target_age, duration, views_count) VALUES (?, ?, ?, ?, ?, ?, ?)";
    $insert_stmt = $db->prepare($insert_query);
    $result = $insert_stmt->execute([
        $test_video['title'],
        $test_video['description'],
        $test_video['category'],
        $test_video['difficulty_level'],
        $test_video['target_age'],
        $test_video['duration'],
        $test_video['views_count']
    ]);
    
    if ($result) {
        $new_id = $db->lastInsertId();
        echo "<p class='status-success'><i class='fas fa-check'></i> تم إدراج فيديو اختبار بنجاح (ID: $new_id)</p>";
        
        // حذف الفيديو التجريبي
        $delete_stmt = $db->prepare("DELETE FROM educational_videos WHERE id = ?");
        $delete_stmt->execute([$new_id]);
        echo "<p class='status-success'><i class='fas fa-check'></i> تم حذف فيديو الاختبار بنجاح</p>";
    } else {
        echo "<p class='status-error'><i class='fas fa-times'></i> فشل في إدراج فيديو الاختبار</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> خطأ في اختبار الإدراج: " . $e->getMessage() . "</p>";
}

echo "</div>";

// روابط مفيدة
echo "<div class='test-card'>";
echo "<h2><i class='fas fa-link'></i> روابط مفيدة</h2>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>للمديرين:</h5>";
echo "<ul>";
echo "<li><a href='admin/videos.php'>إدارة الفيديوهات</a></li>";
echo "<li><a href='admin/dashboard.php'>لوحة تحكم الإدارة</a></li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h5>للطلاب:</h5>";
echo "<ul>";
echo "<li><a href='student-space/videos.php'>مشاهدة الفيديوهات</a></li>";
echo "<li><a href='student-space/dashboard.php'>لوحة تحكم الطالب</a></li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-info mt-3'>";
echo "<h5><i class='fas fa-info-circle'></i> ملاحظات:</h5>";
echo "<ul>";
echo "<li>تأكد من وجود جدول educational_videos في قاعدة البيانات</li>";
echo "<li>تأكد من صحة روابط الفيديوهات قبل النشر</li>";
echo "<li>راجع إعدادات الأعمار المستهدفة للفيديوهات</li>";
echo "<li>تحقق من تصنيف الفيديوهات حسب الفئات المناسبة</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
