<?php
/**
 * إنشاء جدول القصص التفاعلية
 * Create Interactive Stories Table
 */

require_once 'config.php';
require_once 'db.php';

echo "إنشاء جدول القصص التفاعلية...\n";

try {
    // إنشاء جدول stories
    $sql = "CREATE TABLE IF NOT EXISTS stories (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        content LONGTEXT,
        thumbnail VARCHAR(500),
        category ENUM('تعليمية', 'مغامرات', 'خيالية', 'علمية', 'دينية', 'أخلاقية', 'عامة') DEFAULT 'عامة',
        difficulty_level ENUM('easy', 'medium', 'hard') DEFAULT 'easy',
        target_age INT DEFAULT 6,
        duration_minutes INT DEFAULT 10,
        reading_level VARCHAR(50) DEFAULT 'مبتدئ',
        language VARCHAR(10) DEFAULT 'ar',
        author <PERSON><PERSON><PERSON><PERSON>(255),
        illustrator <PERSON><PERSON><PERSON><PERSON>(255),
        moral_lesson TEXT,
        keywords TEXT,
        is_interactive BOOLEAN DEFAULT TRUE,
        has_audio BOOLEAN DEFAULT FALSE,
        audio_url VARCHAR(500),
        views_count INT DEFAULT 0,
        likes_count INT DEFAULT 0,
        is_featured BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_category (category),
        INDEX idx_target_age (target_age),
        INDEX idx_difficulty_level (difficulty_level),
        INDEX idx_is_active (is_active),
        INDEX idx_is_featured (is_featured)
    )";
    
    $db->exec($sql);
    echo "✅ تم إنشاء جدول stories بنجاح\n";

    // إدراج قصص تجريبية
    $stories = [
        [
            'title' => 'الأرنب الذكي والثعلب المكار',
            'description' => 'قصة مشوقة عن أرنب صغير ذكي يتعلم كيف يتعامل مع الثعلب المكار ويحمي نفسه بالذكاء والحكمة',
            'content' => 'في غابة جميلة، كان يعيش أرنب صغير ذكي اسمه أرنوب. كان أرنوب يحب اللعب والقفز بين الأشجار. في يوم من الأيام، قابل ثعلباً مكاراً يريد أن يخدعه. لكن أرنوب كان ذكياً جداً، فاستخدم عقله وتمكن من الهروب من الثعلب. تعلم أرنوب أن الذكاء أقوى من القوة، وأن التفكير الجيد يحل أي مشكلة.',
            'thumbnail' => 'images/stories/smart-rabbit.jpg',
            'category' => 'تعليمية',
            'difficulty_level' => 'easy',
            'target_age' => 5,
            'duration_minutes' => 8,
            'reading_level' => 'مبتدئ',
            'author' => 'فريق نبراس التعليمي',
            'moral_lesson' => 'الذكاء والحكمة أهم من القوة',
            'keywords' => 'ذكاء، حكمة، صداقة، حيوانات',
            'is_interactive' => 1,
            'views_count' => 1250,
            'likes_count' => 98
        ],
        [
            'title' => 'مغامرة سارة في الفضاء',
            'description' => 'رحلة مثيرة مع الطفلة سارة التي تحلم بأن تصبح رائدة فضاء وتكتشف الكواكب والنجوم',
            'content' => 'سارة طفلة صغيرة تحب النجوم والكواكب. في ليلة صافية، حلمت سارة أنها رائدة فضاء تسافر بين النجوم. زارت القمر ولعبت مع الفضائيين الودودين، وتعلمت عن الشمس والكواكب. عندما استيقظت، قررت سارة أن تدرس بجد لتحقق حلمها وتصبح رائدة فضاء حقيقية.',
            'thumbnail' => 'images/stories/space-adventure.jpg',
            'category' => 'علمية',
            'difficulty_level' => 'medium',
            'target_age' => 7,
            'duration_minutes' => 12,
            'reading_level' => 'متوسط',
            'author' => 'د. أحمد الفضائي',
            'moral_lesson' => 'الأحلام تتحقق بالعمل والاجتهاد',
            'keywords' => 'فضاء، نجوم، أحلام، علوم',
            'is_interactive' => 1,
            'views_count' => 980,
            'likes_count' => 87
        ],
        [
            'title' => 'الغابة السحرية وأصدقاء الطبيعة',
            'description' => 'قصة خيالية عن طفلة تكتشف غابة سحرية مليئة بالحيوانات المتكلمة والأشجار الحكيمة',
            'content' => 'لينا طفلة تحب الطبيعة. في يوم من الأيام، وجدت طريقاً سرياً يؤدي إلى غابة سحرية. في هذه الغابة، تتكلم الحيوانات وتغني الأشجار. التقت لينا بالدب الطيب والعصفور المرح والشجرة الحكيمة. تعلمت لينا أهمية المحافظة على البيئة وحب الطبيعة.',
            'thumbnail' => 'images/stories/magic-forest.jpg',
            'category' => 'خيالية',
            'difficulty_level' => 'easy',
            'target_age' => 6,
            'duration_minutes' => 10,
            'reading_level' => 'مبتدئ',
            'author' => 'نورا الخيال',
            'moral_lesson' => 'المحافظة على البيئة واجب على الجميع',
            'keywords' => 'طبيعة، بيئة، حيوانات، سحر',
            'is_interactive' => 1,
            'views_count' => 1500,
            'likes_count' => 125
        ],
        [
            'title' => 'البحث عن الكنز المفقود',
            'description' => 'مغامرة شيقة مع مجموعة من الأطفال يبحثون عن كنز مفقود ويتعلمون قيمة التعاون',
            'content' => 'أحمد وفاطمة وعلي أصدقاء يحبون المغامرات. وجدوا خريطة قديمة تؤدي إلى كنز مفقود. بدأوا رحلة البحث معاً، وواجهوا تحديات كثيرة. تعلموا أن التعاون والصداقة أهم من أي كنز، وأن العمل الجماعي يحقق المستحيل.',
            'thumbnail' => 'images/stories/treasure-hunt.jpg',
            'category' => 'مغامرات',
            'difficulty_level' => 'medium',
            'target_age' => 8,
            'duration_minutes' => 15,
            'reading_level' => 'متوسط',
            'author' => 'كابتن مغامر',
            'moral_lesson' => 'التعاون والصداقة أغلى من أي كنز',
            'keywords' => 'مغامرة، تعاون، صداقة، كنز',
            'is_interactive' => 1,
            'views_count' => 2100,
            'likes_count' => 156
        ],
        [
            'title' => 'قصة النبي يوسف عليه السلام',
            'description' => 'قصة تعليمية مبسطة عن النبي يوسف عليه السلام وصبره وحكمته',
            'content' => 'يوسف عليه السلام كان نبياً كريماً، أحبه والده يعقوب عليه السلام كثيراً. واجه يوسف تحديات كثيرة في حياته، لكنه صبر وتوكل على الله. بفضل صبره وحكمته، أصبح وزيراً في مصر وساعد الناس في سنوات الجفاف. تعلمنا من قصة يوسف أهمية الصبر والتوكل على الله.',
            'thumbnail' => 'images/stories/prophet-yusuf.jpg',
            'category' => 'دينية',
            'difficulty_level' => 'medium',
            'target_age' => 9,
            'duration_minutes' => 18,
            'reading_level' => 'متوسط',
            'author' => 'الشيخ محمد الحكيم',
            'moral_lesson' => 'الصبر والتوكل على الله يؤديان إلى النجاح',
            'keywords' => 'أنبياء، صبر، حكمة، إيمان',
            'is_interactive' => 1,
            'views_count' => 1800,
            'likes_count' => 142
        ],
        [
            'title' => 'الصدق طريق النجاح',
            'description' => 'قصة أخلاقية عن طفل يتعلم أهمية الصدق في جميع المواقف',
            'content' => 'خالد طفل طيب، لكنه أحياناً يقول أكاذيب صغيرة. في يوم من الأيام، كسر خالد إناء أمه بالخطأ، وخاف أن يخبرها الحقيقة. لكن ضميره لم يتركه مرتاحاً. قرر خالد أن يقول الصدق لأمه، فسامحته وشكرته على صدقه. تعلم خالد أن الصدق دائماً هو الطريق الصحيح.',
            'thumbnail' => 'images/stories/honesty.jpg',
            'category' => 'أخلاقية',
            'difficulty_level' => 'easy',
            'target_age' => 6,
            'duration_minutes' => 8,
            'reading_level' => 'مبتدئ',
            'author' => 'أستاذة سلمى',
            'moral_lesson' => 'الصدق أساس الأخلاق الحميدة',
            'keywords' => 'صدق، أخلاق، ضمير، تربية',
            'is_interactive' => 1,
            'views_count' => 1320,
            'likes_count' => 108
        ],
        [
            'title' => 'رحلة إلى عالم الديناصورات',
            'description' => 'مغامرة علمية مشوقة لاكتشاف عالم الديناصورات وتاريخ الأرض القديم',
            'content' => 'ماجد يحب الديناصورات كثيراً. في المتحف، وجد آلة زمن سحرية نقلته إلى عصر الديناصورات. التقى بديناصورات مختلفة: الطيبة والمفترسة. تعلم ماجد عن أنواع الديناصورات وكيف عاشت قديماً. عندما عاد إلى زمنه، قرر أن يصبح عالم آثار ليكتشف المزيد عن التاريخ.',
            'thumbnail' => 'images/stories/dinosaurs.jpg',
            'category' => 'علمية',
            'difficulty_level' => 'medium',
            'target_age' => 8,
            'duration_minutes' => 14,
            'reading_level' => 'متوسط',
            'author' => 'د. سامي الأثري',
            'moral_lesson' => 'حب العلم والاستطلاع يفتح آفاق المعرفة',
            'keywords' => 'ديناصورات، تاريخ، علوم، اكتشاف',
            'is_interactive' => 1,
            'views_count' => 1650,
            'likes_count' => 134
        ],
        [
            'title' => 'النملة المجتهدة والجندب الكسول',
            'description' => 'قصة تعليمية كلاسيكية عن أهمية العمل والاجتهاد مقابل الكسل',
            'content' => 'في حديقة جميلة، كانت تعيش نملة مجتهدة وجندب كسول. النملة تعمل طوال الصيف لتجمع الطعام للشتاء، بينما الجندب يلعب ويغني فقط. عندما جاء الشتاء البارد، كان لدى النملة طعام كثير، أما الجندب فكان جائعاً. ساعدت النملة الطيبة الجندب، وتعلم الجندب أهمية العمل والاجتهاد.',
            'thumbnail' => 'images/stories/ant-grasshopper.jpg',
            'category' => 'تعليمية',
            'difficulty_level' => 'easy',
            'target_age' => 5,
            'duration_minutes' => 7,
            'reading_level' => 'مبتدئ',
            'author' => 'حكايات الجدة',
            'moral_lesson' => 'العمل والاجتهاد أساس النجاح في الحياة',
            'keywords' => 'عمل، اجتهاد، كسل، حكمة',
            'is_interactive' => 1,
            'views_count' => 1100,
            'likes_count' => 89
        ]
    ];

    $stmt = $db->prepare("INSERT IGNORE INTO stories (title, description, content, thumbnail, category, difficulty_level, target_age, duration_minutes, reading_level, author, moral_lesson, keywords, is_interactive, views_count, likes_count) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $inserted_count = 0;
    foreach ($stories as $story) {
        $result = $stmt->execute([
            $story['title'],
            $story['description'],
            $story['content'],
            $story['thumbnail'],
            $story['category'],
            $story['difficulty_level'],
            $story['target_age'],
            $story['duration_minutes'],
            $story['reading_level'],
            $story['author'],
            $story['moral_lesson'],
            $story['keywords'],
            $story['is_interactive'],
            $story['views_count'],
            $story['likes_count']
        ]);
        if ($result) {
            $inserted_count++;
        }
    }
    
    echo "✅ تم إدراج $inserted_count قصة تجريبية\n";

    // التحقق من النتيجة
    $count_result = $db->query("SELECT COUNT(*) FROM stories");
    $total_count = $count_result->fetchColumn();
    echo "✅ إجمالي القصص في الجدول: $total_count\n";

    // عرض إحصائيات حسب الفئة
    echo "\n📊 إحصائيات القصص حسب الفئة:\n";
    $stats_result = $db->query("SELECT category, COUNT(*) as count FROM stories GROUP BY category ORDER BY count DESC");
    $stats = $stats_result->fetchAll();
    
    foreach ($stats as $stat) {
        echo "- {$stat['category']}: {$stat['count']} قصة\n";
    }

    // إحصائيات حسب العمر المستهدف
    echo "\n📈 إحصائيات حسب العمر المستهدف:\n";
    $age_stats = $db->query("SELECT target_age, COUNT(*) as count FROM stories GROUP BY target_age ORDER BY target_age");
    $age_data = $age_stats->fetchAll();
    
    foreach ($age_data as $age) {
        echo "- {$age['target_age']} سنوات: {$age['count']} قصة\n";
    }

    echo "\n🎉 تم إنشاء جدول القصص التفاعلية بنجاح!\n";

} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🚀 جدول القصص جاهز للاستخدام!\n";
?>
