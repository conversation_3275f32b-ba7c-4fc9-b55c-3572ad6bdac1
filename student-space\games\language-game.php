<?php
require_once '../../includes/auth.php';
require_once '../../db.php';

// التحقق من تسجيل الدخول كطالب
require_student();

$user = current_user();

// جلب معلومات الطالب
try {
    $student_info_query = "SELECT * FROM students WHERE user_id = :user_id";
    $student_info_stmt = $db->prepare($student_info_query);
    $student_info_stmt->bindParam(':user_id', $user['id']);
    $student_info_stmt->execute();
    $student_info = $student_info_stmt->fetch();
} catch (PDOException $e) {
    $student_info = null;
}

// معالجة حفظ النتيجة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_score'])) {
    check_csrf();

    $score = (int)$_POST['score'];
    $total_questions = (int)$_POST['total_questions'];
    $time_spent = (int)$_POST['time_spent'];
    $game_type = clean_input($_POST['game_type']);

    $score_percentage = ($score / $total_questions) * 100;
    $points_earned = $score * 8; // 8 نقاط لكل إجابة صحيحة

    try {
        $save_progress_query = "INSERT INTO student_progress
                               (student_id, activity_id, score_percentage, points_earned, time_spent_minutes, is_completed)
                               VALUES (:student_id, 2, :score_percentage, :points_earned, :time_spent, 1)";
        $save_progress_stmt = $db->prepare($save_progress_query);
        $student_id = $student_info['id'] ?? 0;
        $save_progress_stmt->bindParam(':student_id', $student_id);
        $save_progress_stmt->bindParam(':score_percentage', $score_percentage);
        $save_progress_stmt->bindParam(':points_earned', $points_earned);
        $save_progress_stmt->bindParam(':time_spent', $time_spent);
        $save_progress_stmt->execute();

        $success_message = "تم حفظ نتيجتك! حصلت على {$points_earned} نقطة.";
    } catch (PDOException $e) {
        $error_message = "خطأ في حفظ النتيجة: " . $e->getMessage();
    }
}

// إعداد متغيرات الصفحة
$page_title = 'لعبة الحروف والكلمات';
$page_description = 'تعلم اللغة العربية بطريقة ممتعة وتفاعلية';

// تضمين الهيدر
include '../../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../../css/nibrass-unified.css" rel="stylesheet">

<style>
.language-game-container {
    /* background: linear-gradient(135deg, #4ecdc4, #44a08d); */
    min-height: 100vh;
    padding: 2rem 0;
}

.game-card {
    background: white;
    border-radius: 25px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    padding: 2rem;
    margin: 1rem 0;
    transition: all 0.3s ease;
}

.game-header {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
    border-radius: 20px;
    margin-bottom: 2rem;
        margin-top: 80px;

}

.letter-display {
    font-size: 8rem;
    font-weight: bold;
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 20px;
    margin: 2rem 0;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.word-display {
    font-size: 3rem;
    font-weight: bold;
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
    margin: 2rem 0;
    color: #2c3e50;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    border: 3px solid #e9ecef;
}

.answer-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin: 2rem 0;
}

.answer-btn {
    padding: 1.5rem;
    font-size: 1.5rem;
    font-weight: bold;
    border: 3px solid #e9ecef;
    border-radius: 15px;
    background: white;
    color: #2c3e50;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.answer-btn:hover {
    background: #4ecdc4;
    color: white;
    border-color: #4ecdc4;
    transform: translateY(-3px);
}

.answer-btn.correct {
    background: #28a745;
    color: white;
    border-color: #28a745;
    animation: correctAnswer 0.6s ease;
}

.answer-btn.incorrect {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
    animation: incorrectAnswer 0.6s ease;
}

.game-type-selector {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.game-type-btn {
    padding: 1rem 2rem;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    background: white;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    font-weight: 600;
}

.game-type-btn.active {
    background: #4ecdc4;
    color: white;
    border-color: #4ecdc4;
}

.audio-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    font-size: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 1rem auto;
    display: block;
    box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
}

.audio-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
}

.game-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin: 2rem 0;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #4ecdc4;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4ecdc4, #44a08d);
    transition: width 0.3s ease;
    border-radius: 10px;
}

@keyframes correctAnswer {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes incorrectAnswer {
    0% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
    100% { transform: translateX(0); }
}

/* تأكيد ظهور الأيقونات */
.fas, .far, .fab, .fal {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

@media (max-width: 768px) {
    .answer-options {
        grid-template-columns: 1fr;
    }

    .game-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .letter-display {
        font-size: 5rem;
        min-height: 150px;
    }

    .word-display {
        font-size: 2rem;
        padding: 1rem;
    }

    .game-type-selector {
        flex-direction: column;
        align-items: center;
    }
}
</style>

<div class="language-game-container">
    <div class="container">
        <!-- رأس اللعبة -->
        <div class="game-header">
            <h1><i class="fas fa-book-open"></i> لعبة الحروف والكلمات</h1>
            <p>تعلم اللغة العربية بطريقة ممتعة وتفاعلية!</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success text-center">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- بطاقة اللعبة -->
        <div class="game-card">
            <!-- اختيار نوع اللعبة -->
            <div class="game-type-selector">
                <button class="game-type-btn active" data-type="letters">
                    <i class="fas fa-font"></i> تعلم الحروف
                </button>
                <button class="game-type-btn" data-type="words">
                    <i class="fas fa-spell-check"></i> تكوين الكلمات
                </button>
                <button class="game-type-btn" data-type="sounds">
                    <i class="fas fa-volume-up"></i> أصوات الحروف
                </button>
            </div>

            <!-- إحصائيات اللعبة -->
            <div class="game-stats">
                <div class="stat-item">
                    <div class="stat-value" id="score">0</div>
                    <div class="stat-label">النقاط</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="question-number">1</div>
                    <div class="stat-label">السؤال</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="correct-answers">0</div>
                    <div class="stat-label">إجابات صحيحة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="timer">0</div>
                    <div class="stat-label">الوقت (ثانية)</div>
                </div>
            </div>

            <!-- شريط التقدم -->
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
            </div>

            <!-- عرض الحرف أو الكلمة -->
            <div id="letter-mode">
                <div class="letter-display" id="letter-display">
                    اضغط "ابدأ اللعبة" للبدء
                </div>
                <button class="audio-btn" id="audio-btn" style="display: none;">
                    <i class="fas fa-volume-up"></i>
                </button>
            </div>

            <div id="word-mode" style="display: none;">
                <div class="word-display" id="word-display">
                    اضغط "ابدأ اللعبة" للبدء
                </div>
            </div>

            <!-- خيارات الإجابة -->
            <div class="answer-options" id="answer-options" style="display: none;">
                <button class="answer-btn" id="option1"></button>
                <button class="answer-btn" id="option2"></button>
                <button class="answer-btn" id="option3"></button>
                <button class="answer-btn" id="option4"></button>
            </div>

            <!-- أزرار التحكم -->
            <div class="game-controls">
                <button class="nibrass-btn nibrass-btn-primary nibrass-btn-lg" id="start-game">
                    <i class="fas fa-play"></i> ابدأ اللعبة
                </button>
                <button class="nibrass-btn nibrass-btn-warning nibrass-btn-lg" id="restart-game" style="display: none;">
                    <i class="fas fa-redo"></i> إعادة البدء
                </button>
                <button class="nibrass-btn nibrass-btn-success nibrass-btn-lg" id="save-score" style="display: none;">
                    <i class="fas fa-save"></i> حفظ النتيجة
                </button>
            </div>
        </div>

        <!-- قسم الاحتفال -->
        <div class="celebration" id="celebration" style="display: none;">
            <i class="fas fa-star fa-4x mb-3 text-warning"></i>
            <h2>أحسنت! لعبة رائعة!</h2>
            <p id="final-score"></p>
            <div class="mt-3">
                <i class="fas fa-heart text-danger"></i>
                <i class="fas fa-heart text-danger"></i>
                <i class="fas fa-heart text-danger"></i>
            </div>
        </div>

        <!-- العودة للوحة التحكم -->
        <div class="text-center mt-4">
            <a href="../dashboard.php" class="nibrass-btn nibrass-btn-outline-primary">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>
</div>

<!-- نموذج حفظ النتيجة -->
<form id="score-form" method="POST" style="display: none;">
    <?php echo csrf_field(); ?>
    <input type="hidden" name="save_score" value="1">
    <input type="hidden" name="score" id="final-score-input">
    <input type="hidden" name="total_questions" id="total-questions-input">
    <input type="hidden" name="time_spent" id="time-spent-input">
    <input type="hidden" name="game_type" id="game-type-input">
</form>

<script>
// بيانات الحروف العربية
const arabicLetters = [
    { letter: 'أ', name: 'ألف', sound: 'أ' },
    { letter: 'ب', name: 'باء', sound: 'بَ' },
    { letter: 'ت', name: 'تاء', sound: 'تَ' },
    { letter: 'ث', name: 'ثاء', sound: 'ثَ' },
    { letter: 'ج', name: 'جيم', sound: 'جَ' },
    { letter: 'ح', name: 'حاء', sound: 'حَ' },
    { letter: 'خ', name: 'خاء', sound: 'خَ' },
    { letter: 'د', name: 'دال', sound: 'دَ' },
    { letter: 'ذ', name: 'ذال', sound: 'ذَ' },
    { letter: 'ر', name: 'راء', sound: 'رَ' },
    { letter: 'ز', name: 'زاي', sound: 'زَ' },
    { letter: 'س', name: 'سين', sound: 'سَ' },
    { letter: 'ش', name: 'شين', sound: 'شَ' },
    { letter: 'ص', name: 'صاد', sound: 'صَ' },
    { letter: 'ض', name: 'ضاد', sound: 'ضَ' },
    { letter: 'ط', name: 'طاء', sound: 'طَ' },
    { letter: 'ظ', name: 'ظاء', sound: 'ظَ' },
    { letter: 'ع', name: 'عين', sound: 'عَ' },
    { letter: 'غ', name: 'غين', sound: 'غَ' },
    { letter: 'ف', name: 'فاء', sound: 'فَ' },
    { letter: 'ق', name: 'قاف', sound: 'قَ' },
    { letter: 'ك', name: 'كاف', sound: 'كَ' },
    { letter: 'ل', name: 'لام', sound: 'لَ' },
    { letter: 'م', name: 'ميم', sound: 'مَ' },
    { letter: 'ن', name: 'نون', sound: 'نَ' },
    { letter: 'ه', name: 'هاء', sound: 'هَ' },
    { letter: 'و', name: 'واو', sound: 'وَ' },
    { letter: 'ي', name: 'ياء', sound: 'يَ' }
];

// كلمات بسيطة
const simpleWords = [
    { word: 'بيت', meaning: 'منزل' },
    { word: 'قلم', meaning: 'أداة الكتابة' },
    { word: 'كتاب', meaning: 'للقراءة' },
    { word: 'شمس', meaning: 'نجم النهار' },
    { word: 'قمر', meaning: 'نجم الليل' },
    { word: 'ماء', meaning: 'للشرب' },
    { word: 'نار', meaning: 'حارة' },
    { word: 'ورد', meaning: 'زهرة جميلة' },
    { word: 'طير', meaning: 'يطير في السماء' },
    { word: 'سمك', meaning: 'يعيش في الماء' }
];

// متغيرات اللعبة
let gameState = {
    currentQuestion: 1,
    totalQuestions: 10,
    score: 0,
    correctAnswers: 0,
    gameType: 'letters',
    startTime: null,
    gameActive: false,
    currentAnswer: null,
    currentData: null
};

let gameTimer;

// عناصر DOM
const letterDisplay = document.getElementById('letter-display');
const wordDisplay = document.getElementById('word-display');
const letterMode = document.getElementById('letter-mode');
const wordMode = document.getElementById('word-mode');
const answerOptions = document.getElementById('answer-options');
const startBtn = document.getElementById('start-game');
const restartBtn = document.getElementById('restart-game');
const saveBtn = document.getElementById('save-score');
const audioBtn = document.getElementById('audio-btn');
const celebration = document.getElementById('celebration');

// إحصائيات
const scoreElement = document.getElementById('score');
const questionNumberElement = document.getElementById('question-number');
const correctAnswersElement = document.getElementById('correct-answers');
const timerElement = document.getElementById('timer');
const progressFill = document.getElementById('progress-fill');

// أزرار نوع اللعبة
const gameTypeBtns = document.querySelectorAll('.game-type-btn');
const answerBtns = document.querySelectorAll('.answer-btn');

// مستمعي الأحداث
startBtn.addEventListener('click', startGame);
restartBtn.addEventListener('click', restartGame);
saveBtn.addEventListener('click', saveScore);
audioBtn.addEventListener('click', playSound);

gameTypeBtns.forEach(btn => {
    btn.addEventListener('click', function() {
        if (!gameState.gameActive) {
            gameTypeBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            gameState.gameType = this.dataset.type;
            updateGameMode();
        }
    });
});

answerBtns.forEach(btn => {
    btn.addEventListener('click', function() {
        if (gameState.gameActive) {
            checkAnswer(this.textContent.trim());
        }
    });
});

// تحديث وضع اللعبة
function updateGameMode() {
    if (gameState.gameType === 'words') {
        letterMode.style.display = 'none';
        wordMode.style.display = 'block';
    } else {
        letterMode.style.display = 'block';
        wordMode.style.display = 'none';
    }
}

// بدء اللعبة
function startGame() {
    gameState = {
        currentQuestion: 1,
        totalQuestions: 10,
        score: 0,
        correctAnswers: 0,
        gameType: document.querySelector('.game-type-btn.active').dataset.type,
        startTime: Date.now(),
        gameActive: true,
        currentAnswer: null,
        currentData: null
    };

    startBtn.style.display = 'none';
    restartBtn.style.display = 'inline-block';
    answerOptions.style.display = 'grid';
    celebration.style.display = 'none';

    // تعطيل أزرار نوع اللعبة
    gameTypeBtns.forEach(btn => btn.disabled = true);

    // بدء المؤقت
    gameTimer = setInterval(updateTimer, 1000);

    updateGameMode();
    generateQuestion();
}

// إعادة بدء اللعبة
function restartGame() {
    clearInterval(gameTimer);
    gameTypeBtns.forEach(btn => btn.disabled = false);

    startBtn.style.display = 'inline-block';
    restartBtn.style.display = 'none';
    saveBtn.style.display = 'none';
    answerOptions.style.display = 'none';
    celebration.style.display = 'none';
    audioBtn.style.display = 'none';

    letterDisplay.textContent = 'اضغط "ابدأ اللعبة" للبدء';
    wordDisplay.textContent = 'اضغط "ابدأ اللعبة" للبدء';

    // إعادة تعيين الإحصائيات
    updateStats();
    progressFill.style.width = '0%';
}

// توليد سؤال جديد
function generateQuestion() {
    if (gameState.gameType === 'letters' || gameState.gameType === 'sounds') {
        generateLetterQuestion();
    } else if (gameState.gameType === 'words') {
        generateWordQuestion();
    }

    updateStats();
}

// توليد سؤال الحروف
function generateLetterQuestion() {
    const randomLetter = arabicLetters[Math.floor(Math.random() * arabicLetters.length)];
    gameState.currentData = randomLetter;

    if (gameState.gameType === 'sounds') {
        letterDisplay.textContent = 'استمع للصوت واختر الحرف الصحيح';
        audioBtn.style.display = 'block';
        gameState.currentAnswer = randomLetter.letter;
    } else {
        letterDisplay.textContent = randomLetter.letter;
        audioBtn.style.display = 'block';
        gameState.currentAnswer = randomLetter.name;
    }

    generateLetterOptions();
}

// توليد سؤال الكلمات
function generateWordQuestion() {
    const randomWord = simpleWords[Math.floor(Math.random() * simpleWords.length)];
    gameState.currentData = randomWord;

    // إخفاء حرف من الكلمة
    const wordArray = randomWord.word.split('');
    const hiddenIndex = Math.floor(Math.random() * wordArray.length);
    const hiddenLetter = wordArray[hiddenIndex];
    wordArray[hiddenIndex] = '_';

    wordDisplay.textContent = wordArray.join(' ') + ' (' + randomWord.meaning + ')';
    gameState.currentAnswer = hiddenLetter;

    generateWordOptions(hiddenLetter);
}

// توليد خيارات الحروف
function generateLetterOptions() {
    const correctAnswer = gameState.gameType === 'sounds' ?
                         gameState.currentData.letter :
                         gameState.currentData.name;

    const options = [correctAnswer];

    // توليد خيارات خاطئة
    while (options.length < 4) {
        const randomLetter = arabicLetters[Math.floor(Math.random() * arabicLetters.length)];
        const option = gameState.gameType === 'sounds' ?
                      randomLetter.letter :
                      randomLetter.name;

        if (!options.includes(option)) {
            options.push(option);
        }
    }

    // خلط الخيارات
    shuffleArray(options);

    // عرض الخيارات
    answerBtns.forEach((btn, index) => {
        btn.textContent = options[index];
        btn.className = 'answer-btn';
        btn.disabled = false;
    });
}

// توليد خيارات الكلمات
function generateWordOptions(correctLetter) {
    const options = [correctLetter];

    // توليد حروف خاطئة
    while (options.length < 4) {
        const randomLetter = arabicLetters[Math.floor(Math.random() * arabicLetters.length)];
        if (!options.includes(randomLetter.letter)) {
            options.push(randomLetter.letter);
        }
    }

    // خلط الخيارات
    shuffleArray(options);

    // عرض الخيارات
    answerBtns.forEach((btn, index) => {
        btn.textContent = options[index];
        btn.className = 'answer-btn';
        btn.disabled = false;
    });
}

// خلط المصفوفة
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
}

// فحص الإجابة
function checkAnswer(selectedAnswer) {
    const isCorrect = selectedAnswer === gameState.currentAnswer;

    // تعطيل جميع الأزرار
    answerBtns.forEach(btn => btn.disabled = true);

    // تمييز الإجابة الصحيحة والخاطئة
    answerBtns.forEach(btn => {
        if (btn.textContent.trim() === gameState.currentAnswer) {
            btn.classList.add('correct');
        } else if (btn.textContent.trim() === selectedAnswer && !isCorrect) {
            btn.classList.add('incorrect');
        }
    });

    if (isCorrect) {
        gameState.score += 8;
        gameState.correctAnswers++;
    }

    // الانتقال للسؤال التالي بعد تأخير
    setTimeout(() => {
        if (gameState.currentQuestion < gameState.totalQuestions) {
            gameState.currentQuestion++;
            generateQuestion();
        } else {
            endGame();
        }
    }, 1500);
}

// تشغيل الصوت
function playSound() {
    if (gameState.currentData) {
        // محاكاة تشغيل الصوت
        audioBtn.style.transform = 'scale(1.2)';
        setTimeout(() => {
            audioBtn.style.transform = 'scale(1)';
        }, 200);

        // يمكن إضافة Web Speech API هنا لاحقاً
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(gameState.currentData.sound || gameState.currentData.letter);
            utterance.lang = 'ar-SA';
            utterance.rate = 0.7;
            speechSynthesis.speak(utterance);
        }
    }
}

// تحديث الإحصائيات
function updateStats() {
    scoreElement.textContent = gameState.score;
    questionNumberElement.textContent = gameState.currentQuestion;
    correctAnswersElement.textContent = gameState.correctAnswers;

    const progress = (gameState.currentQuestion - 1) / gameState.totalQuestions * 100;
    progressFill.style.width = progress + '%';
}

// تحديث المؤقت
function updateTimer() {
    if (gameState.startTime) {
        const elapsed = Math.floor((Date.now() - gameState.startTime) / 1000);
        timerElement.textContent = elapsed;
    }
}

// إنهاء اللعبة
function endGame() {
    clearInterval(gameTimer);
    gameState.gameActive = false;

    const finalTime = Math.floor((Date.now() - gameState.startTime) / 1000);
    const percentage = Math.round((gameState.correctAnswers / gameState.totalQuestions) * 100);

    letterDisplay.textContent = 'انتهت اللعبة!';
    wordDisplay.textContent = 'انتهت اللعبة!';
    answerOptions.style.display = 'none';
    audioBtn.style.display = 'none';

    // عرض النتيجة النهائية
    document.getElementById('final-score').textContent =
        `حصلت على ${gameState.score} نقطة من أصل ${gameState.totalQuestions * 8} نقطة (${percentage}%)`;

    celebration.style.display = 'block';
    celebration.classList.add('show');
    saveBtn.style.display = 'inline-block';

    // تحضير بيانات الحفظ
    document.getElementById('final-score-input').value = gameState.correctAnswers;
    document.getElementById('total-questions-input').value = gameState.totalQuestions;
    document.getElementById('time-spent-input').value = Math.ceil(finalTime / 60);
    document.getElementById('game-type-input').value = gameState.gameType;
}

// حفظ النتيجة
function saveScore() {
    document.getElementById('score-form').submit();
}
</script>

<?php
// تضمين الفوتر
include '../../includes/footer.php';
?>