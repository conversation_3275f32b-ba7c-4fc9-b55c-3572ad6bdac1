<?php
/**
 * اختبار النظام والتحقق من عمل جميع المكونات
 */

require_once 'config.php';
require_once 'db.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار النظام - منصة نبراس</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }";
echo ".test-container { max-width: 1200px; margin: 2rem auto; padding: 2rem; }";
echo ".test-card { background: white; border-radius: 15px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }";
echo ".status-success { color: #28a745; }";
echo ".status-error { color: #dc3545; }";
echo ".status-warning { color: #ffc107; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='test-container'>";
echo "<div class='test-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-cogs'></i> اختبار نظام منصة نبراس</h1>";

// اختبار الاتصال بقاعدة البيانات
echo "<h3><i class='fas fa-database'></i> اختبار قاعدة البيانات</h3>";
try {
    $db->query("SELECT 1");
    echo "<p class='status-success'><i class='fas fa-check'></i> الاتصال بقاعدة البيانات: نجح</p>";
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> الاتصال بقاعدة البيانات: فشل - " . $e->getMessage() . "</p>";
}

// اختبار الجداول المطلوبة
echo "<h3><i class='fas fa-table'></i> اختبار الجداول</h3>";
$required_tables = [
    'users' => 'جدول المستخدمين',
    'platform_settings' => 'جدول الإعدادات',
    'notifications' => 'جدول الإشعارات',
    'user_notifications' => 'جدول قراءة الإشعارات',
    'activities' => 'جدول الأنشطة',
    'educational_videos' => 'جدول الفيديوهات التعليمية',
    'stories' => 'جدول القصص التفاعلية',
    'achievements' => 'جدول الإنجازات والجوائز',
    'student_achievements' => 'جدول إنجازات الطلاب'
];

foreach ($required_tables as $table => $description) {
    try {
        $result = $db->query("SELECT COUNT(*) FROM $table");
        $count = $result->fetchColumn();
        echo "<p class='status-success'><i class='fas fa-check'></i> $description: موجود ($count سجل)</p>";
    } catch (Exception $e) {
        echo "<p class='status-error'><i class='fas fa-times'></i> $description: غير موجود</p>";
    }
}

// اختبار الإعدادات
echo "<h3><i class='fas fa-cog'></i> اختبار الإعدادات</h3>";
try {
    $settings_query = "SELECT setting_key, setting_value FROM platform_settings LIMIT 5";
    $settings_stmt = $db->prepare($settings_query);
    $settings_stmt->execute();
    $settings = $settings_stmt->fetchAll();
    
    if (!empty($settings)) {
        echo "<p class='status-success'><i class='fas fa-check'></i> الإعدادات: تم تحميلها بنجاح</p>";
        echo "<ul>";
        foreach ($settings as $setting) {
            echo "<li><strong>" . htmlspecialchars($setting['setting_key']) . ":</strong> " . htmlspecialchars($setting['setting_value']) . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='status-warning'><i class='fas fa-exclamation-triangle'></i> الإعدادات: لا توجد إعدادات</p>";
    }
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> الإعدادات: خطأ - " . $e->getMessage() . "</p>";
}

// اختبار الإشعارات
echo "<h3><i class='fas fa-bell'></i> اختبار الإشعارات</h3>";
try {
    $notifications_query = "SELECT COUNT(*) as total, 
                                   SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active
                           FROM notifications";
    $notifications_stmt = $db->prepare($notifications_query);
    $notifications_stmt->execute();
    $notifications_stats = $notifications_stmt->fetch();
    
    echo "<p class='status-success'><i class='fas fa-check'></i> الإشعارات: " . $notifications_stats['total'] . " إجمالي، " . $notifications_stats['active'] . " نشط</p>";
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> الإشعارات: خطأ - " . $e->getMessage() . "</p>";
}

// اختبار الأنشطة
echo "<h3><i class='fas fa-gamepad'></i> اختبار الأنشطة</h3>";
try {
    $activities_query = "SELECT COUNT(*) as total,
                                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active
                        FROM activities";
    $activities_stmt = $db->prepare($activities_query);
    $activities_stmt->execute();
    $activities_stats = $activities_stmt->fetch();

    echo "<p class='status-success'><i class='fas fa-check'></i> الأنشطة: " . $activities_stats['total'] . " إجمالي، " . $activities_stats['active'] . " نشط</p>";
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> الأنشطة: خطأ - " . $e->getMessage() . "</p>";
}

// اختبار الفيديوهات التعليمية
echo "<h3><i class='fas fa-video'></i> اختبار الفيديوهات التعليمية</h3>";
try {
    $videos_query = "SELECT COUNT(*) as total,
                            SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                            COUNT(DISTINCT category) as categories
                    FROM educational_videos";
    $videos_stmt = $db->prepare($videos_query);
    $videos_stmt->execute();
    $videos_stats = $videos_stmt->fetch();

    echo "<p class='status-success'><i class='fas fa-check'></i> الفيديوهات: " . $videos_stats['total'] . " إجمالي، " . $videos_stats['active'] . " نشط، " . $videos_stats['categories'] . " فئة</p>";

    // عرض إحصائيات الفئات
    $categories_query = "SELECT category, COUNT(*) as count FROM educational_videos GROUP BY category ORDER BY count DESC";
    $categories_stmt = $db->prepare($categories_query);
    $categories_stmt->execute();
    $categories = $categories_stmt->fetchAll();

    if (!empty($categories)) {
        echo "<ul>";
        foreach ($categories as $category) {
            echo "<li><strong>" . htmlspecialchars($category['category']) . ":</strong> " . $category['count'] . " فيديو</li>";
        }
        echo "</ul>";
    }
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> الفيديوهات: خطأ - " . $e->getMessage() . "</p>";
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        echo "<p class='status-warning'><i class='fas fa-info-circle'></i> يمكنك إنشاء جدول الفيديوهات من <a href='create_videos_table.php'>هنا</a></p>";
    }
}

// اختبار القصص التفاعلية
echo "<h3><i class='fas fa-book-open'></i> اختبار القصص التفاعلية</h3>";
try {
    $stories_query = "SELECT COUNT(*) as total,
                             SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                             COUNT(DISTINCT category) as categories,
                             AVG(duration_minutes) as avg_duration
                     FROM stories";
    $stories_stmt = $db->prepare($stories_query);
    $stories_stmt->execute();
    $stories_stats = $stories_stmt->fetch();

    echo "<p class='status-success'><i class='fas fa-check'></i> القصص: " . $stories_stats['total'] . " إجمالي، " . $stories_stats['active'] . " نشط، " . $stories_stats['categories'] . " فئة، متوسط المدة: " . number_format($stories_stats['avg_duration']) . " دقيقة</p>";

    // عرض إحصائيات الفئات
    $categories_query = "SELECT category, COUNT(*) as count FROM stories GROUP BY category ORDER BY count DESC";
    $categories_stmt = $db->prepare($categories_query);
    $categories_stmt->execute();
    $categories = $categories_stmt->fetchAll();

    if (!empty($categories)) {
        echo "<ul>";
        foreach ($categories as $category) {
            echo "<li><strong>" . htmlspecialchars($category['category']) . ":</strong> " . $category['count'] . " قصة</li>";
        }
        echo "</ul>";
    }
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> القصص: خطأ - " . $e->getMessage() . "</p>";
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        echo "<p class='status-warning'><i class='fas fa-info-circle'></i> يمكنك إنشاء جدول القصص من <a href='create_stories_table.php'>هنا</a></p>";
    }
}

// اختبار الإنجازات والجوائز
echo "<h3><i class='fas fa-trophy'></i> اختبار الإنجازات والجوائز</h3>";
try {
    $achievements_query = "SELECT COUNT(*) as total,
                                  SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                                  COUNT(DISTINCT category) as categories,
                                  AVG(reward_points) as avg_points
                          FROM achievements";
    $achievements_stmt = $db->prepare($achievements_query);
    $achievements_stmt->execute();
    $achievements_stats = $achievements_stmt->fetch();

    echo "<p class='status-success'><i class='fas fa-check'></i> الإنجازات: " . $achievements_stats['total'] . " إجمالي، " . $achievements_stats['active'] . " نشط، " . $achievements_stats['categories'] . " فئة، متوسط النقاط: " . number_format($achievements_stats['avg_points']) . "</p>";

    // عرض إحصائيات الفئات
    $categories_query = "SELECT category, COUNT(*) as count FROM achievements GROUP BY category ORDER BY count DESC";
    $categories_stmt = $db->prepare($categories_query);
    $categories_stmt->execute();
    $categories = $categories_stmt->fetchAll();

    if (!empty($categories)) {
        echo "<ul>";
        foreach ($categories as $category) {
            echo "<li><strong>" . htmlspecialchars($category['category']) . ":</strong> " . $category['count'] . " إنجاز</li>";
        }
        echo "</ul>";
    }

    // اختبار جدول إنجازات الطلاب
    $student_achievements_query = "SELECT COUNT(*) as total FROM student_achievements";
    $student_achievements_stmt = $db->prepare($student_achievements_query);
    $student_achievements_stmt->execute();
    $student_achievements_stats = $student_achievements_stmt->fetch();

    echo "<p class='status-success'><i class='fas fa-check'></i> إنجازات الطلاب: " . $student_achievements_stats['total'] . " إنجاز محقق</p>";

} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> الإنجازات: خطأ - " . $e->getMessage() . "</p>";
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        echo "<p class='status-warning'><i class='fas fa-info-circle'></i> يمكنك إنشاء جدول الإنجازات من <a href='create_achievements_table.php'>هنا</a></p>";
    }
}

// اختبار الملفات المهمة
echo "<h3><i class='fas fa-file-code'></i> اختبار الملفات</h3>";
$important_files = [
    'config.php' => 'ملف الإعدادات',
    'db.php' => 'ملف قاعدة البيانات',
    'includes/auth.php' => 'ملف المصادقة',
    'includes/header.php' => 'ملف الهيدر',
    'includes/footer.php' => 'ملف الفوتر',
    'admin/notifications.php' => 'صفحة إدارة الإشعارات',
    'notifications.php' => 'صفحة الإشعارات للمستخدمين',
    'mark_notification_read.php' => 'ملف تحديد الإشعارات كمقروءة'
];

foreach ($important_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='status-success'><i class='fas fa-check'></i> $description: موجود</p>";
    } else {
        echo "<p class='status-error'><i class='fas fa-times'></i> $description: غير موجود</p>";
    }
}

// اختبار الروابط
echo "<h3><i class='fas fa-link'></i> روابط مهمة</h3>";
$important_links = [
    'index.php' => 'الصفحة الرئيسية',
    'admin/dashboard.php' => 'لوحة تحكم الإدارة',
    'admin/videos.php' => 'إدارة الفيديوهات التعليمية',
    'admin/stories.php' => 'إدارة القصص التفاعلية',
    'admin/achievements.php' => 'إدارة الإنجازات والجوائز',
    'admin/notifications.php' => 'إدارة الإشعارات',
    'notifications.php' => 'صفحة الإشعارات',
    'student-space/videos.php' => 'صفحة الفيديوهات للطلاب',
    'student-space/stories.php' => 'صفحة القصص للطلاب',
    'student-space/achievements.php' => 'صفحة الإنجازات للطلاب',
    'create_videos_table.php' => 'إنشاء جدول الفيديوهات',
    'create_stories_table.php' => 'إنشاء جدول القصص',
    'create_achievements_table.php' => 'إنشاء جدول الإنجازات',
    'test_videos.php' => 'اختبار الفيديوهات',
    'test_stories.php' => 'اختبار القصص',
    'setup_missing_tables.php' => 'إعداد الجداول المفقودة'
];

foreach ($important_links as $link => $description) {
    echo "<p><i class='fas fa-external-link-alt'></i> <a href='$link' target='_blank'>$description</a></p>";
}

echo "</div>";

// إحصائيات النظام
echo "<div class='test-card'>";
echo "<h2><i class='fas fa-chart-bar'></i> إحصائيات النظام</h2>";

try {
    // إحصائيات المستخدمين
    $users_query = "SELECT role, COUNT(*) as count FROM users GROUP BY role";
    $users_stmt = $db->prepare($users_query);
    $users_stmt->execute();
    $users_stats = $users_stmt->fetchAll();
    
    echo "<h4>المستخدمون:</h4>";
    echo "<ul>";
    foreach ($users_stats as $stat) {
        $role_names = [
            'admin' => 'مديرين',
            'specialist' => 'أخصائيين',
            'parent' => 'أولياء أمور',
            'student' => 'طلاب'
        ];
        $role_name = $role_names[$stat['role']] ?? $stat['role'];
        echo "<li>$role_name: " . number_format($stat['count']) . "</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p class='status-error'>خطأ في جلب إحصائيات المستخدمين: " . $e->getMessage() . "</p>";
}

echo "</div>";

// نصائح وتوجيهات
echo "<div class='test-card'>";
echo "<h2><i class='fas fa-lightbulb'></i> الخطوات التالية</h2>";
echo "<ol>";
echo "<li><strong>إذا كانت الجداول مفقودة:</strong> اذهب إلى <a href='setup_missing_tables.php'>إعداد الجداول المفقودة</a></li>";
echo "<li><strong>لإدارة الإشعارات:</strong> اذهب إلى <a href='admin/notifications.php'>إدارة الإشعارات</a></li>";
echo "<li><strong>لمشاهدة الإشعارات:</strong> اذهب إلى <a href='notifications.php'>صفحة الإشعارات</a></li>";
echo "<li><strong>لإدارة النظام:</strong> اذهب إلى <a href='admin/dashboard.php'>لوحة تحكم الإدارة</a></li>";
echo "</ol>";

echo "<div class='alert alert-info mt-4'>";
echo "<h5><i class='fas fa-info-circle'></i> ملاحظات مهمة:</h5>";
echo "<ul>";
echo "<li>تأكد من تشغيل خادم Apache وMySQL</li>";
echo "<li>تأكد من صحة إعدادات قاعدة البيانات في config.php</li>";
echo "<li>تأكد من وجود جميع الجداول المطلوبة</li>";
echo "<li>اختبر تسجيل الدخول والإشعارات</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
