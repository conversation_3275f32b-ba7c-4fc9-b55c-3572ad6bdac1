<?php
/**
 * اختبار جدول الإنجازات والجوائز
 * Test Achievements and Rewards Table
 */

require_once 'config.php';
require_once 'db.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار الإنجازات والجوائز - منصة نبراس</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }";
echo ".test-container { max-width: 1200px; margin: 2rem auto; padding: 2rem; }";
echo ".test-card { background: white; border-radius: 15px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }";
echo ".status-success { color: #28a745; }";
echo ".status-error { color: #dc3545; }";
echo ".status-warning { color: #ffc107; }";
echo ".achievement-card { border: 1px solid #ddd; border-radius: 10px; padding: 1rem; margin-bottom: 1rem; }";
echo ".badge-custom { padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.8rem; margin: 0.2rem; }";
echo ".difficulty-easy { background: #28a745; color: white; }";
echo ".difficulty-medium { background: #ffc107; color: black; }";
echo ".difficulty-hard { background: #dc3545; color: white; }";
echo ".difficulty-expert { background: #6f42c1; color: white; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='test-container'>";
echo "<div class='test-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-trophy'></i> اختبار جدول الإنجازات والجوائز</h1>";

// اختبار وجود الجداول
echo "<h3><i class='fas fa-database'></i> اختبار وجود الجداول</h3>";
try {
    $check_achievements = $db->query("SHOW TABLES LIKE 'achievements'");
    $check_student_achievements = $db->query("SHOW TABLES LIKE 'student_achievements'");
    
    if ($check_achievements->rowCount() > 0) {
        echo "<p class='status-success'><i class='fas fa-check'></i> جدول achievements: موجود</p>";
        
        // اختبار بنية جدول achievements
        $describe_result = $db->query("DESCRIBE achievements");
        $columns = $describe_result->fetchAll();
        
        echo "<h4>بنية جدول achievements:</h4>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
        echo "<tbody>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
        
    } else {
        echo "<p class='status-error'><i class='fas fa-times'></i> جدول achievements: غير موجود</p>";
        echo "<div class='alert alert-warning'>";
        echo "<h5>الجدول غير موجود!</h5>";
        echo "<p>يمكنك إنشاء الجدول باستخدام أحد الخيارات التالية:</p>";
        echo "<a href='create_achievements_table.php' class='btn btn-primary me-2'><i class='fas fa-plus'></i> إنشاء جدول الإنجازات</a>";
        echo "<a href='setup_missing_tables.php' class='btn btn-secondary'><i class='fas fa-tools'></i> إعداد جميع الجداول</a>";
        echo "</div>";
        echo "</div></div></body></html>";
        exit;
    }
    
    if ($check_student_achievements->rowCount() > 0) {
        echo "<p class='status-success'><i class='fas fa-check'></i> جدول student_achievements: موجود</p>";
    } else {
        echo "<p class='status-error'><i class='fas fa-times'></i> جدول student_achievements: غير موجود</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> خطأ في التحقق من الجداول: " . $e->getMessage() . "</p>";
    echo "</div></div></body></html>";
    exit;
}

// اختبار البيانات
echo "<h3><i class='fas fa-list'></i> اختبار البيانات</h3>";
try {
    // إحصائيات عامة
    $stats_query = "SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                        COUNT(DISTINCT category) as categories,
                        AVG(reward_points) as avg_points,
                        COUNT(DISTINCT difficulty_level) as difficulty_levels
                    FROM achievements";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result->fetch();
    
    echo "<div class='row mb-4'>";
    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-primary'>" . $stats['total'] . "</h4><p>إجمالي الإنجازات</p></div></div>";
    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-success'>" . $stats['active'] . "</h4><p>إنجازات نشطة</p></div></div>";
    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-info'>" . $stats['categories'] . "</h4><p>فئات مختلفة</p></div></div>";
    echo "<div class='col-md-3'><div class='text-center'><h4 class='text-warning'>" . number_format($stats['avg_points']) . "</h4><p>متوسط نقاط المكافأة</p></div></div>";
    echo "<div class='col-md-3'><div class='text-center'><h4 class='text-secondary'>" . $stats['difficulty_levels'] . "</h4><p>مستويات الصعوبة</p></div></div>";
    echo "</div>";
    
    // إحصائيات الفئات
    echo "<h4>إحصائيات الفئات:</h4>";
    $categories_query = "SELECT category, COUNT(*) as count, AVG(reward_points) as avg_points FROM achievements GROUP BY category ORDER BY count DESC";
    $categories_result = $db->query($categories_query);
    $categories = $categories_result->fetchAll();
    
    echo "<table class='table table-striped'>";
    echo "<thead><tr><th>الفئة</th><th>عدد الإنجازات</th><th>متوسط النقاط</th></tr></thead>";
    echo "<tbody>";
    foreach ($categories as $category) {
        echo "<tr>";
        echo "<td><span class='badge-custom bg-primary text-white'>" . htmlspecialchars($category['category']) . "</span></td>";
        echo "<td>" . $category['count'] . "</td>";
        echo "<td>" . number_format($category['avg_points']) . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // إحصائيات مستويات الصعوبة
    echo "<h4>إحصائيات مستويات الصعوبة:</h4>";
    $difficulty_query = "SELECT difficulty_level, COUNT(*) as count, AVG(reward_points) as avg_points FROM achievements GROUP BY difficulty_level ORDER BY FIELD(difficulty_level, 'easy', 'medium', 'hard', 'expert')";
    $difficulty_result = $db->query($difficulty_query);
    $difficulties = $difficulty_result->fetchAll();
    
    echo "<div class='row'>";
    foreach ($difficulties as $difficulty) {
        $difficulty_names = [
            'easy' => 'سهل',
            'medium' => 'متوسط',
            'hard' => 'صعب',
            'expert' => 'خبير'
        ];
        $difficulty_name = $difficulty_names[$difficulty['difficulty_level']] ?? $difficulty['difficulty_level'];
        echo "<div class='col-md-3 mb-2'>";
        echo "<div class='text-center p-2 border rounded'>";
        echo "<span class='badge-custom difficulty-" . $difficulty['difficulty_level'] . "'>" . $difficulty_name . "</span>";
        echo "<h5>" . $difficulty['count'] . " إنجاز</h5>";
        echo "<p class='mb-0'>متوسط النقاط: " . number_format($difficulty['avg_points']) . "</p>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    // عرض بعض الإنجازات
    echo "<h4>عينة من الإنجازات:</h4>";
    $achievements_query = "SELECT * FROM achievements ORDER BY reward_points DESC LIMIT 5";
    $achievements_result = $db->query($achievements_query);
    $achievements = $achievements_result->fetchAll();
    
    if (!empty($achievements)) {
        foreach ($achievements as $achievement) {
            echo "<div class='achievement-card'>";
            echo "<div class='row'>";
            echo "<div class='col-md-8'>";
            echo "<h5><i class='" . htmlspecialchars($achievement['icon']) . "'></i> " . htmlspecialchars($achievement['title']) . "</h5>";
            echo "<p class='text-muted'>" . htmlspecialchars($achievement['description']) . "</p>";
            echo "<div class='mb-2'>";
            echo "<span class='badge-custom bg-primary text-white'>" . htmlspecialchars($achievement['category']) . "</span>";
            echo "<span class='badge-custom difficulty-" . $achievement['difficulty_level'] . "'>";
            $difficulty_names = ['easy' => 'سهل', 'medium' => 'متوسط', 'hard' => 'صعب', 'expert' => 'خبير'];
            echo $difficulty_names[$achievement['difficulty_level']] ?? $achievement['difficulty_level'];
            echo "</span>";
            echo "<span class='badge-custom bg-success text-white'>" . $achievement['reward_points'] . " نقطة</span>";
            if ($achievement['reward_certificate']) {
                echo "<span class='badge-custom bg-warning text-dark'>شهادة</span>";
            }
            echo "</div>";
            if ($achievement['unlock_conditions']) {
                echo "<p class='small text-info'><i class='fas fa-key'></i> <strong>شروط الفتح:</strong> " . htmlspecialchars($achievement['unlock_conditions']) . "</p>";
            }
            echo "</div>";
            echo "<div class='col-md-4 text-end'>";
            echo "<p><i class='fas fa-users'></i> العمر: " . $achievement['target_age_min'] . "-" . $achievement['target_age_max'] . " سنة</p>";
            echo "<p><i class='fas fa-calendar'></i> " . date('Y-m-d', strtotime($achievement['created_at'])) . "</p>";
            echo "<p><i class='fas fa-" . ($achievement['is_active'] ? 'check text-success' : 'times text-danger') . "'></i> " . ($achievement['is_active'] ? 'نشط' : 'غير نشط') . "</p>";
            if ($achievement['reward_gift']) {
                echo "<p><i class='fas fa-gift text-warning'></i> " . htmlspecialchars($achievement['reward_gift']) . "</p>";
            }
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-info'>";
        echo "<i class='fas fa-info-circle'></i> لا توجد إنجازات في الجدول";
        echo "</div>";
    }
    
    // إحصائيات إنجازات الطلاب
    echo "<h4>إحصائيات إنجازات الطلاب:</h4>";
    try {
        $student_stats_query = "SELECT COUNT(*) as total_earned, 
                                       COUNT(DISTINCT student_id) as unique_students,
                                       AVG(progress_percentage) as avg_progress
                               FROM student_achievements";
        $student_stats_result = $db->query($student_stats_query);
        $student_stats = $student_stats_result->fetch();
        
        echo "<div class='row'>";
        echo "<div class='col-md-4'><div class='text-center p-3 border rounded'><h5>" . $student_stats['total_earned'] . "</h5><p>إنجازات محققة</p></div></div>";
        echo "<div class='col-md-4'><div class='text-center p-3 border rounded'><h5>" . $student_stats['unique_students'] . "</h5><p>طلاب حققوا إنجازات</p></div></div>";
        echo "<div class='col-md-4'><div class='text-center p-3 border rounded'><h5>" . number_format($student_stats['avg_progress'], 1) . "%</h5><p>متوسط التقدم</p></div></div>";
        echo "</div>";
    } catch (Exception $e) {
        echo "<p class='text-muted'>لا توجد بيانات إنجازات طلاب حتى الآن</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> خطأ في جلب البيانات: " . $e->getMessage() . "</p>";
}

echo "</div>";

// اختبار الوظائف
echo "<div class='test-card'>";
echo "<h2><i class='fas fa-cogs'></i> اختبار الوظائف</h2>";

// اختبار إدراج إنجاز جديد
echo "<h4>اختبار إدراج إنجاز جديد:</h4>";
try {
    $test_achievement = [
        'title' => 'إنجاز اختبار - ' . date('Y-m-d H:i:s'),
        'description' => 'هذا إنجاز اختبار تم إنشاؤه تلقائياً للتحقق من عمل النظام',
        'category' => 'عام',
        'difficulty_level' => 'easy',
        'reward_points' => 5
    ];
    
    $insert_query = "INSERT INTO achievements (title, description, category, difficulty_level, reward_points) VALUES (?, ?, ?, ?, ?)";
    $insert_stmt = $db->prepare($insert_query);
    $result = $insert_stmt->execute([
        $test_achievement['title'],
        $test_achievement['description'],
        $test_achievement['category'],
        $test_achievement['difficulty_level'],
        $test_achievement['reward_points']
    ]);
    
    if ($result) {
        $new_id = $db->lastInsertId();
        echo "<p class='status-success'><i class='fas fa-check'></i> تم إدراج إنجاز اختبار بنجاح (ID: $new_id)</p>";
        
        // حذف الإنجاز التجريبي
        $delete_stmt = $db->prepare("DELETE FROM achievements WHERE id = ?");
        $delete_stmt->execute([$new_id]);
        echo "<p class='status-success'><i class='fas fa-check'></i> تم حذف إنجاز الاختبار بنجاح</p>";
    } else {
        echo "<p class='status-error'><i class='fas fa-times'></i> فشل في إدراج إنجاز الاختبار</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> خطأ في اختبار الإدراج: " . $e->getMessage() . "</p>";
}

echo "</div>";

// روابط مفيدة
echo "<div class='test-card'>";
echo "<h2><i class='fas fa-link'></i> روابط مفيدة</h2>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>للمديرين:</h5>";
echo "<ul>";
echo "<li><a href='admin/achievements.php'>إدارة الإنجازات</a></li>";
echo "<li><a href='admin/dashboard.php'>لوحة تحكم الإدارة</a></li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h5>للطلاب:</h5>";
echo "<ul>";
echo "<li><a href='student-space/achievements.php'>عرض الإنجازات</a></li>";
echo "<li><a href='student-space/dashboard.php'>لوحة تحكم الطالب</a></li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-info mt-3'>";
echo "<h5><i class='fas fa-info-circle'></i> ملاحظات:</h5>";
echo "<ul>";
echo "<li>تأكد من وجود جدولي achievements و student_achievements في قاعدة البيانات</li>";
echo "<li>راجع شروط فتح الإنجازات وملاءمتها للأعمار المستهدفة</li>";
echo "<li>تحقق من توزيع نقاط المكافآت بشكل عادل</li>";
echo "<li>اختبر آلية منح الإنجازات للطلاب</li>";
echo "<li>راجع الشهادات والهدايا المرتبطة بالإنجازات</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
