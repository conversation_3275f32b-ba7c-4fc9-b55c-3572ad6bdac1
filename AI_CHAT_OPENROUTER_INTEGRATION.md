# تكامل OpenRouter.ai مع نظام الدردشة الذكية - منصة نبراس

## نظرة عامة
تم تحديث نظام الدردشة الذكية في منصة نبراس للعمل مع OpenRouter.ai بدلاً من OpenAI مباشرة. OpenRouter.ai يوفر وصولاً موحداً لعدة نماذج ذكاء اصطناعي مختلفة مع مرونة أكبر في التسعير والاستخدام.

## التغييرات المطبقة

### 1. تحديث API Endpoint
- **من:** `https://api.openai.com/v1/chat/completions`
- **إلى:** `https://openrouter.ai/api/v1/chat/completions`

### 2. تحديث النموذج المستخدم
- **من:** `gpt-3.5-turbo` (OpenAI مباشرة)
- **إلى:** `deepseek/deepseek-chat:free` (DeepSeek V3 المجاني عبر OpenRouter)

### 3. تحسين Headers
```php
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $api_key,
    'HTTP-Referer: https://nibrass.local',
    'X-Title: Nibrass Educational Platform',
    'Content-Type: application/json'
]);
```

### 4. تحسين معالجة الأخطاء
- إضافة تسجيل مفصل للأخطاء
- رسائل خطأ مخصصة للمستخدمين باللغة العربية
- معالجة خاصة لأخطاء المصادقة (401) وتجاوز الحد (429)

### 5. نظام الردود الاحتياطية
تم تحسين نظام الردود الذكية المحلية ليشمل:
- ردود أكثر تفصيلاً وفائدة
- تغطية موضوعات إضافية (التعليم، السلوك، إلخ)
- ردود تفاعلية ومفيدة حتى بدون اتصال بالإنترنت

## إعدادات قابلة للتخصيص

### تغيير النموذج المستخدم
يمكن تغيير النموذج المستخدم بسهولة في الملف `parent-space/ai-chat.php`:

```php
$model = 'deepseek/deepseek-chat:free'; // النموذج الحالي - مجاني تماماً!

// نماذج أخرى متاحة:
// $model = 'openai/gpt-3.5-turbo'; // مدفوع
// $model = 'anthropic/claude-3-haiku'; // مدفوع
// $model = 'meta-llama/llama-3.1-8b-instruct'; // مجاني محدود
// $model = 'google/gemini-pro'; // مدفوع
```

### مزايا نموذج DeepSeek V3 المجاني:
- **مجاني تماماً**: $0 لكل من الإدخال والإخراج
- **سياق كبير**: 163,840 رمز (token)
- **أداء عالي**: ينافس النماذج المدفوعة الرائدة
- **دعم ممتاز للعربية**: يتعامل بشكل طبيعي مع النصوص العربية
- **ردود مفصلة**: قادر على تقديم إجابات شاملة ومفيدة

### تحديث مفتاح API
```php
$api_key = '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';
```

## اختبار النظام

### 1. اختبار الوظائف الأساسية
- تسجيل الدخول كولي أمر
- الوصول إلى صفحة المساعد الذكي
- إرسال رسائل تجريبية
- التحقق من الردود

### 2. اختبار الردود الاحتياطية
جرب الكلمات المفتاحية التالية:
- "مرحبا"
- "كيف يمكنني تحسين التواصل مع طفلي؟"
- "ما هي أفضل الأنشطة للأطفال ذوي التوحد؟"
- "كيف أتعامل مع نوبات الغضب؟"
- "نصائح لتطوير المهارات الاجتماعية"

### 3. مراقبة الأخطاء
تحقق من ملفات السجل (error logs) للتأكد من عدم وجود أخطاء في الاتصال.

## استكشاف الأخطاء وإصلاحها

### خطأ 401 (Unauthorized)
- تحقق من صحة مفتاح API
- تأكد من أن المفتاح لم ينته صلاحيته
- تحقق من تنسيق Header الصحيح

### خطأ 429 (Rate Limit)
- انتظر قبل إرسال طلبات جديدة
- فكر في ترقية خطة OpenRouter.ai
- استخدم نظام التخزين المؤقت للردود

### خطأ 500+ (Server Error)
- تحقق من حالة خدمة OpenRouter.ai
- جرب نموذج ذكاء اصطناعي مختلف
- استخدم الردود الاحتياطية المحلية

## الصيانة والتطوير المستقبلي

### 1. مراقبة الاستخدام
- راقب استهلاك API من لوحة تحكم OpenRouter.ai
- تتبع معدل نجاح الطلبات
- راقب أوقات الاستجابة

### 2. تحسينات مقترحة
- إضافة نظام تخزين مؤقت للردود الشائعة
- تطبيق نظام إعادة المحاولة التلقائية
- إضافة المزيد من النماذج كخيارات احتياطية

### 3. الأمان
- تشفير مفتاح API في قاعدة البيانات
- إضافة نظام مراقبة للاستخدام غير المعتاد
- تطبيق حدود للمستخدمين

## الدعم والمساعدة
- وثائق OpenRouter.ai: https://openrouter.ai/docs
- مجتمع OpenRouter.ai: https://discord.gg/fVyRaUDgxW
- دعم منصة نبراس: راجع فريق التطوير

---
**تاريخ التحديث:** 2025-07-13  
**الإصدار:** 1.0  
**المطور:** Augment Agent
