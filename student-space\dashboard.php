<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كطالب
require_student();

$user = current_user();

// جلب معلومات الطالب
try {
    $student_info_query = "SELECT s.*, p.name as parent_name
                          FROM students s
                          LEFT JOIN users p ON s.parent_id = p.id
                          WHERE s.user_id = :user_id";
    $student_info_stmt = $db->prepare($student_info_query);
    $student_info_stmt->bindParam(':user_id', $user['id']);
    $student_info_stmt->execute();
    $student_info = $student_info_stmt->fetch();
} catch (PDOException $e) {
    $student_info = null;
}

// جلب الأنشطة والألعاب المتاحة
try {
    $student_age = $student_info['age'] ?? 10;
    $activities_query = "SELECT * FROM activities WHERE is_active = 1 AND target_age <= :age ORDER BY difficulty_level ASC, created_at DESC LIMIT 8";
    $activities_stmt = $db->prepare($activities_query);
    $activities_stmt->bindParam(':age', $student_age);
    $activities_stmt->execute();
    $activities = $activities_stmt->fetchAll();
} catch (PDOException $e) {
    $activities = [];
}

// جلب إنجازات الطالب
try {
    $student_id = $student_info['id'] ?? 0;
    $achievements_query = "SELECT * FROM student_achievements WHERE student_id = :student_id ORDER BY earned_date DESC LIMIT 5";
    $achievements_stmt = $db->prepare($achievements_query);
    $achievements_stmt->bindParam(':student_id', $student_id);
    $achievements_stmt->execute();
    $achievements = $achievements_stmt->fetchAll();
} catch (PDOException $e) {
    $achievements = [];
}

// جلب النقاط والمستوى
try {
    $student_id = $student_info['id'] ?? 0;
    $points_query = "SELECT
                        COALESCE(SUM(points_earned), 0) as total_points,
                        COUNT(*) as completed_activities
                     FROM student_progress
                     WHERE student_id = :student_id";
    $points_stmt = $db->prepare($points_query);
    $points_stmt->bindParam(':student_id', $student_id);
    $points_stmt->execute();
    $progress_stats = $points_stmt->fetch();
} catch (PDOException $e) {
    $progress_stats = ['total_points' => 0, 'completed_activities' => 0];
}

// حساب المستوى
$level = floor($progress_stats['total_points'] / 100) + 1;
$points_to_next_level = 100 - ($progress_stats['total_points'] % 100);

// إعداد متغيرات الصفحة
$page_title = 'فضاء التلاميذ';
$page_description = 'مساحة تعليمية تفاعلية للأطفال';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
/* Student Dashboard Modern Styles with Enhanced Arabic RTL Support */
.student-dashboard-header {
    background: #4a90e2;
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.student-dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="studentPattern" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23studentPattern)"/></svg>');
    opacity: 0.3;
}

.student-dashboard-header h1,
.student-dashboard-header p,
.student-dashboard-header .lead {
    color: white !important;
    position: relative;
    z-index: 2;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.student-dashboard-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.student-dashboard-header .lead {
    font-size: 1.2rem;
    font-weight: 400;
    opacity: 0.95;
    margin-bottom: 2rem;
}

/* Enhanced Dashboard Cards */
.dashboard-card {
    background: white;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.08);
    padding: 2.5rem;
    margin-bottom: 2.5rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 107, 107, 0.1);
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
    border-radius: 25px 25px 0 0;
}

.dashboard-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(255, 107, 107, 0.15);
    border-color: rgba(255, 107, 107, 0.3);
}

/* Level and Progress Section */
.level-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 25px;
    padding: 2rem;
    color: white;
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.level-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="levelPattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23levelPattern)"/></svg>');
    opacity: 0.3;
}

.level-content {
    position: relative;
    z-index: 2;
    color: white !important;
}

.level-content h3,
.level-content p {
    color: white !important;
}

.level-badge {
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 2.5rem;
    font-weight: 700;
    backdrop-filter: blur(10px);
    border: 3px solid rgba(255, 255, 255, 0.3);
    animation: levelPulse 3s ease-in-out infinite;
}

@keyframes levelPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.progress-bar-custom {
    height: 15px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ffd700, #ffb347);
    border-radius: 10px;
    transition: width 0.8s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Activity Cards */
.activity-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.activity-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
}

.activity-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: rgba(255, 107, 107, 0.3);
}

.activity-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 1.5rem auto;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.activity-icon.math { background: linear-gradient(135deg, #ff6b6b, #ee5a24); }
.activity-icon.language { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
.activity-icon.science { background: linear-gradient(135deg, #45b7d1, #2980b9); }
.activity-icon.art { background: linear-gradient(135deg, #96ceb4, #27ae60); }
.activity-icon.game { background: linear-gradient(135deg, #feca57, #ff9ff3); }

/* تأكيد ظهور الأيقونات */
.student-dashboard-header i,
.dashboard-card i,
.activity-card i,
.level-section i,
.btn i,
.navbar i,
.dropdown-item i {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}



/* Responsive Design */
@media (max-width: 768px) {
    .student-dashboard-header {
        padding: 2rem 0;
    }

    .student-dashboard-header h1 {
        font-size: 2rem;
    }

    .activity-grid {
        grid-template-columns: 1fr;
    }

    .level-badge {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }
}
</style>

<!-- قسم العنوان -->
<section class="nibrass-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1><i class="fas fa-graduation-cap"></i> مرحباً <?php echo htmlspecialchars($user['name']); ?>!</h1>
                <p class="lead">مرحباً بك في مساحتك التعليمية المليئة بالمرح والتعلم</p>
                <?php if ($student_info): ?>
                <div class="mt-3">
                    <span class="badge bg-light text-dark fs-6 me-2">
                        <i class="fas fa-birthday-cake"></i> العمر: <?php echo $student_info['age']; ?> سنة
                    </span>
                    <?php if (isset($student_info['grade']) && $student_info['grade']): ?>
                    <span class="badge bg-light text-dark fs-6">
                        <i class="fas fa-school"></i> الصف: <?php echo htmlspecialchars($student_info['grade']); ?>
                    </span>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
            <div class="col-lg-4">
                <div class="text-center">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <i class="fas fa-star fa-2x mb-2"></i>
                                <h4><?php echo $progress_stats['total_points']; ?></h4>
                                <small>النقاط</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <i class="fas fa-trophy fa-2x mb-2"></i>
                                <h4><?php echo count($achievements); ?></h4>
                                <small>الإنجازات</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- الإجراءات السريعة -->
<section class="section bg-light">
    <div class="container">
        <div class="dashboard-card">
            <div class="text-center mb-4">
                <h2><i class="fas fa-rocket"></i> ماذا تريد أن تفعل اليوم؟</h2>
                <p class="text-muted">اختر النشاط الذي تريد البدء به</p>
            </div>

            <div class="row text-center">
                <div class="col-lg-3 col-md-6 mb-3">
                    <a href="activities.php" class="btn btn-primary btn-lg w-100 h-100 d-flex flex-column justify-content-center">
                        <i class="fas fa-gamepad fa-3x mb-3"></i>
                        <span>الألعاب التعليمية</span>
                    </a>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <a href="stories.php" class="btn btn-success btn-lg w-100 h-100 d-flex flex-column justify-content-center">
                        <i class="fas fa-book fa-3x mb-3"></i>
                        <span>القصص التفاعلية</span>
                    </a>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <a href="drawing.php" class="btn btn-warning btn-lg w-100 h-100 d-flex flex-column justify-content-center">
                        <i class="fas fa-paint-brush fa-3x mb-3"></i>
                        <span>الرسم والتلوين</span>
                    </a>
                </div>

                <div class="col-lg-3 col-md-6 mb-3">
                    <a href="videos.php" class="btn btn-info btn-lg w-100 h-100 d-flex flex-column justify-content-center">
                        <i class="fas fa-video fa-3x mb-3"></i>
                        <span>الفيديوهات التعليمية</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>


<!-- الأنشطة والألعاب -->
<section class="section">
    <div class="container">
        <div class="nibrass-card">
            <div class="nibrass-card-header">
                <div class="nibrass-card-icon primary">
                    <i class="fas fa-gamepad"></i>
                </div>
                <div>
                    <h3 class="nibrass-card-title">الأنشطة والألعاب التعليمية</h3>
                    <p class="nibrass-card-subtitle">اختر النشاط المناسب لك وابدأ التعلم بالمرح</p>
                </div>
            </div>

            <div class="activity-grid">
                <!-- نشاط الرياضيات -->
                <div class="activity-card">
                    <div class="text-center p-4">
                        <div class="activity-icon math">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <h4>الرياضيات المرحة</h4>
                        <p class="text-muted">تعلم الأرقام والحساب بطريقة ممتعة</p>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <span class="badge bg-success">سهل</span>
                            <span class="text-warning">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                                <i class="far fa-star"></i>
                            </span>
                        </div>
                        <a href="games/math-game.php" class="nibrass-btn nibrass-btn-primary w-100 mt-3">
                            <i class="fas fa-play"></i> ابدأ اللعب
                        </a>
                    </div>
                </div>

                <!-- نشاط اللغة -->
                <div class="activity-card">
                    <div class="text-center p-4">
                        <div class="activity-icon language">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <h4>عالم الحروف</h4>
                        <p class="text-muted">تعلم الحروف والكلمات وتكوين الجمل</p>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <span class="badge bg-info">متوسط</span>
                            <span class="text-warning">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </span>
                        </div>
                        <a href="games/language-game.php" class="nibrass-btn nibrass-btn-primary w-100 mt-3">
                            <i class="fas fa-play"></i> ابدأ اللعب
                        </a>
                    </div>
                </div>

                <!-- نشاط العلوم -->
                <div class="activity-card">
                    <div class="text-center p-4">
                        <div class="activity-icon science">
                            <i class="fas fa-flask"></i>
                        </div>
                        <h4>مختبر العلوم</h4>
                        <p class="text-muted">اكتشف عجائب العلوم والطبيعة</p>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <span class="badge bg-warning">متقدم</span>
                            <span class="text-warning">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                                <i class="far fa-star"></i>
                                <i class="far fa-star"></i>
                            </span>
                        </div>
                        <a href="activities.php?type=science" class="nibrass-btn nibrass-btn-primary w-100 mt-3">
                            <i class="fas fa-play"></i> ابدأ اللعب
                        </a>
                    </div>
                </div>

                <!-- نشاط الفنون -->
                <div class="activity-card">
                    <div class="text-center p-4">
                        <div class="activity-icon art">
                            <i class="fas fa-palette"></i>
                        </div>
                        <h4>الفنون والإبداع</h4>
                        <p class="text-muted">أطلق إبداعك في الرسم والتلوين</p>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <span class="badge bg-success">سهل</span>
                            <span class="text-warning">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </span>
                        </div>
                        <a href="activities.php?type=art" class="nibrass-btn nibrass-btn-primary w-100 mt-3">
                            <i class="fas fa-play"></i> ابدأ اللعب
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- الإنجازات والإحصائيات -->
<section class="section">
    <div class="container">
        <div class="row">
            <!-- الإنجازات الأخيرة -->
            <div class="col-lg-6">
                <div class="nibrass-card">
                    <div class="nibrass-card-header">
                        <div class="nibrass-card-icon warning">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div>
                            <h3 class="nibrass-card-title">إنجازاتي الأخيرة</h3>
                        </div>
                    </div>

                    <?php if (!empty($achievements)): ?>
                        <?php foreach ($achievements as $achievement): ?>
                            <div class="d-flex align-items-center p-3 mb-2 bg-light rounded">
                                <div class="achievement-icon me-3">
                                    <i class="fas fa-medal text-warning fa-2x"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1"><?php echo htmlspecialchars($achievement['title']); ?></h6>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i>
                                        <?php echo date('d/m/Y', strtotime($achievement['earned_date'])); ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد إنجازات بعد</h5>
                            <p class="text-muted">ابدأ بحل الأنشطة لتحصل على إنجازاتك الأولى!</p>
                        </div>
                    <?php endif; ?>

                    <div class="text-center mt-3">
                        <a href="achievements.php" class="nibrass-btn nibrass-btn-outline-primary">
                            <i class="fas fa-list"></i> عرض جميع الإنجازات
                        </a>
                    </div>
                </div>
            </div>

            <!-- الإحصائيات -->
            <div class="col-lg-6">
                <div class="nibrass-card">
                    <div class="nibrass-card-header">
                        <div class="nibrass-card-icon info">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div>
                            <h3 class="nibrass-card-title">إحصائياتي</h3>
                        </div>
                    </div>

                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="stat-item p-3 bg-light rounded">
                                <i class="fas fa-star fa-2x text-warning mb-2"></i>
                                <h4 class="text-primary"><?php echo $progress_stats['total_points']; ?></h4>
                                <small class="text-muted">إجمالي النقاط</small>
                            </div>
                        </div>

                        <div class="col-6 mb-3">
                            <div class="stat-item p-3 bg-light rounded">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h4 class="text-primary"><?php echo $progress_stats['completed_activities']; ?></h4>
                                <small class="text-muted">الأنشطة المكتملة</small>
                            </div>
                        </div>

                        <div class="col-6 mb-3">
                            <div class="stat-item p-3 bg-light rounded">
                                <i class="fas fa-level-up-alt fa-2x text-info mb-2"></i>
                                <h4 class="text-primary"><?php echo $level; ?></h4>
                                <small class="text-muted">المستوى الحالي</small>
                            </div>
                        </div>

                        <div class="col-6 mb-3">
                            <div class="stat-item p-3 bg-light rounded">
                                <i class="fas fa-medal fa-2x text-warning mb-2"></i>
                                <h4 class="text-primary"><?php echo count($achievements); ?></h4>
                                <small class="text-muted">الإنجازات</small>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <a href="progress.php" class="nibrass-btn nibrass-btn-outline-primary">
                            <i class="fas fa-chart-line"></i> تقرير التقدم التفصيلي
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<!-- قسم المستوى والتقدم -->
<section class="section">
    <div class="container">
        <div class="level-section">
            <div class="level-content">
                <div class="level-badge">
                <?php echo $level; ?>
                </div>
                <h3>المستوى <?php echo $level; ?></h3>
                <p>أنت تتقدم بشكل رائع! استمر في التعلم</p>

                <div class="progress-bar-custom">
                    <div class="progress-fill" style="width: <?php echo (100 - $points_to_next_level); ?>%"></div>
                </div>

                <p class="mt-2">
                    <i class="fas fa-star"></i>
                    تحتاج <?php echo $points_to_next_level; ?> نقطة للوصول للمستوى التالي
                </p>
            </div>
        </div>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    // تحريك أشرطة التقدم
    document.addEventListener('DOMContentLoaded', function() {
        const progressBars = document.querySelectorAll('.progress-fill');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });

        // تأثيرات التمرير للكروت
        const cards = document.querySelectorAll('.activity-card, .dashboard-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
