<?php
/**
 * Nibrass Platform - Missing Tables Setup
 * This script creates all the missing database tables needed for the platform
 */

require_once 'config.php';
require_once 'db.php';

// Check if user is admin or if this is initial setup
session_start();
$is_admin = isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
$is_setup = !file_exists('setup_complete.txt');

if (!$is_admin && !$is_setup) {
    die('Access denied. Admin privileges required.');
}

$success_messages = [];
$error_messages = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_tables'])) {
    try {
        // Read the SQL file
        $sql_file = 'database/create_missing_tables.sql';
        if (!file_exists($sql_file)) {
            throw new Exception('SQL file not found: ' . $sql_file);
        }
        
        $sql_content = file_get_contents($sql_file);
        
        // Split SQL statements
        $statements = array_filter(
            array_map('trim', explode(';', $sql_content)),
            function($stmt) {
                return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
            }
        );
        
        $db->beginTransaction();
        
        foreach ($statements as $statement) {
            if (trim($statement)) {
                try {
                    $db->exec($statement);
                    
                    // Extract table name for success message
                    if (preg_match('/CREATE TABLE.*?`?(\w+)`?\s*\(/i', $statement, $matches)) {
                        $success_messages[] = "تم إنشاء جدول: " . $matches[1];
                    } elseif (preg_match('/INSERT.*?INTO\s+`?(\w+)`?/i', $statement, $matches)) {
                        $success_messages[] = "تم إدراج بيانات في جدول: " . $matches[1];
                    }
                } catch (PDOException $e) {
                    // Skip if table already exists
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        throw $e;
                    }
                }
            }
        }
        
        $db->commit();
        $success_messages[] = "تم إنشاء جميع الجداول بنجاح!";
        
        // Create completion marker
        file_put_contents('tables_setup_complete.txt', date('Y-m-d H:i:s'));
        
    } catch (Exception $e) {
        $db->rollBack();
        $error_messages[] = "خطأ في إنشاء الجداول: " . $e->getMessage();
    }
}

// Check existing tables
try {
    $existing_tables = [];
    $result = $db->query("SHOW TABLES");
    while ($row = $result->fetch(PDO::FETCH_NUM)) {
        $existing_tables[] = $row[0];
    }
} catch (PDOException $e) {
    $error_messages[] = "خطأ في قراءة الجداول الموجودة: " . $e->getMessage();
    $existing_tables = [];
}

// Required tables list
$required_tables = [
    'student_progress' => 'تتبع تقدم الطلاب',
    'achievements' => 'الإنجازات المتاحة',
    'student_achievements' => 'إنجازات الطلاب',
    'activities' => 'الأنشطة التعليمية',
    'educational_videos' => 'الفيديوهات التعليمية',
    'stories' => 'القصص التفاعلية',
    'notifications' => 'الإشعارات',
    'consultation_requests' => 'طلبات الاستشارة',
    'appointments' => 'المواعيد',
    'ai_chat_conversations' => 'محادثات المساعد الذكي',
    'ai_chat_messages' => 'رسائل المساعد الذكي',
    'educational_resources' => 'الموارد التعليمية',
    'platform_settings' => 'إعدادات المنصة'
];

$missing_tables = array_diff(array_keys($required_tables), $existing_tables);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد الجداول المفقودة - نبراس</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/nibrass-unified.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', 'Tajawal', sans-serif;
        }
        
        .setup-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .setup-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            padding: 3rem;
        }
        
        .table-status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .status-exists {
            background: #d4edda;
            color: #155724;
        }
        
        .status-missing {
            background: #f8d7da;
            color: #721c24;
        }
        
        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 1rem 1.5rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="text-center mb-4">
                <i class="fas fa-database fa-3x text-primary mb-3"></i>
                <h1 class="h2">إعداد الجداول المفقودة</h1>
                <p class="text-muted">إنشاء الجداول الضرورية لتشغيل منصة نبراس</p>
            </div>

            <?php if (!empty($success_messages)): ?>
                <div class="alert alert-success alert-custom">
                    <i class="fas fa-check-circle"></i>
                    <strong>تم بنجاح!</strong>
                    <ul class="mb-0 mt-2">
                        <?php foreach ($success_messages as $message): ?>
                            <li><?php echo htmlspecialchars($message); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_messages)): ?>
                <div class="alert alert-danger alert-custom">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>خطأ!</strong>
                    <ul class="mb-0 mt-2">
                        <?php foreach ($error_messages as $message): ?>
                            <li><?php echo htmlspecialchars($message); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="text-center p-3 bg-light rounded">
                        <i class="fas fa-table fa-2x text-success mb-2"></i>
                        <h4 class="text-success"><?php echo count($existing_tables); ?></h4>
                        <small>جداول موجودة</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="text-center p-3 bg-light rounded">
                        <i class="fas fa-plus-circle fa-2x text-warning mb-2"></i>
                        <h4 class="text-warning"><?php echo count($missing_tables); ?></h4>
                        <small>جداول مفقودة</small>
                    </div>
                </div>
            </div>

            <h3><i class="fas fa-list"></i> حالة الجداول المطلوبة</h3>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم الجدول</th>
                            <th>الوصف</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($required_tables as $table => $description): ?>
                            <tr>
                                <td><code><?php echo $table; ?></code></td>
                                <td><?php echo $description; ?></td>
                                <td>
                                    <?php if (in_array($table, $existing_tables)): ?>
                                        <span class="table-status status-exists">
                                            <i class="fas fa-check"></i> موجود
                                        </span>
                                    <?php else: ?>
                                        <span class="table-status status-missing">
                                            <i class="fas fa-times"></i> مفقود
                                        </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <?php if (!empty($missing_tables)): ?>
                <div class="alert alert-warning alert-custom">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تحذير:</strong> يوجد <?php echo count($missing_tables); ?> جدول مفقود. 
                    يجب إنشاء هذه الجداول لضمان عمل المنصة بشكل صحيح.
                </div>

                <form method="POST" class="text-center">
                    <button type="submit" name="create_tables" class="nibrass-btn nibrass-btn-primary nibrass-btn-lg">
                        <i class="fas fa-plus-circle"></i> إنشاء الجداول المفقودة
                    </button>
                </form>
            <?php else: ?>
                <div class="alert alert-success alert-custom text-center">
                    <i class="fas fa-check-circle fa-2x mb-3"></i>
                    <h4>جميع الجداول موجودة!</h4>
                    <p class="mb-0">المنصة جاهزة للعمل بشكل كامل.</p>
                </div>

                <div class="text-center mt-4">
                    <a href="index.php" class="nibrass-btn nibrass-btn-success nibrass-btn-lg">
                        <i class="fas fa-home"></i> العودة للصفحة الرئيسية
                    </a>
                </div>
            <?php endif; ?>

            <hr class="my-4">
            
            <div class="text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    هذا الإعداد مطلوب مرة واحدة فقط. بعد إنشاء الجداول، ستعمل المنصة بكامل ميزاتها.
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
