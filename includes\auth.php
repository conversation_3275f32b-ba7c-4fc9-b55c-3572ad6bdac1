<?php
/**
 * ملف التحقق من الصلاحيات والمصادقة
 * Authentication and Authorization Helper
 */

require_once __DIR__ . '/../config.php';

// التحقق من تسجيل الدخول
function require_login() {
    if (!is_logged_in()) {
        redirect('login.php');
    }
}

// التحقق من نوع المستخدم
function require_role($required_role) {
    require_login();
    
    $user = current_user();
    if ($user['role'] !== $required_role) {
        redirect('index.php');
    }
}

// التحقق من صلاحيات متعددة
function require_roles($allowed_roles) {
    require_login();
    
    $user = current_user();
    if (!in_array($user['role'], $allowed_roles)) {
        redirect('index.php');
    }
}

// التحقق من كون المستخدم مدير
function require_admin() {
    require_role('admin');
}

// التحقق من كون المستخدم ولي أمر
function require_parent() {
    require_role('parent');
}

// التحقق من كون المستخدم أخصائي
function require_specialist() {
    require_role('specialist');
}

// التحقق من كون المستخدم طالب
function require_student() {
    require_role('student');
}

// الحصول على معلومات الطالب المرتبط بولي الأمر
function get_parent_students($parent_id) {
    global $db;
    
    try {
        $query = "SELECT s.*, u.name as student_name, u.email as student_email 
                  FROM students s 
                  JOIN users u ON s.user_id = u.id 
                  WHERE s.parent_id = :parent_id AND u.is_active = 1";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':parent_id', $parent_id);
        $stmt->execute();
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

// الحصول على معلومات الطالب
function get_student_info($user_id) {
    global $db;
    
    try {
        $query = "SELECT s.*, u.name, u.email, p.name as parent_name 
                  FROM students s 
                  JOIN users u ON s.user_id = u.id 
                  LEFT JOIN users p ON s.parent_id = p.id 
                  WHERE s.user_id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        return $stmt->fetch();
    } catch (PDOException $e) {
        return null;
    }
}

// تحديث نقاط الطالب
function update_student_stars($student_id, $weekly_stars = 0, $total_stars = 0) {
    global $db;
    
    try {
        $query = "UPDATE students SET 
                  weekly_stars = weekly_stars + :weekly_stars,
                  total_stars = total_stars + :total_stars,
                  updated_at = CURRENT_TIMESTAMP
                  WHERE id = :student_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':weekly_stars', $weekly_stars);
        $stmt->bindParam(':total_stars', $total_stars);
        $stmt->bindParam(':student_id', $student_id);
        return $stmt->execute();
    } catch (PDOException $e) {
        return false;
    }
}

// الحصول على إحصائيات المنصة
function get_platform_stats() {
    global $db;
    
    try {
        $stats = [];
        
        // عدد المستخدمين
        $query = "SELECT role, COUNT(*) as count FROM users WHERE is_active = 1 GROUP BY role";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $user_stats = $stmt->fetchAll();
        
        foreach ($user_stats as $stat) {
            $stats['users'][$stat['role']] = $stat['count'];
        }
        
        // عدد المقالات
        $query = "SELECT COUNT(*) as count FROM articles WHERE is_published = 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats['articles'] = $stmt->fetch()['count'];
        
        // عدد الوسائط
        $query = "SELECT COUNT(*) as count FROM media WHERE is_public = 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats['media'] = $stmt->fetch()['count'];
        
        // عدد الورشات
        $query = "SELECT COUNT(*) as count FROM workshops WHERE is_active = 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats['workshops'] = $stmt->fetch()['count'];
        
        // عدد الاستشارات
        $query = "SELECT status, COUNT(*) as count FROM feedback GROUP BY status";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $feedback_stats = $stmt->fetchAll();
        
        foreach ($feedback_stats as $stat) {
            $stats['feedback'][$stat['status']] = $stat['count'];
        }
        
        return $stats;
    } catch (PDOException $e) {
        return [];
    }
}

// الحصول على الأنشطة الأخيرة
function get_recent_activities($limit = 10) {
    global $db;
    
    try {
        $activities = [];
        
        // المقالات الجديدة
        $query = "SELECT 'article' as type, title as content, created_at 
                  FROM articles WHERE is_published = 1 
                  ORDER BY created_at DESC LIMIT :limit";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        $articles = $stmt->fetchAll();
        
        // الوسائط الجديدة
        $query = "SELECT 'media' as type, title as content, created_at 
                  FROM media WHERE is_public = 1 
                  ORDER BY created_at DESC LIMIT :limit";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        $media = $stmt->fetchAll();
        
        // دمج النتائج وترتيبها
        $activities = array_merge($articles, $media);
        usort($activities, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        return array_slice($activities, 0, $limit);
    } catch (PDOException $e) {
        return [];
    }
}

// تسجيل نشاط المستخدم
function log_user_activity($user_id, $activity_type, $description) {
    global $db;
    
    try {
        // يمكن إضافة جدول للأنشطة لاحقاً
        // حالياً سنحفظ في ملف log بسيط
        $log_entry = date('Y-m-d H:i:s') . " - User ID: $user_id - $activity_type: $description\n";
        file_put_contents('../logs/user_activities.log', $log_entry, FILE_APPEND | LOCK_EX);
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// إنشاء مجلد logs إذا لم يكن موجوداً
if (!file_exists('../logs')) {
    mkdir('../logs', 0777, true);
}
?>
