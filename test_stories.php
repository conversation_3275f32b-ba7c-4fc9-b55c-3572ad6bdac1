<?php
/**
 * اختبار جدول القصص التفاعلية
 * Test Interactive Stories Table
 */

require_once 'config.php';
require_once 'db.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار القصص التفاعلية - منصة نبراس</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }";
echo ".test-container { max-width: 1200px; margin: 2rem auto; padding: 2rem; }";
echo ".test-card { background: white; border-radius: 15px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }";
echo ".status-success { color: #28a745; }";
echo ".status-error { color: #dc3545; }";
echo ".status-warning { color: #ffc107; }";
echo ".story-card { border: 1px solid #ddd; border-radius: 10px; padding: 1rem; margin-bottom: 1rem; }";
echo ".category-badge { padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.8rem; margin: 0.2rem; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='test-container'>";
echo "<div class='test-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-book-open'></i> اختبار جدول القصص التفاعلية</h1>";

// اختبار وجود الجدول
echo "<h3><i class='fas fa-database'></i> اختبار وجود الجدول</h3>";
try {
    $check_table = $db->query("SHOW TABLES LIKE 'stories'");
    if ($check_table->rowCount() > 0) {
        echo "<p class='status-success'><i class='fas fa-check'></i> جدول stories: موجود</p>";
        
        // اختبار بنية الجدول
        $describe_result = $db->query("DESCRIBE stories");
        $columns = $describe_result->fetchAll();
        
        echo "<h4>بنية الجدول:</h4>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
        echo "<tbody>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
        
    } else {
        echo "<p class='status-error'><i class='fas fa-times'></i> جدول stories: غير موجود</p>";
        echo "<div class='alert alert-warning'>";
        echo "<h5>الجدول غير موجود!</h5>";
        echo "<p>يمكنك إنشاء الجدول باستخدام أحد الخيارات التالية:</p>";
        echo "<a href='create_stories_table.php' class='btn btn-primary me-2'><i class='fas fa-plus'></i> إنشاء جدول القصص</a>";
        echo "<a href='setup_missing_tables.php' class='btn btn-secondary'><i class='fas fa-tools'></i> إعداد جميع الجداول</a>";
        echo "</div>";
        echo "</div></div></body></html>";
        exit;
    }
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> خطأ في التحقق من الجدول: " . $e->getMessage() . "</p>";
    echo "</div></div></body></html>";
    exit;
}

// اختبار البيانات
echo "<h3><i class='fas fa-list'></i> اختبار البيانات</h3>";
try {
    // إحصائيات عامة
    $stats_query = "SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                        COUNT(DISTINCT category) as categories,
                        AVG(views_count) as avg_views,
                        AVG(likes_count) as avg_likes,
                        AVG(duration_minutes) as avg_duration
                    FROM stories";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result->fetch();
    
    echo "<div class='row mb-4'>";
    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-primary'>" . $stats['total'] . "</h4><p>إجمالي القصص</p></div></div>";
    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-success'>" . $stats['active'] . "</h4><p>قصص نشطة</p></div></div>";
    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-info'>" . $stats['categories'] . "</h4><p>فئات مختلفة</p></div></div>";
    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-warning'>" . number_format($stats['avg_views']) . "</h4><p>متوسط المشاهدات</p></div></div>";
    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-danger'>" . number_format($stats['avg_likes']) . "</h4><p>متوسط الإعجابات</p></div></div>";
    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-secondary'>" . number_format($stats['avg_duration']) . "</h4><p>متوسط المدة (دقيقة)</p></div></div>";
    echo "</div>";
    
    // إحصائيات الفئات
    echo "<h4>إحصائيات الفئات:</h4>";
    $categories_query = "SELECT category, COUNT(*) as count, AVG(views_count) as avg_views, AVG(likes_count) as avg_likes FROM stories GROUP BY category ORDER BY count DESC";
    $categories_result = $db->query($categories_query);
    $categories = $categories_result->fetchAll();
    
    echo "<table class='table table-striped'>";
    echo "<thead><tr><th>الفئة</th><th>عدد القصص</th><th>متوسط المشاهدات</th><th>متوسط الإعجابات</th></tr></thead>";
    echo "<tbody>";
    foreach ($categories as $category) {
        echo "<tr>";
        echo "<td><span class='category-badge bg-primary text-white'>" . htmlspecialchars($category['category']) . "</span></td>";
        echo "<td>" . $category['count'] . "</td>";
        echo "<td>" . number_format($category['avg_views']) . "</td>";
        echo "<td>" . number_format($category['avg_likes']) . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    
    // إحصائيات الأعمار المستهدفة
    echo "<h4>إحصائيات الأعمار المستهدفة:</h4>";
    $age_query = "SELECT target_age, COUNT(*) as count FROM stories GROUP BY target_age ORDER BY target_age";
    $age_result = $db->query($age_query);
    $ages = $age_result->fetchAll();
    
    echo "<div class='row'>";
    foreach ($ages as $age) {
        echo "<div class='col-md-3 mb-2'>";
        echo "<div class='text-center p-2 border rounded'>";
        echo "<h5>" . $age['target_age'] . " سنوات</h5>";
        echo "<p class='mb-0'>" . $age['count'] . " قصة</p>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    // عرض بعض القصص
    echo "<h4>عينة من القصص:</h4>";
    $stories_query = "SELECT * FROM stories ORDER BY views_count DESC LIMIT 5";
    $stories_result = $db->query($stories_query);
    $stories = $stories_result->fetchAll();
    
    if (!empty($stories)) {
        foreach ($stories as $story) {
            echo "<div class='story-card'>";
            echo "<div class='row'>";
            echo "<div class='col-md-8'>";
            echo "<h5>" . htmlspecialchars($story['title']) . "</h5>";
            echo "<p class='text-muted'>" . htmlspecialchars(substr($story['description'], 0, 150)) . "...</p>";
            echo "<div class='mb-2'>";
            echo "<span class='category-badge bg-primary text-white'>" . htmlspecialchars($story['category']) . "</span>";
            echo "<span class='category-badge bg-secondary text-white'>" . htmlspecialchars($story['difficulty_level']) . "</span>";
            echo "<span class='category-badge bg-info text-white'>" . $story['target_age'] . " سنوات</span>";
            echo "<span class='category-badge bg-success text-white'>" . $story['duration_minutes'] . " دقيقة</span>";
            echo "</div>";
            if ($story['moral_lesson']) {
                echo "<p class='small text-success'><i class='fas fa-lightbulb'></i> <strong>العبرة:</strong> " . htmlspecialchars($story['moral_lesson']) . "</p>";
            }
            echo "</div>";
            echo "<div class='col-md-4 text-end'>";
            echo "<p><i class='fas fa-eye'></i> " . number_format($story['views_count']) . " مشاهدة</p>";
            echo "<p><i class='fas fa-heart'></i> " . number_format($story['likes_count']) . " إعجاب</p>";
            echo "<p><i class='fas fa-user'></i> " . htmlspecialchars($story['author']) . "</p>";
            echo "<p><i class='fas fa-calendar'></i> " . date('Y-m-d', strtotime($story['created_at'])) . "</p>";
            echo "<p><i class='fas fa-" . ($story['is_active'] ? 'check text-success' : 'times text-danger') . "'></i> " . ($story['is_active'] ? 'نشط' : 'غير نشط') . "</p>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-info'>";
        echo "<i class='fas fa-info-circle'></i> لا توجد قصص في الجدول";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> خطأ في جلب البيانات: " . $e->getMessage() . "</p>";
}

echo "</div>";

// اختبار الوظائف
echo "<div class='test-card'>";
echo "<h2><i class='fas fa-cogs'></i> اختبار الوظائف</h2>";

// اختبار إدراج قصة جديدة
echo "<h4>اختبار إدراج قصة جديدة:</h4>";
try {
    $test_story = [
        'title' => 'قصة اختبار - ' . date('Y-m-d H:i:s'),
        'description' => 'هذه قصة اختبار تم إنشاؤها تلقائياً للتحقق من عمل النظام',
        'content' => 'محتوى قصة الاختبار...',
        'category' => 'عامة',
        'difficulty_level' => 'easy',
        'target_age' => 5,
        'duration_minutes' => 5,
        'author' => 'نظام الاختبار'
    ];
    
    $insert_query = "INSERT INTO stories (title, description, content, category, difficulty_level, target_age, duration_minutes, author) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    $insert_stmt = $db->prepare($insert_query);
    $result = $insert_stmt->execute([
        $test_story['title'],
        $test_story['description'],
        $test_story['content'],
        $test_story['category'],
        $test_story['difficulty_level'],
        $test_story['target_age'],
        $test_story['duration_minutes'],
        $test_story['author']
    ]);
    
    if ($result) {
        $new_id = $db->lastInsertId();
        echo "<p class='status-success'><i class='fas fa-check'></i> تم إدراج قصة اختبار بنجاح (ID: $new_id)</p>";
        
        // حذف القصة التجريبية
        $delete_stmt = $db->prepare("DELETE FROM stories WHERE id = ?");
        $delete_stmt->execute([$new_id]);
        echo "<p class='status-success'><i class='fas fa-check'></i> تم حذف قصة الاختبار بنجاح</p>";
    } else {
        echo "<p class='status-error'><i class='fas fa-times'></i> فشل في إدراج قصة الاختبار</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='status-error'><i class='fas fa-times'></i> خطأ في اختبار الإدراج: " . $e->getMessage() . "</p>";
}

echo "</div>";

// روابط مفيدة
echo "<div class='test-card'>";
echo "<h2><i class='fas fa-link'></i> روابط مفيدة</h2>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>للمديرين:</h5>";
echo "<ul>";
echo "<li><a href='admin/stories.php'>إدارة القصص</a></li>";
echo "<li><a href='admin/dashboard.php'>لوحة تحكم الإدارة</a></li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h5>للطلاب:</h5>";
echo "<ul>";
echo "<li><a href='student-space/stories.php'>قراءة القصص</a></li>";
echo "<li><a href='student-space/dashboard.php'>لوحة تحكم الطالب</a></li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-info mt-3'>";
echo "<h5><i class='fas fa-info-circle'></i> ملاحظات:</h5>";
echo "<ul>";
echo "<li>تأكد من وجود جدول stories في قاعدة البيانات</li>";
echo "<li>تحقق من محتوى القصص وملاءمتها للأعمار المستهدفة</li>";
echo "<li>راجع الدروس الأخلاقية في كل قصة</li>";
echo "<li>تأكد من تصنيف القصص حسب الفئات المناسبة</li>";
echo "<li>اختبر الميزات التفاعلية للقصص</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";
?>
