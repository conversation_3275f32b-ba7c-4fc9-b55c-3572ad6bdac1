<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كأخصائي
require_specialist();

$user = current_user();
$consultation_id = isset($_GET['consultation_id']) ? (int)$_GET['consultation_id'] : 0;
$student_id = isset($_GET['student_id']) ? (int)$_GET['student_id'] : 0;

// جلب بيانات الاستشارة إذا كانت موجودة
$consultation = null;
if ($consultation_id) {
    try {
        $consultation_query = "SELECT c.*, p.name as parent_name, s.name as student_name, s.id as student_id
                               FROM consultations c
                               LEFT JOIN users p ON c.parent_id = p.id
                               LEFT JOIN students s ON c.student_id = s.id
                               WHERE c.id = :id";
        $consultation_stmt = $db->prepare($consultation_query);
        $consultation_stmt->bindParam(':id', $consultation_id);
        $consultation_stmt->execute();
        $consultation = $consultation_stmt->fetch();
    } catch (PDOException $e) {
        $error_message = "حدث خطأ في جلب بيانات الاستشارة";
    }
}

// جلب بيانات الطالب إذا كان محدد
$student = null;
if ($student_id) {
    try {
        $student_query = "SELECT s.*, p.name as parent_name, p.id as parent_id
                          FROM students s
                          LEFT JOIN users p ON s.parent_id = p.id
                          WHERE s.id = :id";
        $student_stmt = $db->prepare($student_query);
        $student_stmt->bindParam(':id', $student_id);
        $student_stmt->execute();
        $student = $student_stmt->fetch();
    } catch (PDOException $e) {
        $error_message = "حدث خطأ في جلب بيانات الطالب";
    }
}

// معالجة حجز الموعد
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $parent_id = (int)$_POST['parent_id'];
    $student_id = (int)$_POST['student_id'];
    $appointment_date = clean_input($_POST['appointment_date']);
    $appointment_time = clean_input($_POST['appointment_time']);
    $duration_minutes = (int)$_POST['duration_minutes'];
    $appointment_type = clean_input($_POST['appointment_type']);
    $notes = clean_input($_POST['notes']);
    $meeting_link = clean_input($_POST['meeting_link']);
    
    // دمج التاريخ والوقت
    $appointment_datetime = $appointment_date . ' ' . $appointment_time;
    
    try {
        $insert_query = "INSERT INTO appointments (specialist_id, parent_id, student_id, consultation_id, 
                                                  appointment_date, duration_minutes, appointment_type, 
                                                  notes, meeting_link, status) 
                         VALUES (:specialist_id, :parent_id, :student_id, :consultation_id, 
                                :appointment_date, :duration_minutes, :appointment_type, 
                                :notes, :meeting_link, 'scheduled')";
        $insert_stmt = $db->prepare($insert_query);
        $insert_stmt->execute([
            ':specialist_id' => $user['id'],
            ':parent_id' => $parent_id,
            ':student_id' => $student_id,
            ':consultation_id' => $consultation_id ?: null,
            ':appointment_date' => $appointment_datetime,
            ':duration_minutes' => $duration_minutes,
            ':appointment_type' => $appointment_type,
            ':notes' => $notes,
            ':meeting_link' => $meeting_link
        ]);
        
        $success_message = "تم حجز الموعد بنجاح";
        
        // تحديث حالة الاستشارة إذا كانت موجودة
        if ($consultation_id) {
            $update_consultation_query = "UPDATE consultations SET status = 'scheduled' WHERE id = :id";
            $update_consultation_stmt = $db->prepare($update_consultation_query);
            $update_consultation_stmt->execute([':id' => $consultation_id]);
        }
        
    } catch (PDOException $e) {
        $error_message = "حدث خطأ في حجز الموعد: " . $e->getMessage();
    }
}

// جلب جميع أولياء الأمور والطلاب
try {
    $parents_query = "SELECT DISTINCT p.id, p.name, p.phone FROM users p 
                      INNER JOIN students s ON p.id = s.parent_id 
                      WHERE p.role = 'parent' ORDER BY p.name";
    $parents_stmt = $db->prepare($parents_query);
    $parents_stmt->execute();
    $parents = $parents_stmt->fetchAll();
    
    $students_query = "SELECT s.*, p.name as parent_name FROM students s 
                       LEFT JOIN users p ON s.parent_id = p.id 
                       ORDER BY s.name";
    $students_stmt = $db->prepare($students_query);
    $students_stmt->execute();
    $students = $students_stmt->fetchAll();
} catch (PDOException $e) {
    $parents = [];
    $students = [];
}

// إعداد متغيرات الصفحة
$page_title = 'حجز موعد جديد';
$page_description = 'حجز موعد جديد مع المرضى';

// تضمين الهيدر
include '../includes/header.php';
?>

<style>
.schedule-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.schedule-header h1,
.schedule-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

/* تأكيد ظهور الأيقونات */
.schedule-header i,
.schedule-form i,
.section-title i,
.btn i,
.alert i,
.consultation-info i {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

.schedule-form {
    background: white;
    border-radius: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    padding: 3rem;
    margin-bottom: 2rem;
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #f8f9fa;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.section-title i {
    margin-left: 0.5rem;
    color: #4a90e2;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.btn-schedule {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    border: none;
    color: white;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.btn-schedule:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(74, 144, 226, 0.3);
    color: white;
}

.consultation-info {
    background: #e3f2fd;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-right: 4px solid #2196f3;
}

.time-slots {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
    margin-top: 1rem;
}

.time-slot {
    padding: 0.5rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.time-slot:hover {
    border-color: #4a90e2;
    background: #f0f7ff;
}

.time-slot.selected {
    border-color: #4a90e2;
    background: #4a90e2;
    color: white;
}


</style>

<!-- قسم العنوان -->
<section class="schedule-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-calendar-plus"></i> حجز موعد جديد</h1>
            <p class="lead">حجز موعد جديد مع المرضى وأولياء الأمور</p>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                <div class="mt-2">
                    <a href="appointments.php" class="btn btn-sm btn-outline-success">عرض جميع المواعيد</a>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- معلومات الاستشارة إذا كانت موجودة -->
        <?php if ($consultation): ?>
        <div class="consultation-info">
            <h5><i class="fas fa-info-circle"></i> معلومات الاستشارة المرتبطة</h5>
            <p><strong>الموضوع:</strong> <?php echo htmlspecialchars($consultation['subject']); ?></p>
            <p><strong>ولي الأمر:</strong> <?php echo htmlspecialchars($consultation['parent_name']); ?></p>
            <?php if ($consultation['student_name']): ?>
            <p><strong>الطفل:</strong> <?php echo htmlspecialchars($consultation['student_name']); ?></p>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- نموذج حجز الموعد -->
        <div class="schedule-form">
            <form method="POST" id="scheduleForm">
                <!-- معلومات المريض -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-user"></i>
                        معلومات المريض
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <label for="parent_id" class="form-label">ولي الأمر:</label>
                            <select class="form-control" id="parent_id" name="parent_id" required>
                                <option value="">اختر ولي الأمر</option>
                                <?php foreach ($parents as $parent): ?>
                                <option value="<?php echo $parent['id']; ?>" 
                                        <?php echo ($consultation && $consultation['parent_id'] == $parent['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($parent['name']); ?>
                                    <?php if ($parent['phone']): ?>
                                        - <?php echo htmlspecialchars($parent['phone']); ?>
                                    <?php endif; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="student_id" class="form-label">الطفل:</label>
                            <select class="form-control" id="student_id" name="student_id" required>
                                <option value="">اختر الطفل</option>
                                <?php foreach ($students as $student_option): ?>
                                <option value="<?php echo $student_option['id']; ?>" 
                                        data-parent="<?php echo $student_option['parent_id']; ?>"
                                        <?php echo (($consultation && $consultation['student_id'] == $student_option['id']) || 
                                                   ($student && $student['id'] == $student_option['id'])) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($student_option['name']); ?> 
                                    (<?php echo $student_option['age']; ?> سنة) - 
                                    <?php echo htmlspecialchars($student_option['parent_name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الموعد -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-calendar"></i>
                        تفاصيل الموعد
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <label for="appointment_date" class="form-label">التاريخ:</label>
                            <input type="date" class="form-control" id="appointment_date" name="appointment_date" 
                                   min="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="appointment_time" class="form-label">الوقت:</label>
                            <input type="time" class="form-control" id="appointment_time" name="appointment_time" required>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="duration_minutes" class="form-label">المدة (بالدقائق):</label>
                            <select class="form-control" id="duration_minutes" name="duration_minutes" required>
                                <option value="30">30 دقيقة</option>
                                <option value="45">45 دقيقة</option>
                                <option value="60" selected>60 دقيقة</option>
                                <option value="90">90 دقيقة</option>
                                <option value="120">120 دقيقة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="appointment_type" class="form-label">نوع الموعد:</label>
                            <select class="form-control" id="appointment_type" name="appointment_type" required>
                                <option value="">اختر نوع الموعد</option>
                                <option value="استشارة أولية">استشارة أولية</option>
                                <option value="متابعة">متابعة</option>
                                <option value="تقييم">تقييم</option>
                                <option value="علاج سلوكي">علاج سلوكي</option>
                                <option value="علاج نطق">علاج نطق</option>
                                <option value="تدريب مهارات">تدريب مهارات</option>
                                <option value="استشارة عائلية">استشارة عائلية</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="meeting_link" class="form-label">رابط الاجتماع (اختياري):</label>
                            <input type="url" class="form-control" id="meeting_link" name="meeting_link" 
                                   placeholder="https://zoom.us/j/...">
                        </div>
                    </div>
                </div>

                <!-- ملاحظات إضافية -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-sticky-note"></i>
                        ملاحظات إضافية
                    </h4>
                    
                    <div class="row">
                        <div class="col-12">
                            <label for="notes" class="form-label">ملاحظات:</label>
                            <textarea class="form-control" id="notes" name="notes" rows="4" 
                                      placeholder="أي ملاحظات أو تعليمات خاصة بالموعد..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="text-center">
                    <button type="submit" class="btn btn-schedule">
                        <i class="fas fa-calendar-plus"></i> حجز الموعد
                    </button>
                    <a href="appointments.php" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
                
                <!-- حقول مخفية -->
                <?php if ($consultation_id): ?>
                <input type="hidden" name="consultation_id" value="<?php echo $consultation_id; ?>">
                <?php endif; ?>
            </form>
        </div>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    // ربط اختيار ولي الأمر بالطفل
    document.getElementById('parent_id').addEventListener('change', function() {
        const parentId = this.value;
        const studentSelect = document.getElementById('student_id');
        const studentOptions = studentSelect.querySelectorAll('option');
        
        studentOptions.forEach(option => {
            if (option.value === '') {
                option.style.display = 'block';
                return;
            }
            
            const optionParentId = option.getAttribute('data-parent');
            if (parentId === '' || optionParentId === parentId) {
                option.style.display = 'block';
            } else {
                option.style.display = 'none';
            }
        });
        
        // إعادة تعيين اختيار الطفل
        studentSelect.value = '';
    });
    
    // ربط اختيار الطفل بولي الأمر
    document.getElementById('student_id').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value !== '') {
            const parentId = selectedOption.getAttribute('data-parent');
            document.getElementById('parent_id').value = parentId;
        }
    });
    
    // تحديد الحد الأدنى للتاريخ (اليوم)
    document.getElementById('appointment_date').min = new Date().toISOString().split('T')[0];
    
    // تحديد الحد الأدنى للوقت إذا كان التاريخ اليوم
    document.getElementById('appointment_date').addEventListener('change', function() {
        const selectedDate = new Date(this.value);
        const today = new Date();
        const timeInput = document.getElementById('appointment_time');
        
        if (selectedDate.toDateString() === today.toDateString()) {
            const currentTime = today.getHours().toString().padStart(2, '0') + ':' + 
                               today.getMinutes().toString().padStart(2, '0');
            timeInput.min = currentTime;
        } else {
            timeInput.removeAttribute('min');
        }
    });
    
    // التحقق من صحة النموذج
    document.getElementById('scheduleForm').addEventListener('submit', function(e) {
        const appointmentDate = new Date(document.getElementById('appointment_date').value + ' ' + 
                                       document.getElementById('appointment_time').value);
        const now = new Date();
        
        if (appointmentDate <= now) {
            e.preventDefault();
            alert('يجب أن يكون موعد الاجتماع في المستقبل');
            return false;
        }
        
        // التحقق من أوقات العمل (9 صباحاً - 6 مساءً)
        const hour = appointmentDate.getHours();
        if (hour < 9 || hour >= 18) {
            if (!confirm('الموعد خارج أوقات العمل المعتادة (9 صباحاً - 6 مساءً). هل تريد المتابعة؟')) {
                e.preventDefault();
                return false;
            }
        }
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
