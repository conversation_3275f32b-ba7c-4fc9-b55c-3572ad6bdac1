<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كولي أمر
require_parent();

$user = current_user();
$success_message = '';
$error_message = '';

// إعداد متغيرات الصفحة
$page_title = 'إضافة طفل جديد';
$page_description = 'إضافة طفل جديد لحسابك في منصة نبراس';

// معالجة إضافة طالب جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $student_name = clean_input($_POST['student_name']);
    $student_email = clean_input($_POST['student_email']);
    $birth_date = clean_input($_POST['birth_date']);
    $diagnosis_level = clean_input($_POST['diagnosis_level']);
    $interests = clean_input($_POST['interests']);
    
    if (empty($student_name) || empty($student_email) || empty($birth_date)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        try {
            // التحقق من عدم وجود البريد الإلكتروني مسبقاً
            $check_query = "SELECT id FROM users WHERE email = :email";
            $check_stmt = $db->prepare($check_query);
            $check_stmt->bindParam(':email', $student_email);
            $check_stmt->execute();
            
            if ($check_stmt->rowCount() > 0) {
                $error_message = 'البريد الإلكتروني مسجل مسبقاً';
            } else {
                // إنشاء حساب للطالب
                $student_password = password_hash('student123', PASSWORD_DEFAULT);
                
                $insert_user_query = "INSERT INTO users (name, email, password, role) VALUES (:name, :email, :password, 'student')";
                $insert_user_stmt = $db->prepare($insert_user_query);
                $insert_user_stmt->bindParam(':name', $student_name);
                $insert_user_stmt->bindParam(':email', $student_email);
                $insert_user_stmt->bindParam(':password', $student_password);
                
                if ($insert_user_stmt->execute()) {
                    $student_user_id = $db->lastInsertId();
                    
                    // إضافة معلومات الطالب
                    $insert_student_query = "INSERT INTO students (user_id, parent_id, birth_date, diagnosis_level, interests) 
                                           VALUES (:user_id, :parent_id, :birth_date, :diagnosis_level, :interests)";
                    $insert_student_stmt = $db->prepare($insert_student_query);
                    $insert_student_stmt->bindParam(':user_id', $student_user_id);
                    $insert_student_stmt->bindParam(':parent_id', $user['id']);
                    $insert_student_stmt->bindParam(':birth_date', $birth_date);
                    $insert_student_stmt->bindParam(':diagnosis_level', $diagnosis_level);
                    $insert_student_stmt->bindParam(':interests', $interests);
                    
                    if ($insert_student_stmt->execute()) {
                        $success_message = 'تم إضافة الطالب بنجاح! كلمة المرور الافتراضية: student123';
                        // مسح البيانات من النموذج
                        $_POST = array();
                    } else {
                        $error_message = 'حدث خطأ أثناء إضافة معلومات الطالب';
                    }
                } else {
                    $error_message = 'حدث خطأ أثناء إنشاء حساب الطالب';
                }
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
        }
    }
}

// تضمين الهيدر
include '../includes/header.php';
?>

<style>
.add-student-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.add-student-header h1,
.add-student-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}
</style>

<!-- قسم العنوان -->
<section class="add-student-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-user-plus"></i> إضافة طفل جديد</h1>
            <p class="lead">إضافة طفل جديد لحسابك في منصة نبراس</p>
        </div>
    </div>
</section>

    <!-- المحتوى الرئيسي -->
    <section class="section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header text-center" style="background-color: var(--primary-color); color: white;">
                            <h3><i class="fas fa-user-plus"></i> إضافة طالب جديد</h3>
                            <p class="mb-0">أضف معلومات طفلك للاستفادة من خدمات المنصة</p>
                        </div>
                        
                        <div class="card-body p-4">
                            <?php if (!empty($success_message)): ?>
                                <div class="alert alert-success alert-arabic">
                                    <i class="fas fa-check-circle"></i>
                                    <?php echo $success_message; ?>
                                    <div class="mt-2">
                                        <a href="dashboard.php" class="btn btn-success btn-sm">العودة للوحة التحكم</a>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($error_message)): ?>
                                <div class="alert alert-danger alert-arabic">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <?php echo $error_message; ?>
                                </div>
                            <?php endif; ?>

                            <form method="POST" action="" class="form-arabic">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="student_name" class="form-label">
                                                <i class="fas fa-child"></i> اسم الطالب *
                                            </label>
                                            <input type="text" class="form-control" id="student_name" name="student_name" 
                                                   value="<?php echo isset($_POST['student_name']) ? htmlspecialchars($_POST['student_name']) : ''; ?>" 
                                                   required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="student_email" class="form-label">
                                                <i class="fas fa-envelope"></i> البريد الإلكتروني *
                                            </label>
                                            <input type="email" class="form-control" id="student_email" name="student_email" 
                                                   value="<?php echo isset($_POST['student_email']) ? htmlspecialchars($_POST['student_email']) : ''; ?>" 
                                                   required>
                                            <small class="text-muted">سيتم إنشاء حساب للطالب بهذا البريد</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="birth_date" class="form-label">
                                                <i class="fas fa-calendar"></i> تاريخ الميلاد *
                                            </label>
                                            <input type="date" class="form-control" id="birth_date" name="birth_date" 
                                                   value="<?php echo isset($_POST['birth_date']) ? htmlspecialchars($_POST['birth_date']) : ''; ?>" 
                                                   required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="diagnosis_level" class="form-label">
                                                <i class="fas fa-chart-line"></i> مستوى التشخيص
                                            </label>
                                            <select class="form-control" id="diagnosis_level" name="diagnosis_level">
                                                <option value="">اختر المستوى</option>
                                                <option value="mild" <?php echo (isset($_POST['diagnosis_level']) && $_POST['diagnosis_level'] == 'mild') ? 'selected' : ''; ?>>
                                                    خفيف
                                                </option>
                                                <option value="moderate" <?php echo (isset($_POST['diagnosis_level']) && $_POST['diagnosis_level'] == 'moderate') ? 'selected' : ''; ?>>
                                                    متوسط
                                                </option>
                                                <option value="severe" <?php echo (isset($_POST['diagnosis_level']) && $_POST['diagnosis_level'] == 'severe') ? 'selected' : ''; ?>>
                                                    شديد
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-4">
                                    <label for="interests" class="form-label">
                                        <i class="fas fa-heart"></i> الاهتمامات والهوايات
                                    </label>
                                    <textarea class="form-control" id="interests" name="interests" rows="3" 
                                              placeholder="اكتب اهتمامات وهوايات الطفل (مثل: الرسم، الموسيقى، الألعاب...)"><?php echo isset($_POST['interests']) ? htmlspecialchars($_POST['interests']) : ''; ?></textarea>
                                </div>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> معلومات مهمة:</h6>
                                    <ul class="mb-0">
                                        <li>سيتم إنشاء حساب منفصل للطالب بكلمة المرور الافتراضية: <strong>student123</strong></li>
                                        <li>يمكن للطالب تغيير كلمة المرور لاحقاً من ملفه الشخصي</li>
                                        <li>ستتمكن من متابعة تقدم الطالب من لوحة التحكم الخاصة بك</li>
                                    </ul>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg btn-arabic">
                                        <i class="fas fa-plus-circle"></i> إضافة الطالب
                                    </button>
                                    <a href="dashboard.php" class="btn btn-secondary btn-lg ms-3">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // التحقق من صحة التاريخ
        document.getElementById('birth_date').addEventListener('change', function() {
            const birthDate = new Date(this.value);
            const today = new Date();
            const age = today.getFullYear() - birthDate.getFullYear();
            
            if (age < 2 || age > 18) {
                alert('يرجى التأكد من صحة تاريخ الميلاد. العمر المناسب للمنصة من 2 إلى 18 سنة.');
                this.value = '';
            }
        });
        
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            form.addEventListener('submit', function() {
                const submitBtn = document.querySelector('button[type="submit"]');
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';
                submitBtn.disabled = true;
            });
        });
    </script>

<?php
// تضمين الفوتر
include '../includes/footer.php';
?>
