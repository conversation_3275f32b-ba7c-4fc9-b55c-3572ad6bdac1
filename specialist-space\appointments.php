<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كأخصائي
require_specialist();

$user = current_user();

// معالجة تحديث حالة الموعد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $appointment_id = (int)$_POST['appointment_id'];
    $action = $_POST['action'];
    
    try {
        if ($action == 'confirm') {
            $update_query = "UPDATE appointments SET status = 'confirmed' WHERE id = :id AND specialist_id = :specialist_id";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->execute([':id' => $appointment_id, ':specialist_id' => $user['id']]);
            $success_message = "تم تأكيد الموعد بنجاح";
        } elseif ($action == 'complete') {
            $update_query = "UPDATE appointments SET status = 'completed' WHERE id = :id AND specialist_id = :specialist_id";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->execute([':id' => $appointment_id, ':specialist_id' => $user['id']]);
            $success_message = "تم إكمال الموعد بنجاح";
        } elseif ($action == 'cancel') {
            $update_query = "UPDATE appointments SET status = 'cancelled' WHERE id = :id AND specialist_id = :specialist_id";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->execute([':id' => $appointment_id, ':specialist_id' => $user['id']]);
            $success_message = "تم إلغاء الموعد بنجاح";
        }
    } catch (PDOException $e) {
        $error_message = "حدث خطأ في تحديث الموعد";
    }
}

// جلب جميع المواعيد
try {
    $appointments_query = "SELECT a.*, p.name as parent_name, p.phone as parent_phone, 
                                  s.name as student_name, s.age, s.diagnosis_level,
                                  c.subject as consultation_subject
                           FROM appointments a
                           LEFT JOIN users p ON a.parent_id = p.id
                           LEFT JOIN students s ON a.student_id = s.id
                           LEFT JOIN consultations c ON a.consultation_id = c.id
                           WHERE a.specialist_id = :specialist_id
                           ORDER BY 
                               CASE a.status 
                                   WHEN 'scheduled' THEN 1 
                                   WHEN 'confirmed' THEN 2 
                                   WHEN 'completed' THEN 3 
                                   WHEN 'cancelled' THEN 4 
                               END,
                               a.appointment_date ASC";
    $appointments_stmt = $db->prepare($appointments_query);
    $appointments_stmt->bindParam(':specialist_id', $user['id']);
    $appointments_stmt->execute();
    $appointments = $appointments_stmt->fetchAll();
} catch (PDOException $e) {
    $appointments = [];
    $error_message = "حدث خطأ في جلب المواعيد";
}

// تصنيف المواعيد
$upcoming_appointments = array_filter($appointments, function($app) {
    return strtotime($app['appointment_date']) >= time() && in_array($app['status'], ['scheduled', 'confirmed']);
});

$past_appointments = array_filter($appointments, function($app) {
    return strtotime($app['appointment_date']) < time() || in_array($app['status'], ['completed', 'cancelled']);
});

// إعداد متغيرات الصفحة
$page_title = 'إدارة المواعيد';
$page_description = 'إدارة ومتابعة جميع المواعيد';

// تضمين الهيدر
include '../includes/header.php';
?>

<style>
.appointments-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.appointments-header h1,
.appointments-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

/* تأكيد ظهور الأيقونات */
.appointments-header i,
.appointment-card i,
.btn i,
.alert i,
.badge i,
.nav-link i {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

.appointment-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border-right: 4px solid transparent;
}

.appointment-card:hover {
    transform: translateX(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.appointment-card.scheduled { border-right-color: #ffc107; }
.appointment-card.confirmed { border-right-color: #28a745; }
.appointment-card.completed { border-right-color: #6c757d; }
.appointment-card.cancelled { border-right-color: #dc3545; }

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-scheduled { background: #fff3cd; color: #856404; }
.status-confirmed { background: #d4edda; color: #155724; }
.status-completed { background: #e2e3e5; color: #383d41; }
.status-cancelled { background: #f8d7da; color: #721c24; }

.tabs-arabic {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 2rem;
}

.tabs-arabic .nav-link {
    color: #666 !important;
    font-weight: 600;
    border: none;
    border-bottom: 3px solid transparent;
    padding: 1rem 1.5rem;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: transparent !important;
    text-decoration: none;
}

.tabs-arabic .nav-link.active {
    color: #4a90e2 !important;
    border-bottom-color: #4a90e2;
    background: none !important;
}

.tabs-arabic .nav-link:hover {
    color: #4a90e2 !important;
    border-bottom-color: rgba(74, 144, 226, 0.3);
    background: transparent !important;
}


</style>

<!-- قسم العنوان -->
<section class="appointments-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-calendar-check"></i> إدارة المواعيد</h1>
            <p class="lead">إدارة ومتابعة جميع مواعيدك مع المرضى</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-clock"></i> <?php echo count($upcoming_appointments); ?> موعد قادم
                </span>
                <span class="badge bg-light text-dark fs-6">
                    <i class="fas fa-history"></i> <?php echo count($past_appointments); ?> موعد سابق
                </span>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- أزرار الإجراءات -->
        <div class="row mb-4">
            <div class="col-md-6">
                <a href="schedule-appointment.php" class="btn btn-warning btn-lg">
                    <i class="fas fa-plus"></i> حجز موعد جديد
                </a>
            </div>
            <div class="col-md-6 text-end">
                <div class="btn-group">
                    <button class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-outline-secondary" onclick="exportAppointments()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>
        </div>

        <!-- التبويبات -->
        <ul class="nav nav-tabs tabs-arabic" id="appointmentTabs">
            <li class="nav-item">
                <a class="nav-link active" data-bs-toggle="tab" href="#upcoming">
                    <i class="fas fa-clock"></i> المواعيد القادمة (<?php echo count($upcoming_appointments); ?>)
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#past">
                    <i class="fas fa-history"></i> المواعيد السابقة (<?php echo count($past_appointments); ?>)
                </a>
            </li>
        </ul>

        <!-- محتوى التبويبات -->
        <div class="tab-content">
            <!-- المواعيد القادمة -->
            <div class="tab-pane fade show active" id="upcoming">
                <?php if (!empty($upcoming_appointments)): ?>
                    <?php foreach ($upcoming_appointments as $appointment): ?>
                        <div class="appointment-card <?php echo $appointment['status']; ?>">
                            <div class="row align-items-center">
                                <div class="col-lg-8">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h5 class="mb-1">
                                            موعد مع <?php echo htmlspecialchars($appointment['parent_name']); ?>
                                            <?php if ($appointment['student_name']): ?>
                                                - <?php echo htmlspecialchars($appointment['student_name']); ?>
                                            <?php endif; ?>
                                        </h5>
                                        <span class="status-badge status-<?php echo $appointment['status']; ?>">
                                            <?php 
                                            $status_labels = [
                                                'scheduled' => 'مجدول',
                                                'confirmed' => 'مؤكد',
                                                'completed' => 'مكتمل',
                                                'cancelled' => 'ملغي'
                                            ];
                                            echo $status_labels[$appointment['status']] ?? $appointment['status'];
                                            ?>
                                        </span>
                                    </div>
                                    
                                    <?php if ($appointment['consultation_subject']): ?>
                                    <p class="text-muted mb-2">الموضوع: <?php echo htmlspecialchars($appointment['consultation_subject']); ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="d-flex flex-wrap gap-3 text-muted small">
                                        <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y', strtotime($appointment['appointment_date'])); ?></span>
                                        <span><i class="fas fa-clock"></i> <?php echo date('H:i', strtotime($appointment['appointment_date'])); ?></span>
                                        <span><i class="fas fa-hourglass-half"></i> <?php echo $appointment['duration_minutes']; ?> دقيقة</span>
                                        <span><i class="fas fa-tag"></i> <?php echo $appointment['appointment_type']; ?></span>
                                        <?php if ($appointment['parent_phone']): ?>
                                        <span><i class="fas fa-phone"></i> <?php echo htmlspecialchars($appointment['parent_phone']); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ($appointment['notes']): ?>
                                    <div class="mt-2">
                                        <strong>ملاحظات:</strong>
                                        <p class="text-muted mb-0"><?php echo htmlspecialchars($appointment['notes']); ?></p>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="col-lg-4 text-end">
                                    <div class="btn-group-vertical gap-1">
                                        <a href="appointment-details.php?id=<?php echo $appointment['id']; ?>" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i> التفاصيل
                                        </a>
                                        
                                        <?php if ($appointment['status'] == 'scheduled'): ?>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="appointment_id" value="<?php echo $appointment['id']; ?>">
                                            <input type="hidden" name="action" value="confirm">
                                            <button type="submit" class="btn btn-success btn-sm">
                                                <i class="fas fa-check"></i> تأكيد
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                        
                                        <?php if (in_array($appointment['status'], ['scheduled', 'confirmed'])): ?>
                                        <a href="reschedule-appointment.php?id=<?php echo $appointment['id']; ?>" class="btn btn-warning btn-sm">
                                            <i class="fas fa-edit"></i> إعادة جدولة
                                        </a>
                                        
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="appointment_id" value="<?php echo $appointment['id']; ?>">
                                            <input type="hidden" name="action" value="complete">
                                            <button type="submit" class="btn btn-info btn-sm">
                                                <i class="fas fa-check-circle"></i> إكمال
                                            </button>
                                        </form>
                                        
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="appointment_id" value="<?php echo $appointment['id']; ?>">
                                            <input type="hidden" name="action" value="cancel">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-times"></i> إلغاء
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                        
                                        <?php if ($appointment['meeting_link']): ?>
                                        <a href="<?php echo htmlspecialchars($appointment['meeting_link']); ?>" target="_blank" class="btn btn-primary btn-sm">
                                            <i class="fas fa-video"></i> الانضمام
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد مواعيد قادمة</h4>
                        <p class="text-muted">احجز موعداً جديداً مع أحد المرضى</p>
                        <a href="schedule-appointment.php" class="btn btn-warning">
                            <i class="fas fa-plus"></i> حجز موعد جديد
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- المواعيد السابقة -->
            <div class="tab-pane fade" id="past">
                <?php if (!empty($past_appointments)): ?>
                    <?php foreach ($past_appointments as $appointment): ?>
                        <div class="appointment-card <?php echo $appointment['status']; ?>">
                            <div class="row align-items-center">
                                <div class="col-lg-10">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h5 class="mb-1">
                                            موعد مع <?php echo htmlspecialchars($appointment['parent_name']); ?>
                                            <?php if ($appointment['student_name']): ?>
                                                - <?php echo htmlspecialchars($appointment['student_name']); ?>
                                            <?php endif; ?>
                                        </h5>
                                        <span class="status-badge status-<?php echo $appointment['status']; ?>">
                                            <?php echo $status_labels[$appointment['status']] ?? $appointment['status']; ?>
                                        </span>
                                    </div>
                                    
                                    <div class="d-flex flex-wrap gap-3 text-muted small">
                                        <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y', strtotime($appointment['appointment_date'])); ?></span>
                                        <span><i class="fas fa-clock"></i> <?php echo date('H:i', strtotime($appointment['appointment_date'])); ?></span>
                                        <span><i class="fas fa-hourglass-half"></i> <?php echo $appointment['duration_minutes']; ?> دقيقة</span>
                                        <span><i class="fas fa-tag"></i> <?php echo $appointment['appointment_type']; ?></span>
                                    </div>
                                </div>
                                
                                <div class="col-lg-2 text-end">
                                    <a href="appointment-details.php?id=<?php echo $appointment['id']; ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-history fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد مواعيد سابقة</h4>
                        <p class="text-muted">ستظهر هنا مواعيدك السابقة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    function exportAppointments() {
        NibrassHelpers.showAlert('سيتم تطوير ميزة التصدير قريباً', 'info');
    }
    
    // تأكيد الإجراءات
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const action = this.querySelector('input[name=\"action\"]').value;
            let message = '';
            
            if (action === 'confirm') {
                message = 'هل أنت متأكد من تأكيد هذا الموعد؟';
            } else if (action === 'complete') {
                message = 'هل أنت متأكد من إكمال هذا الموعد؟';
            } else if (action === 'cancel') {
                message = 'هل أنت متأكد من إلغاء هذا الموعد؟';
            }
            
            if (message && !confirm(message)) {
                e.preventDefault();
            }
        });
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
