<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كولي أمر
require_parent();

$user = current_user();

// جلب الموارد التعليمية المناسبة للأولياء
$articles_query = "SELECT * FROM articles 
                   WHERE category IN ('نصائح للأولياء', 'تطوير المهارات', 'تعليمي', 'إرشادات') 
                   AND is_published = 1 
                   ORDER BY created_at DESC";
$articles_stmt = $db->prepare($articles_query);
$articles_stmt->execute();
$articles = $articles_stmt->fetchAll();

// جلب الوسائط التعليمية
$media_query = "SELECT * FROM media 
                WHERE is_public = 1 
                AND (category LIKE '%تعليمي%' OR category LIKE '%أولياء%' OR category LIKE '%إرشاد%')
                ORDER BY created_at DESC";
$media_stmt = $db->prepare($media_query);
$media_stmt->execute();
$media_items = $media_stmt->fetchAll();

// تصنيف الوسائط
$videos = array_filter($media_items, function($item) {
    return in_array($item['file_type'], ['mp4', 'avi', 'mov', 'video']);
});

$audios = array_filter($media_items, function($item) {
    return in_array($item['file_type'], ['mp3', 'wav', 'audio']);
});

$documents = array_filter($media_items, function($item) {
    return in_array($item['file_type'], ['pdf', 'doc', 'docx', 'document']);
});

// جلب الورشات المتاحة
$workshops_query = "SELECT * FROM workshops
                    WHERE is_active = 1
                    ORDER BY created_at DESC";
$workshops_stmt = $db->prepare($workshops_query);
$workshops_stmt->execute();
$workshops = $workshops_stmt->fetchAll();

// إعداد متغيرات الصفحة
$page_title = 'الموارد التعليمية';
$page_description = 'مجموعة شاملة من الموارد التعليمية والإرشادية للأولياء';

// تضمين الهيدر
include '../includes/header.php';
?>

<style>
.resources-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.resources-header h1,
.resources-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.resource-category {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.resource-category::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6f42c1, #20c997);
}

.resource-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.category-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.category-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-left: 1rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.category-icon.articles { background: linear-gradient(135deg, #6f42c1, #e83e8c); }
.category-icon.videos { background: linear-gradient(135deg, #dc3545, #fd7e14); }
.category-icon.audios { background: linear-gradient(135deg, #28a745, #20c997); }
.category-icon.documents { background: linear-gradient(135deg, #17a2b8, #6f42c1); }
.category-icon.workshops { background: linear-gradient(135deg, #ffc107, #fd7e14); }

.category-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.category-subtitle {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.resource-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.resource-card:hover {
    background: white;
    border-color: #6f42c1;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(111, 66, 193, 0.15);
}

.resource-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #6f42c1, #20c997);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.resource-card:hover::before {
    transform: scaleY(1);
}

.resource-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.resource-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    margin-left: 1rem;
}

.resource-title {
    font-weight: 600;
    color: #333;
    margin: 0;
    font-size: 1rem;
    line-height: 1.4;
}

.resource-description {
    color: #666;
    font-size: 0.9rem;
    margin: 0.5rem 0 1rem 0;
    line-height: 1.5;
}

.resource-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #999;
    margin-bottom: 1rem;
}

.resource-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-resource {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary-resource {
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    color: white;
}

.btn-primary-resource:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(111, 66, 193, 0.3);
    color: white;
}

.btn-outline-resource {
    background: transparent;
    color: #6f42c1;
    border: 2px solid #6f42c1;
}

.btn-outline-resource:hover {
    background: #6f42c1;
    color: white;
}

.search-section {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

.search-input {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: #6f42c1;
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
}

.filter-tabs {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.filter-tab {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background: #f8f9fa;
    color: #666;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.filter-tab.active,
.filter-tab:hover {
    background: #6f42c1;
    color: white;
    border-color: #6f42c1;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
}

.empty-state i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@media (max-width: 768px) {
    .resources-grid {
        grid-template-columns: 1fr;
    }
    
    .category-header {
        flex-direction: column;
        text-align: center;
    }
    
    .category-icon {
        margin-left: 0;
        margin-bottom: 1rem;
    }
    
    .resource-header {
        flex-direction: column;
        text-align: center;
    }
    
    .resource-icon {
        margin-left: 0;
        margin-bottom: 0.5rem;
    }
    
    .filter-tabs {
        justify-content: center;
    }
}
</style>

<!-- قسم العنوان -->
<section class="resources-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-book-open"></i> الموارد التعليمية</h1>
            <p class="lead">مجموعة شاملة من الموارد التعليمية والإرشادية للأولياء</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-newspaper"></i> <?php echo count($articles); ?> مقال
                </span>
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-video"></i> <?php echo count($videos); ?> فيديو
                </span>
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-headphones"></i> <?php echo count($audios); ?> صوتي
                </span>
                <span class="badge bg-light text-dark fs-6">
                    <i class="fas fa-file-pdf"></i> <?php echo count($documents); ?> مستند
                </span>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <!-- قسم البحث والفلترة -->
        <div class="search-section">
            <div class="row">
                <div class="col-lg-8">
                    <input type="text" class="form-control search-input" id="searchInput" 
                           placeholder="ابحث في الموارد التعليمية...">
                </div>
                <div class="col-lg-4">
                    <button class="btn btn-primary w-100" onclick="searchResources()">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
            <div class="filter-tabs">
                <a href="#" class="filter-tab active" data-filter="all">الكل</a>
                <a href="#" class="filter-tab" data-filter="articles">المقالات</a>
                <a href="#" class="filter-tab" data-filter="videos">الفيديوهات</a>
                <a href="#" class="filter-tab" data-filter="audios">الصوتيات</a>
                <a href="#" class="filter-tab" data-filter="documents">المستندات</a>
                <a href="#" class="filter-tab" data-filter="workshops">الورشات</a>
            </div>
        </div>

        <!-- المقالات التعليمية -->
        <div class="resource-category" data-category="articles">
            <div class="category-header">
                <div class="category-icon articles">
                    <i class="fas fa-newspaper"></i>
                </div>
                <div>
                    <h3 class="category-title">المقالات التعليمية</h3>
                    <p class="category-subtitle">مقالات متخصصة ونصائح للأولياء</p>
                </div>
            </div>

            <?php if (!empty($articles)): ?>
                <div class="resources-grid">
                    <?php foreach (array_slice($articles, 0, 6) as $article): ?>
                        <div class="resource-card">
                            <div class="resource-header">
                                <div class="resource-icon category-icon articles">
                                    <i class="fas fa-newspaper"></i>
                                </div>
                                <h4 class="resource-title"><?php echo htmlspecialchars($article['title']); ?></h4>
                            </div>
                            <p class="resource-description">
                                <?php echo htmlspecialchars(substr($article['content'], 0, 120)) . '...'; ?>
                            </p>
                            <div class="resource-meta">
                                <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y', strtotime($article['created_at'])); ?></span>
                                <span><i class="fas fa-eye"></i> <?php echo $article['views'] ?? 0; ?> مشاهدة</span>
                            </div>
                            <div class="resource-actions">
                                <a href="../article.php?id=<?php echo $article['id']; ?>" class="btn-resource btn-primary-resource">
                                    <i class="fas fa-book-open"></i> قراءة
                                </a>
                                <button class="btn-resource btn-outline-resource" onclick="shareResource('article', <?php echo $article['id']; ?>)">
                                    <i class="fas fa-share"></i> مشاركة
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-newspaper"></i>
                    <h4>لا توجد مقالات متاحة</h4>
                    <p>سيتم إضافة مقالات تعليمية قريباً</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- الفيديوهات التعليمية -->
        <div class="resource-category" data-category="videos">
            <div class="category-header">
                <div class="category-icon videos">
                    <i class="fas fa-video"></i>
                </div>
                <div>
                    <h3 class="category-title">الفيديوهات التعليمية</h3>
                    <p class="category-subtitle">محتوى مرئي تفاعلي ومفيد</p>
                </div>
            </div>

            <?php if (!empty($videos)): ?>
                <div class="resources-grid">
                    <?php foreach (array_slice($videos, 0, 6) as $video): ?>
                        <div class="resource-card">
                            <div class="resource-header">
                                <div class="resource-icon category-icon videos">
                                    <i class="fas fa-play"></i>
                                </div>
                                <h4 class="resource-title"><?php echo htmlspecialchars($video['title']); ?></h4>
                            </div>
                            <p class="resource-description">
                                <?php echo htmlspecialchars($video['description'] ?? 'فيديو تعليمي مفيد'); ?>
                            </p>
                            <div class="resource-meta">
                                <span><i class="fas fa-clock"></i> <?php echo $video['duration'] ?? '5'; ?> دقائق</span>
                                <span><i class="fas fa-eye"></i> <?php echo $video['views'] ?? 0; ?> مشاهدة</span>
                            </div>
                            <div class="resource-actions">
                                <button class="btn-resource btn-primary-resource" onclick="playVideo('<?php echo $video['file_path']; ?>')">
                                    <i class="fas fa-play"></i> تشغيل
                                </button>
                                <button class="btn-resource btn-outline-resource" onclick="shareResource('video', <?php echo $video['id']; ?>)">
                                    <i class="fas fa-share"></i> مشاركة
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-video"></i>
                    <h4>لا توجد فيديوهات متاحة</h4>
                    <p>سيتم إضافة محتوى مرئي قريباً</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- الورشات التدريبية -->
        <?php if (!empty($workshops)): ?>
        <div class="resource-category" data-category="workshops">
            <div class="category-header">
                <div class="category-icon workshops">
                    <i class="fas fa-chalkboard-teacher"></i>
                </div>
                <div>
                    <h3 class="category-title">الورشات التدريبية</h3>
                    <p class="category-subtitle">ورشات تفاعلية للأولياء</p>
                </div>
            </div>

            <div class="resources-grid">
                <?php foreach (array_slice($workshops, 0, 4) as $workshop): ?>
                    <div class="resource-card">
                        <div class="resource-header">
                            <div class="resource-icon category-icon workshops">
                                <i class="fas fa-users"></i>
                            </div>
                            <h4 class="resource-title"><?php echo htmlspecialchars($workshop['title']); ?></h4>
                        </div>
                        <p class="resource-description">
                            <?php echo htmlspecialchars(substr($workshop['description'], 0, 120)) . '...'; ?>
                        </p>
                        <div class="resource-meta">
                            <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y', strtotime($workshop['created_at'])); ?></span>
                            <span><i class="fas fa-users"></i> <?php echo $workshop['max_participants'] ?? 20; ?> مشارك</span>
                        </div>
                        <div class="resource-actions">
                            <a href="../workshops.php?id=<?php echo $workshop['id']; ?>" class="btn-resource btn-primary-resource">
                                <i class="fas fa-sign-in-alt"></i> التسجيل
                            </a>
                            <button class="btn-resource btn-outline-resource" onclick="shareResource('workshop', <?php echo $workshop['id']; ?>)">
                                <i class="fas fa-share"></i> مشاركة
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    function searchResources() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const cards = document.querySelectorAll('.resource-card');
        
        cards.forEach(card => {
            const title = card.querySelector('.resource-title').textContent.toLowerCase();
            const description = card.querySelector('.resource-description').textContent.toLowerCase();
            
            if (title.includes(searchTerm) || description.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
    
    function playVideo(videoPath) {
        NibrassHelpers.showAlert('سيتم تشغيل الفيديو: ' + videoPath, 'info');
    }
    
    function shareResource(type, id) {
        NibrassHelpers.showAlert('سيتم مشاركة ' + type + ' رقم ' + id, 'info');
    }
    
    // فلترة الموارد
    document.querySelectorAll('.filter-tab').forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            const filter = this.dataset.filter;
            const categories = document.querySelectorAll('.resource-category');
            
            categories.forEach(category => {
                if (filter === 'all' || category.dataset.category === filter) {
                    category.style.display = 'block';
                } else {
                    category.style.display = 'none';
                }
            });
        });
    });
    
    // البحث عند الكتابة
    document.getElementById('searchInput').addEventListener('input', searchResources);
";

// تضمين الفوتر
include '../includes/footer.php';
?>
