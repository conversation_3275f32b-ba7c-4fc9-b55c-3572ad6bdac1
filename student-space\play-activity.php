<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كطالب
require_student();

$user = current_user();

// جلب معلومات الطالب
try {
    $student_info_query = "SELECT * FROM students WHERE user_id = :user_id";
    $student_info_stmt = $db->prepare($student_info_query);
    $student_info_stmt->bindParam(':user_id', $user['id']);
    $student_info_stmt->execute();
    $student_info = $student_info_stmt->fetch();
} catch (PDOException $e) {
    $student_info = null;
}

// جلب معرف النشاط من URL
$activity_id = isset($_GET['id']) ? (int)$_GET['id'] : 1;

// تحديد النشاط بناءً على المعرف
$activities = [
    1 => [
        'name' => 'لعبة الرياضيات',
        'description' => 'تعلم الجمع والطرح بطريقة ممتعة',
        'icon' => 'fas fa-calculator',
        'color' => 'math',
        'url' => 'games/math-game.php'
    ],
    2 => [
        'name' => 'لعبة اللغة',
        'description' => 'تعلم الحروف والكلمات',
        'icon' => 'fas fa-language',
        'color' => 'language',
        'url' => 'games/language-game.php'
    ],
    3 => [
        'name' => 'الفيديوهات التعليمية',
        'description' => 'تعلم العلوم من خلال الفيديوهات الممتعة',
        'icon' => 'fas fa-video',
        'color' => 'science',
        'url' => 'videos.php'
    ],
    4 => [
        'name' => 'الرسم والتلوين',
        'description' => 'أطلق إبداعك في الرسم والتلوين',
        'icon' => 'fas fa-paint-brush',
        'color' => 'art',
        'url' => 'drawing.php'
    ]
];

$current_activity = $activities[$activity_id] ?? $activities[1];

include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
/* Floating shapes animation for header */
.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.shape {
    position: absolute;
    color: rgba(255,255,255,0.1);
    animation: float 6s ease-in-out infinite;
}

.shape:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.shape:nth-child(4) {
    top: 40%;
    right: 30%;
    animation-delay: 6s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.activity-launcher {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    text-align: center;
    margin: 2rem 0;
}

.activity-icon-huge {
    width: 150px;
    height: 150px;
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: white;
    margin: 0 auto 2rem auto;
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.activity-icon-huge:hover {
    transform: scale(1.05) rotate(5deg);
}

.activity-icon-huge.math { background: linear-gradient(135deg, #ff6b6b, #ee5a24); }
.activity-icon-huge.language { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
.activity-icon-huge.science { background: linear-gradient(135deg, #a8edea, #fed6e3); color: #333; }
.activity-icon-huge.art { background: linear-gradient(135deg, #ffecd2, #fcb69f); color: #333; }

.play-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: white;
    padding: 1rem 3rem;
    font-size: 1.2rem;
    border-radius: 50px;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.play-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    color: white;
}

.back-button {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    border: none;
    color: white;
    padding: 0.8rem 2rem;
    font-size: 1rem;
    border-radius: 25px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.back-button:hover {
    transform: translateY(-2px);
    color: white;
}
</style>

<!-- قسم العنوان -->
<section class="nibrass-header position-relative">
    <div class="floating-shapes">
        <div class="shape"><i class="<?php echo $current_activity['icon']; ?> fa-3x"></i></div>
        <div class="shape"><i class="fas fa-star fa-3x"></i></div>
        <div class="shape"><i class="fas fa-trophy fa-3x"></i></div>
        <div class="shape"><i class="fas fa-heart fa-3x"></i></div>
    </div>
    <div class="container">
        <div class="hero-content text-center">
            <h1><i class="<?php echo $current_activity['icon']; ?>"></i> <?php echo $current_activity['name']; ?></h1>
            <p class="lead"><?php echo $current_activity['description']; ?></p>
            <div class="mt-4">
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-user"></i> <?php echo $user['name']; ?>
                </span>
                <span class="badge bg-light text-dark fs-6">
                    <i class="fas fa-play"></i> جاهز للعب
                </span>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="activity-launcher">
                <div class="activity-icon-huge <?php echo $current_activity['color']; ?>">
                    <i class="<?php echo $current_activity['icon']; ?>"></i>
                </div>
                
                <h2 class="mb-3"><?php echo $current_activity['name']; ?></h2>
                <p class="text-muted mb-4"><?php echo $current_activity['description']; ?></p>
                
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="<?php echo $current_activity['url']; ?>" class="play-button">
                        <i class="fas fa-play me-2"></i>
                        <?php
                        if ($activity_id == 3) {
                            echo 'شاهد الفيديوهات';
                        } elseif ($activity_id == 4) {
                            echo 'ابدأ الرسم';
                        } else {
                            echo 'ابدأ اللعب';
                        }
                        ?>
                    </a>
                    <a href="activities.php" class="back-button">
                        <i class="fas fa-arrow-right me-2"></i>العودة للأنشطة
                    </a>
                </div>
                
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        <?php
                        if ($activity_id == 3) {
                            echo 'اضغط على "شاهد الفيديوهات" للانتقال إلى قسم الفيديوهات التعليمية';
                        } elseif ($activity_id == 4) {
                            echo 'اضغط على "ابدأ الرسم" للانتقال إلى استوديو الرسم والتلوين';
                        } else {
                            echo 'اضغط على "ابدأ اللعب" للانتقال إلى النشاط';
                        }
                        ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
