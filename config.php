<?php
/**
 * ملف الإعدادات العامة لمنصة نبراس
 * Nibrass Platform Configuration File
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'nibrass_platform');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الموقع
define('SITE_NAME', 'منصة نبراس');
define('SITE_URL', 'http://localhost/Nibrass');
define('SITE_DESCRIPTION', 'منصة تعليمية متخصصة في دعم الأطفال ذوي اضطراب طيف التوحد');

// إعدادات الأمان
define('SECRET_KEY', 'nibrass_secret_key_2024');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة

// إعدادات الملفات
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 10485760); // 10MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'mp3', 'mp4', 'doc', 'docx']);

// إعدادات OpenAI
define('OPENAI_API_KEY', '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');
define('OPENAI_MODEL', 'gpt-3.5-turbo');

// إعدادات البريد الإلكتروني (اختيارية)
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USER', '');
define('SMTP_PASS', '');

// إعدادات المنطقة الزمنية
date_default_timezone_set('Africa/Tunis');

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// دالة للحصول على الرابط الأساسي
function base_url($path = '') {
    return SITE_URL . '/' . ltrim($path, '/');
}

// دالة للتحقق من تسجيل الدخول
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// دالة للحصول على معلومات المستخدم الحالي
function current_user() {
    if (is_logged_in()) {
        return [
            'id' => $_SESSION['user_id'],
            'name' => $_SESSION['user_name'] ?? '',
            'email' => $_SESSION['user_email'] ?? '',
            'role' => $_SESSION['user_role'] ?? 'user'
        ];
    }
    return null;
}

// دالة لتنظيف البيانات
function clean_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// دالة لإعادة التوجيه
function redirect($url) {
    header("Location: " . base_url($url));
    exit();
}

// دالة لعرض الرسائل
function show_message($message, $type = 'info') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
}

// ===== CSRF PROTECTION FUNCTIONS =====

// دالة لإنشاء رمز CSRF
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// دالة للتحقق من رمز CSRF
function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) &&
           hash_equals($_SESSION['csrf_token'], $token);
}

// دالة لإنشاء حقل CSRF مخفي للنماذج
function csrf_field() {
    $token = generate_csrf_token();
    return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
}

// دالة للتحقق من CSRF في النماذج
function check_csrf() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = $_POST['csrf_token'] ?? '';
        if (!verify_csrf_token($token)) {
            die('خطأ في الأمان: رمز التحقق غير صحيح');
        }
    }
}

// دالة محسنة لمعالجة الأخطاء
function handle_error($message, $type = 'error') {
    error_log("Nibrass Error: " . $message);

    if (ENVIRONMENT === 'development') {
        show_message($message, $type);
    } else {
        show_message('حدث خطأ في النظام. يرجى المحاولة مرة أخرى.', $type);
    }
}

// دالة للتحقق من صحة البريد الإلكتروني
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// دالة للتحقق من صحة الأسماء العربية
function validate_arabic_name($name) {
    // السماح بالحروف العربية والمسافات والنقاط والشرطات
    return preg_match('/^[\p{Arabic}\s\-\.]+$/u', $name);
}

// دالة لتنظيف وتحقق المدخلات العربية
function sanitize_arabic_input($input) {
    $input = trim($input);
    $input = filter_var($input, FILTER_SANITIZE_STRING, FILTER_FLAG_NO_ENCODE_QUOTES);
    return $input;
}

// دالة لاسترجاع وعرض الرسائل
function get_flash_message() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message']);
        unset($_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

// دالة الحصول على الرابط الصحيح
function get_url($path = '') {
    $base_url = SITE_URL;
    if (!empty($path)) {
        $base_url .= '/' . ltrim($path, '/');
    }
    return $base_url;
}

// دالة الحصول على مسار الأصول (CSS, JS, Images)
function get_asset_url($asset_path) {
    return get_url('assets/' . ltrim($asset_path, '/'));
}

// دالة الحصول على مسار الصور
function get_image_url($image_path) {
    return get_asset_url('images/' . ltrim($image_path, '/'));
}

// دالة الحصول على مسار الرفع
function get_upload_url($file_path) {
    return get_url('uploads/' . ltrim($file_path, '/'));
}

// دالة التحقق من المسار النسبي
function get_relative_path($from_file) {
    $current_dir = dirname($_SERVER['PHP_SELF']);
    $base_dir = dirname($_SERVER['SCRIPT_NAME']);

    // حساب عدد المستويات للعودة للجذر
    $levels = substr_count(str_replace($base_dir, '', $current_dir), '/');

    return str_repeat('../', $levels);
}

// دالة الحصول على رابط لوحة التحكم حسب دور المستخدم
function get_dashboard_url() {
    if (!is_logged_in()) {
        return get_url('index.php');
    }

    $user = current_user();
    switch ($user['role']) {
        case 'student':
            return get_url('student-space/dashboard.php');
        case 'parent':
            return get_url('parent-space/dashboard.php');
        case 'specialist':
            return get_url('specialist-space/dashboard.php');
        case 'admin':
            return get_url('admin/dashboard.php');
        default:
            return get_url('index.php');
    }
}
?>
