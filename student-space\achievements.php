<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كطالب
require_student();

$user = current_user();

// جلب معلومات الطالب
try {
    $student_info_query = "SELECT * FROM students WHERE user_id = :user_id";
    $student_info_stmt = $db->prepare($student_info_query);
    $student_info_stmt->bindParam(':user_id', $user['id']);
    $student_info_stmt->execute();
    $student_info = $student_info_stmt->fetch();
} catch (PDOException $e) {
    $student_info = null;
}

// جلب جميع الإنجازات
try {
    // التحقق من وجود جدول achievements أولاً
    $check_table = $db->query("SHOW TABLES LIKE 'achievements'");
    if ($check_table->rowCount() == 0) {
        throw new Exception("جدول الإنجازات غير موجود");
    }

    $student_id = $student_info['id'] ?? 0;
    $achievements_query = "SELECT sa.*, a.title, a.description, a.icon, a.points_required, a.badge_color
                          FROM student_achievements sa
                          LEFT JOIN achievements a ON sa.achievement_id = a.id
                          WHERE sa.student_id = :student_id
                          ORDER BY sa.earned_at DESC";
    $achievements_stmt = $db->prepare($achievements_query);
    $achievements_stmt->bindParam(':student_id', $student_id);
    $achievements_stmt->execute();
    $earned_achievements = $achievements_stmt->fetchAll();
} catch (Exception $e) {
    $earned_achievements = [];
}

// جلب الإنجازات المتاحة (غير المحققة)
try {
    $student_id = $student_info['id'] ?? 0;
    $available_query = "SELECT a.* FROM achievements a
                       WHERE a.id NOT IN (
                           SELECT achievement_id FROM student_achievements
                           WHERE student_id = :student_id
                       )
                       ORDER BY a.points_required ASC";
    $available_stmt = $db->prepare($available_query);
    $available_stmt->bindParam(':student_id', $student_id);
    $available_stmt->execute();
    $available_achievements = $available_stmt->fetchAll();
} catch (PDOException $e) {
    $available_achievements = [];
}

// حساب إجمالي النقاط
$total_points = 0;
foreach ($earned_achievements as $achievement) {
    $total_points += $achievement['points_earned'] ?? 0;
}

// إعداد متغيرات الصفحة
$page_title = 'إنجازاتي';
$page_description = 'عرض جميع الإنجازات والجوائز';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
.achievements-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.achievements-header h1,
.achievements-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.achievement-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.achievement-card.earned {
    border-color: #ffd700;
    background: linear-gradient(135deg, #fff9e6, #ffffff);
}

.achievement-card.earned::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ffd700, #ffb347);
}

.achievement-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.achievement-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 1rem;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.achievement-icon.gold { background: linear-gradient(135deg, #ffd700, #ffb347); }
.achievement-icon.silver { background: linear-gradient(135deg, #c0c0c0, #a8a8a8); }
.achievement-icon.bronze { background: linear-gradient(135deg, #cd7f32, #b8860b); }
.achievement-icon.blue { background: linear-gradient(135deg, #4a90e2, #2c5aa0); }
.achievement-icon.green { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
.achievement-icon.purple { background: linear-gradient(135deg, #9b59b6, #8e44ad); }

.achievement-locked {
    opacity: 0.6;
    filter: grayscale(50%);
}

.achievement-locked .achievement-icon {
    background: linear-gradient(135deg, #bdc3c7, #95a5a6) !important;
}

.progress-ring {
    width: 60px;
    height: 60px;
    position: relative;
}

.progress-ring svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.progress-ring circle {
    fill: none;
    stroke-width: 4;
    r: 26;
    cx: 30;
    cy: 30;
}

.progress-ring .background {
    stroke: #e9ecef;
}

.progress-ring .progress {
    stroke: #ffd700;
    stroke-dasharray: 163.36;
    stroke-dashoffset: 163.36;
    transition: stroke-dashoffset 0.6s ease;
}

.stats-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 25px;
    padding: 2rem;
    color: white;
    text-align: center;
    margin-bottom: 2rem;
}

/* تأكيد ظهور الأيقونات */
.achievements-header i,
.achievement-card i,
.stats-section i,
.btn i {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}
</style>

<!-- قسم العنوان -->
<section class="nibrass-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-trophy"></i> إنجازاتي</h1>
            <p class="lead">اكتشف جميع الجوائز والإنجازات التي حققتها</p>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <!-- إحصائيات الإنجازات -->
        <div class="stats-section">
            <div class="row text-center">
                <div class="col-md-3">
                    <i class="fas fa-trophy fa-2x mb-2"></i>
                    <h3><?php echo count($earned_achievements); ?></h3>
                    <p>إنجازات محققة</p>
                </div>
                <div class="col-md-3">
                    <i class="fas fa-star fa-2x mb-2"></i>
                    <h3><?php echo $total_points; ?></h3>
                    <p>نقاط الإنجازات</p>
                </div>
                <div class="col-md-3">
                    <i class="fas fa-target fa-2x mb-2"></i>
                    <h3><?php echo count($available_achievements); ?></h3>
                    <p>إنجازات متاحة</p>
                </div>
                <div class="col-md-3">
                    <i class="fas fa-percentage fa-2x mb-2"></i>
                    <h3><?php echo count($earned_achievements) > 0 ? round((count($earned_achievements) / (count($earned_achievements) + count($available_achievements))) * 100) : 0; ?>%</h3>
                    <p>نسبة الإكمال</p>
                </div>
            </div>
        </div>

        <!-- الإنجازات المحققة -->
        <?php if (!empty($earned_achievements)): ?>
        <div class="mb-4">
            <h2><i class="fas fa-medal text-warning"></i> الإنجازات المحققة</h2>
            <div class="row">
                <?php foreach ($earned_achievements as $achievement): ?>
                    <div class="col-lg-6">
                        <div class="achievement-card earned">
                            <div class="d-flex align-items-center">
                                <div class="achievement-icon <?php echo $achievement['badge_color'] ?? 'gold'; ?>">
                                    <i class="fas fa-<?php echo $achievement['icon'] ?? 'trophy'; ?>"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-1"><?php echo htmlspecialchars($achievement['title']); ?></h5>
                                    <p class="text-muted mb-2"><?php echo htmlspecialchars($achievement['description']); ?></p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-warning">
                                            <i class="fas fa-star"></i> +<?php echo $achievement['points_earned']; ?> نقطة
                                        </span>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar"></i> 
                                            <?php echo date('d/m/Y', strtotime($achievement['earned_date'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- الإنجازات المتاحة -->
        <?php if (!empty($available_achievements)): ?>
        <div class="mb-4">
            <h2><i class="fas fa-lock text-muted"></i> إنجازات يمكن تحقيقها</h2>
            <div class="row">
                <?php foreach ($available_achievements as $achievement): ?>
                    <div class="col-lg-6">
                        <div class="achievement-card achievement-locked">
                            <div class="d-flex align-items-center">
                                <div class="achievement-icon">
                                    <i class="fas fa-<?php echo $achievement['icon'] ?? 'trophy'; ?>"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-1"><?php echo htmlspecialchars($achievement['title']); ?></h5>
                                    <p class="text-muted mb-2"><?php echo htmlspecialchars($achievement['description']); ?></p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-star"></i> +<?php echo $achievement['points_required']; ?> نقطة
                                        </span>
                                        <small class="text-muted">
                                            <i class="fas fa-lock"></i> غير محقق
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if (empty($earned_achievements) && empty($available_achievements)): ?>
        <div class="text-center py-5">
            <i class="fas fa-trophy fa-4x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد إنجازات متاحة</h4>
            <p class="text-muted">ابدأ بحل الأنشطة لتحصل على إنجازاتك الأولى!</p>
            <a href="activities.php" class="nibrass-btn nibrass-btn-primary">
                <i class="fas fa-gamepad"></i> ابدأ الأنشطة
            </a>
        </div>
        <?php endif; ?>

        <!-- العودة للوحة التحكم -->
        <div class="text-center mt-4">
            <a href="dashboard.php" class="nibrass-btn nibrass-btn-outline-primary">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    // تأثيرات التمرير للكروت
    document.addEventListener('DOMContentLoaded', function() {
        const achievementCards = document.querySelectorAll('.achievement-card');
        achievementCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                if (!this.classList.contains('achievement-locked')) {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                }
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
