<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كمدير
require_admin();

$user = current_user();

// معالجة إضافة إشعار جديد
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_notification'])) {
        $title = trim($_POST['title']);
        $message = trim($_POST['message']);
        $type = $_POST['type'];
        $target_audience = $_POST['target_audience'];
        $priority = $_POST['priority'];
        $start_date = $_POST['start_date'];
        $end_date = !empty($_POST['end_date']) ? $_POST['end_date'] : null;
        
        if (empty($title) || empty($message)) {
            $error_message = 'العنوان والرسالة مطلوبان';
        } else {
            try {
                $stmt = $db->prepare("INSERT INTO notifications (title, message, type, target_audience, priority, start_date, end_date, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$title, $message, $type, $target_audience, $priority, $start_date, $end_date, $user['id']]);
                $success_message = 'تم إضافة الإشعار بنجاح';
            } catch (PDOException $e) {
                $error_message = 'خطأ في إضافة الإشعار: ' . $e->getMessage();
            }
        }
    } elseif (isset($_POST['toggle_status'])) {
        $notification_id = (int)$_POST['notification_id'];
        $new_status = (int)$_POST['new_status'];
        
        try {
            $stmt = $db->prepare("UPDATE notifications SET is_active = ? WHERE id = ?");
            $stmt->execute([$new_status, $notification_id]);
            $success_message = 'تم تحديث حالة الإشعار بنجاح';
        } catch (PDOException $e) {
            $error_message = 'خطأ في تحديث الإشعار: ' . $e->getMessage();
        }
    } elseif (isset($_POST['delete_notification'])) {
        $notification_id = (int)$_POST['notification_id'];
        
        try {
            // حذف قراءات المستخدمين أولاً
            $stmt = $db->prepare("DELETE FROM user_notifications WHERE notification_id = ?");
            $stmt->execute([$notification_id]);
            
            // حذف الإشعار
            $stmt = $db->prepare("DELETE FROM notifications WHERE id = ?");
            $stmt->execute([$notification_id]);
            
            $success_message = 'تم حذف الإشعار بنجاح';
        } catch (PDOException $e) {
            $error_message = 'خطأ في حذف الإشعار: ' . $e->getMessage();
        }
    }
}

// جلب الإشعارات مع إحصائيات القراءة
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$type_filter = isset($_GET['type']) ? $_GET['type'] : '';
$audience_filter = isset($_GET['audience']) ? $_GET['audience'] : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = 10;
$offset = ($page - 1) * $per_page;

// بناء الاستعلام
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(title LIKE ? OR message LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($type_filter)) {
    $where_conditions[] = "type = ?";
    $params[] = $type_filter;
}

if (!empty($audience_filter)) {
    $where_conditions[] = "target_audience = ?";
    $params[] = $audience_filter;
}

if ($status_filter !== '') {
    $where_conditions[] = "is_active = ?";
    $params[] = (int)$status_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    // عدد الإشعارات الإجمالي
    $count_query = "SELECT COUNT(*) FROM notifications $where_clause";
    $count_stmt = $db->prepare($count_query);
    $count_stmt->execute($params);
    $total_notifications = $count_stmt->fetchColumn();
    
    // جلب الإشعارات مع إحصائيات القراءة
    $query = "SELECT n.*, 
                     u.name as created_by_name,
                     COUNT(un.id) as total_reads,
                     COUNT(CASE WHEN un.is_read = 1 THEN 1 END) as read_count
              FROM notifications n
              LEFT JOIN users u ON n.created_by = u.id
              LEFT JOIN user_notifications un ON n.id = un.notification_id
              $where_clause
              GROUP BY n.id
              ORDER BY n.created_at DESC
              LIMIT $per_page OFFSET $offset";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $notifications = $stmt->fetchAll();
    
    $total_pages = ceil($total_notifications / $per_page);
    
} catch (PDOException $e) {
    $error_message = 'خطأ في جلب الإشعارات: ' . $e->getMessage();
    $notifications = [];
    $total_notifications = 0;
    $total_pages = 0;
}

// إعداد متغيرات الصفحة
$page_title = 'إدارة الإشعارات';
$page_description = 'إدارة إشعارات المنصة والتواصل مع المستخدمين';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
/* Admin Dashboard Modern Styles with Enhanced Arabic RTL Support */
body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: #f8f9fa !important;
    min-height: 100vh;
    margin: 0;
    padding-top: 70px;
    direction: rtl;
    text-align: right;
}

.admin-container {
    min-height: 100vh;
    padding: 2rem 0;
    background: #f8f9fa;
}

.admin-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Section - Unified Admin Header */
.admin-header {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 5rem;
}

.admin-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #333;
}

.admin-header p {
    opacity: 0.8;
    margin: 0;
    color: #333;
}

/* Form and Filter Sections */
.add-notification-section,
.filter-section,
.notifications-table-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    display: block;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    width: 100%;
}

.form-control:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
    outline: none;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f, #a8e6cf);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
    background: transparent;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Notification Cards */
.notification-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-right: 5px solid #4a90e2;
    transition: all 0.3s ease;
}

.notification-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.notification-card.type-success {
    border-right-color: #28a745;
}

.notification-card.type-warning {
    border-right-color: #ffc107;
}

.notification-card.type-error {
    border-right-color: #dc3545;
}

.notification-card.type-announcement {
    border-right-color: #6f42c1;
}

.notification-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.notification-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #333;
    margin: 0;
    flex: 1;
}

.notification-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.notification-message {
    color: #555;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.badge-info { background: #17a2b8; color: white; }
.badge-success { background: #28a745; color: white; }
.badge-warning { background: #ffc107; color: #212529; }
.badge-danger { background: #dc3545; color: white; }
.badge-primary { background: #6f42c1; color: white; }

.status-active { color: #28a745; }
.status-inactive { color: #dc3545; }

/* Responsive Design */
@media (max-width: 768px) {
    .admin-content {
        padding: 0 0.5rem;
    }
    
    .admin-header,
    .add-notification-section,
    .filter-section,
    .notifications-table-container {
        padding: 1rem;
    }
    
    .notification-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .notification-actions {
        justify-content: center;
    }
}
</style>

<?php include 'includes/admin-nav.php'; ?>

<div class="admin-container">
    <div class="admin-content container mx-auto">
        <!-- رأس الصفحة -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-bell"></i> إدارة الإشعارات</h1>
                    <p>إدارة إشعارات المنصة والتواصل مع المستخدمين</p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- إضافة إشعار جديد -->
        <div class="add-notification-section">
            <h3><i class="fas fa-plus-circle"></i> إضافة إشعار جديد</h3>
            <form method="POST" class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">العنوان</label>
                        <input type="text" name="title" class="form-control" required>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">نوع الإشعار</label>
                        <select name="type" class="form-control" required>
                            <option value="info">معلومات</option>
                            <option value="success">نجاح</option>
                            <option value="warning">تحذير</option>
                            <option value="error">خطأ</option>
                            <option value="announcement">إعلان</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">الجمهور المستهدف</label>
                        <select name="target_audience" class="form-control" required>
                            <option value="all">الجميع</option>
                            <option value="parents">أولياء الأمور</option>
                            <option value="students">الطلاب</option>
                            <option value="specialists">الأخصائيين</option>
                            <option value="admins">المديرين</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">الأولوية</label>
                        <select name="priority" class="form-control" required>
                            <option value="low">منخفضة</option>
                            <option value="medium" selected>متوسطة</option>
                            <option value="high">عالية</option>
                            <option value="urgent">عاجلة</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">تاريخ البداية</label>
                        <input type="datetime-local" name="start_date" class="form-control"
                               value="<?php echo date('Y-m-d\TH:i'); ?>" required>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">تاريخ النهاية (اختياري)</label>
                        <input type="datetime-local" name="end_date" class="form-control">
                    </div>
                </div>

                <div class="col-12">
                    <div class="form-group">
                        <label class="form-label">الرسالة</label>
                        <textarea name="message" class="form-control" rows="4" required></textarea>
                    </div>
                </div>

                <div class="col-12">
                    <button type="submit" name="add_notification" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة الإشعار
                    </button>
                </div>
            </form>
        </div>

        <!-- فلترة الإشعارات -->
        <div class="filter-section">
            <h3><i class="fas fa-filter"></i> فلترة الإشعارات</h3>
            <form method="GET" class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label">البحث</label>
                        <input type="text" name="search" class="form-control"
                               value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="البحث في العنوان أو الرسالة">
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">النوع</label>
                        <select name="type" class="form-control">
                            <option value="">جميع الأنواع</option>
                            <option value="info" <?php echo $type_filter === 'info' ? 'selected' : ''; ?>>معلومات</option>
                            <option value="success" <?php echo $type_filter === 'success' ? 'selected' : ''; ?>>نجاح</option>
                            <option value="warning" <?php echo $type_filter === 'warning' ? 'selected' : ''; ?>>تحذير</option>
                            <option value="error" <?php echo $type_filter === 'error' ? 'selected' : ''; ?>>خطأ</option>
                            <option value="announcement" <?php echo $type_filter === 'announcement' ? 'selected' : ''; ?>>إعلان</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">الجمهور</label>
                        <select name="audience" class="form-control">
                            <option value="">جميع الجماهير</option>
                            <option value="all" <?php echo $audience_filter === 'all' ? 'selected' : ''; ?>>الجميع</option>
                            <option value="parents" <?php echo $audience_filter === 'parents' ? 'selected' : ''; ?>>أولياء الأمور</option>
                            <option value="students" <?php echo $audience_filter === 'students' ? 'selected' : ''; ?>>الطلاب</option>
                            <option value="specialists" <?php echo $audience_filter === 'specialists' ? 'selected' : ''; ?>>الأخصائيين</option>
                            <option value="admins" <?php echo $audience_filter === 'admins' ? 'selected' : ''; ?>>المديرين</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-control">
                            <option value="">جميع الحالات</option>
                            <option value="1" <?php echo $status_filter === '1' ? 'selected' : ''; ?>>نشط</option>
                            <option value="0" <?php echo $status_filter === '0' ? 'selected' : ''; ?>>غير نشط</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="notifications.php" class="btn btn-outline-primary">
                                <i class="fas fa-refresh"></i> إعادة تعيين
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- قائمة الإشعارات -->
        <div class="notifications-table-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3><i class="fas fa-list"></i> قائمة الإشعارات</h3>
                <div class="text-muted">
                    إجمالي النتائج: <?php echo number_format($total_notifications); ?>
                </div>
            </div>

            <?php if (!empty($notifications)): ?>
                <?php foreach ($notifications as $notification): ?>
                    <div class="notification-card type-<?php echo $notification['type']; ?>">
                        <div class="notification-header">
                            <h4 class="notification-title">
                                <i class="fas fa-<?php
                                    echo $notification['type'] === 'info' ? 'info-circle' :
                                        ($notification['type'] === 'success' ? 'check-circle' :
                                        ($notification['type'] === 'warning' ? 'exclamation-triangle' :
                                        ($notification['type'] === 'error' ? 'times-circle' : 'bullhorn')));
                                ?>"></i>
                                <?php echo htmlspecialchars($notification['title']); ?>
                            </h4>
                            <div class="notification-actions">
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                    <input type="hidden" name="new_status" value="<?php echo $notification['is_active'] ? 0 : 1; ?>">
                                    <button type="submit" name="toggle_status"
                                            class="btn btn-sm <?php echo $notification['is_active'] ? 'btn-warning' : 'btn-success'; ?>">
                                        <i class="fas fa-<?php echo $notification['is_active'] ? 'pause' : 'play'; ?>"></i>
                                        <?php echo $notification['is_active'] ? 'إيقاف' : 'تفعيل'; ?>
                                    </button>
                                </form>

                                <form method="POST" style="display: inline;"
                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')">
                                    <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                    <button type="submit" name="delete_notification" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                </form>
                            </div>
                        </div>

                        <div class="notification-meta">
                            <div class="meta-item">
                                <i class="fas fa-tag"></i>
                                <span class="badge badge-<?php echo $notification['type']; ?>">
                                    <?php
                                    $type_labels = [
                                        'info' => 'معلومات',
                                        'success' => 'نجاح',
                                        'warning' => 'تحذير',
                                        'error' => 'خطأ',
                                        'announcement' => 'إعلان'
                                    ];
                                    echo $type_labels[$notification['type']] ?? $notification['type'];
                                    ?>
                                </span>
                            </div>

                            <div class="meta-item">
                                <i class="fas fa-users"></i>
                                <span><?php
                                    $audience_labels = [
                                        'all' => 'الجميع',
                                        'parents' => 'أولياء الأمور',
                                        'students' => 'الطلاب',
                                        'specialists' => 'الأخصائيين',
                                        'admins' => 'المديرين'
                                    ];
                                    echo $audience_labels[$notification['target_audience']] ?? $notification['target_audience'];
                                ?></span>
                            </div>

                            <div class="meta-item">
                                <i class="fas fa-exclamation"></i>
                                <span><?php
                                    $priority_labels = [
                                        'low' => 'منخفضة',
                                        'medium' => 'متوسطة',
                                        'high' => 'عالية',
                                        'urgent' => 'عاجلة'
                                    ];
                                    echo $priority_labels[$notification['priority']] ?? $notification['priority'];
                                ?></span>
                            </div>

                            <div class="meta-item">
                                <i class="fas fa-<?php echo $notification['is_active'] ? 'check' : 'times'; ?>"></i>
                                <span class="<?php echo $notification['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo $notification['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </div>

                            <div class="meta-item">
                                <i class="fas fa-eye"></i>
                                <span><?php echo number_format($notification['read_count']); ?> قراءة</span>
                            </div>

                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <span><?php echo date('Y-m-d H:i', strtotime($notification['start_date'])); ?></span>
                            </div>

                            <?php if ($notification['created_by_name']): ?>
                                <div class="meta-item">
                                    <i class="fas fa-user"></i>
                                    <span><?php echo htmlspecialchars($notification['created_by_name']); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="notification-message">
                            <?php echo nl2br(htmlspecialchars($notification['message'])); ?>
                        </div>

                        <?php if ($notification['end_date']): ?>
                            <div class="meta-item">
                                <i class="fas fa-calendar-times"></i>
                                <span>ينتهي في: <?php echo date('Y-m-d H:i', strtotime($notification['end_date'])); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>

                <!-- التنقل بين الصفحات -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination-container mt-4">
                        <nav aria-label="تنقل الصفحات">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&type=<?php echo urlencode($type_filter); ?>&audience=<?php echo urlencode($audience_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                            <i class="fas fa-chevron-right"></i> السابق
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&type=<?php echo urlencode($type_filter); ?>&audience=<?php echo urlencode($audience_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&type=<?php echo urlencode($type_filter); ?>&audience=<?php echo urlencode($audience_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                            التالي <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد إشعارات</h4>
                    <p class="text-muted">لم يتم العثور على إشعارات تطابق معايير البحث</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
