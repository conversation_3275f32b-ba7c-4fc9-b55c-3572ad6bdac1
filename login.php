<?php
require_once 'config.php';
require_once 'db.php';

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = clean_input($_POST['email']);
    $password = $_POST['password'];
    
    if (empty($email) || empty($password)) {
        $error_message = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    } else {
        try {
            $query = "SELECT * FROM users WHERE email = :email AND is_active = 1";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':email', $email);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch();
                
                if (password_verify($password, $user['password'])) {
                    // تسجيل الدخول بنجاح
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_name'] = $user['name'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_role'] = $user['role'];
                    
                    // إعادة التوجيه حسب نوع المستخدم
                    switch ($user['role']) {
                        case 'admin':
                            redirect('admin/dashboard.php');
                            break;
                        case 'parent':
                            redirect('parent-space/dashboard.php');
                            break;
                        case 'specialist':
                            redirect('specialist-space/dashboard.php');
                            break;
                        case 'student':
                            redirect('student-space/dashboard.php');
                            break;
                        default:
                            redirect('index.php');
                    }
                } else {
                    $error_message = 'كلمة المرور غير صحيحة';
                }
            } else {
                $error_message = 'البريد الإلكتروني غير مسجل';
            }
        } catch (PDOException $e) {
            $error_message = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
        }
    }
}
?>

<?php
// إعداد متغيرات الصفحة
$page_title = 'تسجيل الدخول';
$page_description = 'أدخل بياناتك للوصول إلى حسابك في منصة نبراس';

// تضمين الهيدر
include 'includes/header.php';
?>

<style>
.login-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0, #1e3a8a, #3b82f6);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
}

.login-header h1,
.login-header p,
.login-header .lead {
    color: white !important;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    pointer-events: none;
}

.shape {
    position: absolute;
    color: rgba(255,255,255,0.1);
    animation: float 6s ease-in-out infinite;
}

.shape:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.shape:nth-child(4) {
    top: 40%;
    right: 30%;
    animation-delay: 6s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.login-form-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-top: 2rem;
    position: relative;
    z-index: 10;
}

.login-form-body {
    padding: 3rem 2rem;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--nibrass-blue);
    margin-bottom: 0.5rem;
}

.btn-login {
    background: linear-gradient(135deg, var(--primary-color), var(--nibrass-green));
    border: none;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
}

.password-toggle {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
}

.password-field {
    position: relative;
}

.login-links {
    text-align: center;
    margin-top: 1.5rem;
}

.login-links a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.login-links a:hover {
    color: var(--nibrass-blue);
}

/* Test Data Section Styles */
.test-data-section {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    border: 2px solid #e9ecef;
}

.test-data-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.test-data-header h5 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.test-accounts {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.test-account-card {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.test-account-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.account-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.account-header i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.account-header span {
    font-weight: 600;
    color: #333;
}

.account-credentials {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.credential-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.credential-item label {
    font-size: 0.85rem;
    font-weight: 600;
    color: #666;
    margin-bottom: 0;
}

.credential-value {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.credential-value input {
    flex: 1;
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
}

.copy-btn {
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.copy-success {
    background: #28a745 !important;
    color: white !important;
    border-color: #28a745 !important;
}

/* Responsive Design */
@media (min-width: 768px) {
    .test-accounts {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .credential-item {
        flex-direction: row;
        align-items: center;
        gap: 1rem;
    }

    .credential-item label {
        min-width: 120px;
        text-align: right;
    }
}
</style>

<!-- قسم العنوان المحسن -->
<section class="login-header position-relative">
    <div class="floating-shapes">
        <div class="shape"><i class="fas fa-sign-in-alt fa-3x"></i></div>
        <div class="shape"><i class="fas fa-user-circle fa-3x"></i></div>
        <div class="shape"><i class="fas fa-lock fa-3x"></i></div>
        <div class="shape"><i class="fas fa-key fa-3x"></i></div>
    </div>
    <div class="container">
        <div class="hero-content text-center">
            <h1><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h1>
            <p class="lead">أدخل بياناتك للوصول إلى حسابك في منصة نبراس</p>
            <div class="mt-4">
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-shield-alt"></i> آمن
                </span>
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-clock"></i> سريع
                </span>
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-mobile-alt"></i> متجاوب
                </span>
                <span class="badge bg-light text-dark fs-6">
                    <i class="fas fa-user-friends"></i> مجتمع
                </span>
            </div>
        </div>
    </div>
</section>

    <!-- نموذج تسجيل الدخول المحسن -->
    <section class="section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="login-form-container">
                        <div class="login-form-body">

                            <?php if (!empty($error_message)): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <?php echo $error_message; ?>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($success_message)): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    <?php echo $success_message; ?>
                                </div>
                            <?php endif; ?>

                            <form method="POST" action="">
                                <div class="form-group mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope"></i> البريد الإلكتروني
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                           required>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock"></i> كلمة المرور
                                    </label>
                                    <div class="password-field">
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <button type="button" class="password-toggle" onclick="togglePassword()">
                                            <i class="fas fa-eye" id="toggleIcon"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                        <label class="form-check-label" for="remember">
                                            تذكرني
                                        </label>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-login w-100 mb-3">
                                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                                </button>
                            </form>

                            <div class="login-links">
                                <p class="mb-2">
                                    <a href="forgot-password.php" class="text-decoration-none">
                                        <i class="fas fa-key"></i> نسيت كلمة المرور؟
                                    </a>
                                </p>
                                <p>
                                    ليس لديك حساب؟
                                    <a href="<?php echo get_url('register.php'); ?>" class="text-decoration-none fw-bold">
                                        <i class="fas fa-user-plus"></i> إنشاء حساب جديد
                                    </a>
                                </p>
                            </div>

                            <!-- بيانات تجريبية للاختبار: تم حذف هذا القسم بناءً على طلب الإدارة -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    function togglePassword() {
        const passwordField = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    function copyToClipboard(elementId) {
        const element = document.getElementById(elementId);
        const button = element.nextElementSibling;

        // نسخ النص
        element.select();
        element.setSelectionRange(0, 99999); // للهواتف المحمولة

        try {
            document.execCommand('copy');

            // تغيير شكل الزر لإظهار النجاح
            const originalIcon = button.innerHTML;
            button.innerHTML = '<i class=\"fas fa-check\"></i>';
            button.classList.add('copy-success');

            // إظهار رسالة نجاح
            NibrassHelpers.showAlert('تم نسخ البيانات بنجاح!', 'success');

            // إعادة الزر لحالته الأصلية بعد ثانيتين
            setTimeout(() => {
                button.innerHTML = originalIcon;
                button.classList.remove('copy-success');
            }, 2000);

        } catch (err) {
            NibrassHelpers.showAlert('فشل في نسخ البيانات. يرجى النسخ يدوياً.', 'error');
        }

        // إلغاء التحديد
        element.blur();
    }

    // دالة لملء النموذج تلقائياً
    function fillLoginForm(email, password) {
        document.getElementById('email').value = email;
        document.getElementById('password').value = password;
        NibrassHelpers.showAlert('تم ملء النموذج بالبيانات التجريبية!', 'info');
    }
";

// تضمين الفوتر
include 'includes/footer.php';
?>
