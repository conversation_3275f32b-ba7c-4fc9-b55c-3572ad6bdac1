<?php
/**
 * إنشاء جدول الفيديوهات التعليمية
 * Create Educational Videos Table
 */

require_once 'config.php';
require_once 'db.php';

echo "إنشاء جدول الفيديوهات التعليمية...\n";

try {
    // إنشاء جدول educational_videos
    $sql = "CREATE TABLE IF NOT EXISTS educational_videos (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        thumbnail VARCHAR(500),
        video_url VARCHAR(500),
        duration VARCHAR(20),
        category ENUM('لغة عربية', 'رياضيات', 'علوم', 'فنون', 'دينية', 'عامة') DEFAULT 'عامة',
        difficulty_level ENUM('easy', 'medium', 'hard') DEFAULT 'easy',
        target_age INT DEFAULT 6,
        views_count INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_category (category),
        INDEX idx_target_age (target_age),
        INDEX idx_is_active (is_active)
    )";
    
    $db->exec($sql);
    echo "✅ تم إنشاء جدول educational_videos بنجاح\n";

    // إدراج فيديوهات تجريبية
    $videos = [
        [
            'title' => 'تعلم الحروف العربية',
            'description' => 'فيديو تعليمي ممتع لتعلم الحروف العربية بالصوت والصورة. يساعد الأطفال على تعلم نطق الحروف وكتابتها بطريقة تفاعلية.',
            'thumbnail' => 'images/videos/arabic-letters.jpg',
            'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'duration' => '15:30',
            'category' => 'لغة عربية',
            'difficulty_level' => 'easy',
            'target_age' => 5,
            'views_count' => 1250
        ],
        [
            'title' => 'الأرقام والعد من 1 إلى 10',
            'description' => 'تعلم الأرقام من 1 إلى 10 بطريقة تفاعلية ومسلية مع الأغاني والألعاب التعليمية.',
            'thumbnail' => 'images/videos/numbers.jpg',
            'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'duration' => '12:45',
            'category' => 'رياضيات',
            'difficulty_level' => 'easy',
            'target_age' => 4,
            'views_count' => 980
        ],
        [
            'title' => 'الألوان والأشكال الهندسية',
            'description' => 'اكتشف عالم الألوان والأشكال الهندسية الأساسية مع شخصيات كرتونية محببة للأطفال.',
            'thumbnail' => 'images/videos/colors-shapes.jpg',
            'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'duration' => '18:20',
            'category' => 'فنون',
            'difficulty_level' => 'easy',
            'target_age' => 3,
            'views_count' => 1500
        ],
        [
            'title' => 'عالم الحيوانات وأصواتها',
            'description' => 'رحلة ممتعة لاكتشاف الحيوانات المختلفة وأصواتها وبيئاتها الطبيعية.',
            'thumbnail' => 'images/videos/animals.jpg',
            'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'duration' => '20:15',
            'category' => 'علوم',
            'difficulty_level' => 'medium',
            'target_age' => 6,
            'views_count' => 2100
        ],
        [
            'title' => 'قصص الأنبياء للأطفال',
            'description' => 'قصص تعليمية هادفة من سير الأنبياء عليهم السلام مقدمة بأسلوب مناسب للأطفال.',
            'thumbnail' => 'images/videos/prophets.jpg',
            'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'duration' => '25:00',
            'category' => 'دينية',
            'difficulty_level' => 'medium',
            'target_age' => 8,
            'views_count' => 1800
        ],
        [
            'title' => 'تعلم الجمع والطرح',
            'description' => 'تعلم عمليات الجمع والطرح البسيطة باستخدام الألعاب والأمثلة التفاعلية.',
            'thumbnail' => 'images/videos/math-operations.jpg',
            'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'duration' => '22:10',
            'category' => 'رياضيات',
            'difficulty_level' => 'medium',
            'target_age' => 7,
            'views_count' => 1650
        ],
        [
            'title' => 'الفصول الأربعة',
            'description' => 'تعرف على الفصول الأربعة وخصائص كل فصل والتغيرات التي تحدث في الطبيعة.',
            'thumbnail' => 'images/videos/seasons.jpg',
            'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'duration' => '16:40',
            'category' => 'علوم',
            'difficulty_level' => 'easy',
            'target_age' => 5,
            'views_count' => 1320
        ],
        [
            'title' => 'الرسم والتلوين للمبتدئين',
            'description' => 'تعلم أساسيات الرسم والتلوين مع تقنيات بسيطة ومناسبة للأطفال.',
            'thumbnail' => 'images/videos/drawing.jpg',
            'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'duration' => '19:30',
            'category' => 'فنون',
            'difficulty_level' => 'easy',
            'target_age' => 6,
            'views_count' => 1100
        ],
        [
            'title' => 'آداب الطعام والشراب',
            'description' => 'تعلم آداب الطعام والشراب في الإسلام بطريقة تفاعلية ومناسبة للأطفال.',
            'thumbnail' => 'images/videos/eating-manners.jpg',
            'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'duration' => '14:20',
            'category' => 'دينية',
            'difficulty_level' => 'easy',
            'target_age' => 5,
            'views_count' => 950
        ],
        [
            'title' => 'تعلم كتابة الحروف',
            'description' => 'تعلم كتابة الحروف العربية خطوة بخطوة مع التدريب على الخط الصحيح.',
            'thumbnail' => 'images/videos/writing.jpg',
            'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            'duration' => '28:15',
            'category' => 'لغة عربية',
            'difficulty_level' => 'medium',
            'target_age' => 7,
            'views_count' => 1750
        ]
    ];

    $stmt = $db->prepare("INSERT IGNORE INTO educational_videos (title, description, thumbnail, video_url, duration, category, difficulty_level, target_age, views_count) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $inserted_count = 0;
    foreach ($videos as $video) {
        $result = $stmt->execute([
            $video['title'],
            $video['description'],
            $video['thumbnail'],
            $video['video_url'],
            $video['duration'],
            $video['category'],
            $video['difficulty_level'],
            $video['target_age'],
            $video['views_count']
        ]);
        if ($result) {
            $inserted_count++;
        }
    }
    
    echo "✅ تم إدراج $inserted_count فيديو تجريبي\n";

    // التحقق من النتيجة
    $count_result = $db->query("SELECT COUNT(*) FROM educational_videos");
    $total_count = $count_result->fetchColumn();
    echo "✅ إجمالي الفيديوهات في الجدول: $total_count\n";

    // عرض إحصائيات حسب الفئة
    echo "\n📊 إحصائيات الفيديوهات حسب الفئة:\n";
    $stats_result = $db->query("SELECT category, COUNT(*) as count FROM educational_videos GROUP BY category ORDER BY count DESC");
    $stats = $stats_result->fetchAll();
    
    foreach ($stats as $stat) {
        echo "- {$stat['category']}: {$stat['count']} فيديو\n";
    }

    echo "\n🎉 تم إنشاء جدول الفيديوهات التعليمية بنجاح!\n";

} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🚀 جدول الفيديوهات جاهز للاستخدام!\n";
?>
