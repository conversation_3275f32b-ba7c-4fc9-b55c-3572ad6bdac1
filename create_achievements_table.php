<?php
/**
 * إنشاء جدول الإنجازات والجوائز
 * Create Achievements and Rewards Table
 */

require_once 'config.php';
require_once 'db.php';

echo "إنشاء جدول الإنجازات والجوائز...\n";

try {
    // إنشاء جدول achievements
    $sql = "CREATE TABLE IF NOT EXISTS achievements (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        icon VARCHAR(100) DEFAULT 'fas fa-trophy',
        badge_image VARCHAR(500),
        category ENUM('تعليمي', 'إبداعي', 'اجتماعي', 'رياضي', 'تقني', 'أخلاقي', 'عام') DEFAULT 'عام',
        type ENUM('نقاط', 'مهمة', 'وقت', 'تكرار', 'مستوى') DEFAULT 'نقاط',
        difficulty_level ENUM('easy', 'medium', 'hard', 'expert') DEFAULT 'easy',
        points_required INT DEFAULT 0,
        tasks_required INT DEFAULT 1,
        time_required INT DEFAULT 0,
        repetitions_required INT DEFAULT 1,
        level_required INT DEFAULT 1,
        reward_points INT DEFAULT 10,
        reward_badge VARCHAR(255),
        reward_certificate BOOLEAN DEFAULT FALSE,
        reward_gift VARCHAR(255),
        target_age_min INT DEFAULT 3,
        target_age_max INT DEFAULT 18,
        prerequisites TEXT,
        unlock_conditions TEXT,
        is_hidden BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        display_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_category (category),
        INDEX idx_type (type),
        INDEX idx_difficulty_level (difficulty_level),
        INDEX idx_is_active (is_active),
        INDEX idx_display_order (display_order)
    )";
    
    $db->exec($sql);
    echo "✅ تم إنشاء جدول achievements بنجاح\n";

    // إنشاء جدول student_achievements لربط الطلاب بالإنجازات
    $sql2 = "CREATE TABLE IF NOT EXISTS student_achievements (
        id INT PRIMARY KEY AUTO_INCREMENT,
        student_id INT NOT NULL,
        achievement_id INT NOT NULL,
        earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        progress_percentage DECIMAL(5,2) DEFAULT 0.00,
        current_points INT DEFAULT 0,
        current_tasks INT DEFAULT 0,
        current_time_spent INT DEFAULT 0,
        current_repetitions INT DEFAULT 0,
        is_completed BOOLEAN DEFAULT FALSE,
        completion_date TIMESTAMP NULL,
        notes TEXT,
        UNIQUE KEY unique_student_achievement (student_id, achievement_id),
        INDEX idx_student_id (student_id),
        INDEX idx_achievement_id (achievement_id),
        INDEX idx_is_completed (is_completed),
        INDEX idx_earned_at (earned_at)
    )";
    
    $db->exec($sql2);
    echo "✅ تم إنشاء جدول student_achievements بنجاح\n";

    // إدراج إنجازات تجريبية
    $achievements = [
        [
            'title' => 'أول خطوة',
            'description' => 'مبروك! لقد سجلت دخولك لأول مرة إلى منصة نبراس',
            'icon' => 'fas fa-baby',
            'category' => 'عام',
            'type' => 'مهمة',
            'difficulty_level' => 'easy',
            'tasks_required' => 1,
            'reward_points' => 10,
            'reward_badge' => 'شارة البداية',
            'target_age_min' => 3,
            'target_age_max' => 18,
            'unlock_conditions' => 'تسجيل الدخول لأول مرة',
            'display_order' => 1
        ],
        [
            'title' => 'عاشق الرياضيات',
            'description' => 'أكمل 10 أنشطة رياضية بنجاح',
            'icon' => 'fas fa-calculator',
            'category' => 'تعليمي',
            'type' => 'تكرار',
            'difficulty_level' => 'easy',
            'repetitions_required' => 10,
            'reward_points' => 50,
            'reward_badge' => 'شارة الرياضيات',
            'target_age_min' => 5,
            'target_age_max' => 12,
            'unlock_conditions' => 'إكمال 10 أنشطة رياضية',
            'display_order' => 2
        ],
        [
            'title' => 'قارئ نهم',
            'description' => 'اقرأ 5 قصص تفاعلية كاملة',
            'icon' => 'fas fa-book-reader',
            'category' => 'تعليمي',
            'type' => 'تكرار',
            'difficulty_level' => 'easy',
            'repetitions_required' => 5,
            'reward_points' => 30,
            'reward_badge' => 'شارة القراءة',
            'reward_certificate' => 1,
            'target_age_min' => 4,
            'target_age_max' => 15,
            'unlock_conditions' => 'قراءة 5 قصص كاملة',
            'display_order' => 3
        ],
        [
            'title' => 'فنان مبدع',
            'description' => 'أنشئ 3 رسومات في استوديو الرسم',
            'icon' => 'fas fa-palette',
            'category' => 'إبداعي',
            'type' => 'تكرار',
            'difficulty_level' => 'easy',
            'repetitions_required' => 3,
            'reward_points' => 25,
            'reward_badge' => 'شارة الفن',
            'target_age_min' => 3,
            'target_age_max' => 16,
            'unlock_conditions' => 'إنشاء 3 رسومات',
            'display_order' => 4
        ],
        [
            'title' => 'جامع النقاط',
            'description' => 'اجمع 100 نقطة من الأنشطة المختلفة',
            'icon' => 'fas fa-coins',
            'category' => 'عام',
            'type' => 'نقاط',
            'difficulty_level' => 'medium',
            'points_required' => 100,
            'reward_points' => 20,
            'reward_badge' => 'شارة النقاط',
            'target_age_min' => 5,
            'target_age_max' => 18,
            'unlock_conditions' => 'جمع 100 نقطة',
            'display_order' => 5
        ],
        [
            'title' => 'عالم صغير',
            'description' => 'شاهد 10 فيديوهات علمية تعليمية',
            'icon' => 'fas fa-microscope',
            'category' => 'تعليمي',
            'type' => 'تكرار',
            'difficulty_level' => 'medium',
            'repetitions_required' => 10,
            'reward_points' => 40,
            'reward_badge' => 'شارة العلوم',
            'reward_certificate' => 1,
            'target_age_min' => 6,
            'target_age_max' => 16,
            'unlock_conditions' => 'مشاهدة 10 فيديوهات علمية',
            'display_order' => 6
        ],
        [
            'title' => 'صديق مساعد',
            'description' => 'ساعد زميلاً في حل مشكلة أو نشاط',
            'icon' => 'fas fa-hands-helping',
            'category' => 'اجتماعي',
            'type' => 'مهمة',
            'difficulty_level' => 'medium',
            'tasks_required' => 1,
            'reward_points' => 35,
            'reward_badge' => 'شارة المساعدة',
            'target_age_min' => 5,
            'target_age_max' => 18,
            'unlock_conditions' => 'مساعدة زميل',
            'display_order' => 7
        ],
        [
            'title' => 'مثابر لا يستسلم',
            'description' => 'أكمل نشاطاً صعباً بعد 3 محاولات',
            'icon' => 'fas fa-mountain',
            'category' => 'أخلاقي',
            'type' => 'مهمة',
            'difficulty_level' => 'hard',
            'tasks_required' => 1,
            'reward_points' => 60,
            'reward_badge' => 'شارة المثابرة',
            'reward_certificate' => 1,
            'target_age_min' => 7,
            'target_age_max' => 18,
            'unlock_conditions' => 'إكمال نشاط صعب بعد محاولات متعددة',
            'display_order' => 8
        ],
        [
            'title' => 'نجم الأسبوع',
            'description' => 'كن الأكثر نشاطاً لمدة أسبوع كامل',
            'icon' => 'fas fa-star',
            'category' => 'عام',
            'type' => 'وقت',
            'difficulty_level' => 'hard',
            'time_required' => 7,
            'reward_points' => 100,
            'reward_badge' => 'شارة النجم',
            'reward_certificate' => 1,
            'reward_gift' => 'هدية خاصة',
            'target_age_min' => 5,
            'target_age_max' => 18,
            'unlock_conditions' => 'النشاط اليومي لمدة أسبوع',
            'display_order' => 9
        ],
        [
            'title' => 'خبير التقنية',
            'description' => 'استخدم جميع ميزات المنصة التفاعلية',
            'icon' => 'fas fa-laptop-code',
            'category' => 'تقني',
            'type' => 'مهمة',
            'difficulty_level' => 'expert',
            'tasks_required' => 1,
            'reward_points' => 150,
            'reward_badge' => 'شارة التقنية',
            'reward_certificate' => 1,
            'target_age_min' => 8,
            'target_age_max' => 18,
            'unlock_conditions' => 'استخدام جميع ميزات المنصة',
            'display_order' => 10
        ]
    ];

    $stmt = $db->prepare("INSERT IGNORE INTO achievements (title, description, icon, category, type, difficulty_level, points_required, tasks_required, time_required, repetitions_required, reward_points, reward_badge, reward_certificate, reward_gift, target_age_min, target_age_max, unlock_conditions, display_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $inserted_count = 0;
    foreach ($achievements as $achievement) {
        $result = $stmt->execute([
            $achievement['title'],
            $achievement['description'],
            $achievement['icon'],
            $achievement['category'],
            $achievement['type'],
            $achievement['difficulty_level'],
            $achievement['points_required'] ?? 0,
            $achievement['tasks_required'] ?? 1,
            $achievement['time_required'] ?? 0,
            $achievement['repetitions_required'] ?? 1,
            $achievement['reward_points'],
            $achievement['reward_badge'],
            $achievement['reward_certificate'] ?? 0,
            $achievement['reward_gift'] ?? null,
            $achievement['target_age_min'],
            $achievement['target_age_max'],
            $achievement['unlock_conditions'],
            $achievement['display_order']
        ]);
        if ($result) {
            $inserted_count++;
        }
    }
    
    echo "✅ تم إدراج $inserted_count إنجاز تجريبي\n";

    // التحقق من النتيجة
    $count_result = $db->query("SELECT COUNT(*) FROM achievements");
    $total_count = $count_result->fetchColumn();
    echo "✅ إجمالي الإنجازات في الجدول: $total_count\n";

    // عرض إحصائيات حسب الفئة
    echo "\n📊 إحصائيات الإنجازات حسب الفئة:\n";
    $stats_result = $db->query("SELECT category, COUNT(*) as count FROM achievements GROUP BY category ORDER BY count DESC");
    $stats = $stats_result->fetchAll();
    
    foreach ($stats as $stat) {
        echo "- {$stat['category']}: {$stat['count']} إنجاز\n";
    }

    // إحصائيات حسب مستوى الصعوبة
    echo "\n📈 إحصائيات حسب مستوى الصعوبة:\n";
    $difficulty_stats = $db->query("SELECT difficulty_level, COUNT(*) as count FROM achievements GROUP BY difficulty_level ORDER BY FIELD(difficulty_level, 'easy', 'medium', 'hard', 'expert')");
    $difficulty_data = $difficulty_stats->fetchAll();
    
    foreach ($difficulty_data as $difficulty) {
        $level_names = [
            'easy' => 'سهل',
            'medium' => 'متوسط', 
            'hard' => 'صعب',
            'expert' => 'خبير'
        ];
        $level_name = $level_names[$difficulty['difficulty_level']] ?? $difficulty['difficulty_level'];
        echo "- $level_name: {$difficulty['count']} إنجاز\n";
    }

    // إحصائيات حسب نوع الإنجاز
    echo "\n🎯 إحصائيات حسب نوع الإنجاز:\n";
    $type_stats = $db->query("SELECT type, COUNT(*) as count FROM achievements GROUP BY type ORDER BY count DESC");
    $type_data = $type_stats->fetchAll();
    
    foreach ($type_data as $type) {
        echo "- {$type['type']}: {$type['count']} إنجاز\n";
    }

    echo "\n🎉 تم إنشاء جدول الإنجازات والجوائز بنجاح!\n";

} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🚀 جدول الإنجازات جاهز للاستخدام!\n";
?>
