<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كولي أمر
require_parent();

$user = current_user();
$students = get_parent_students($user['id']);

// معالجة إرسال طلب الاستشارة
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $consultation_type = clean_input($_POST['consultation_type']);
    $urgency_level = clean_input($_POST['urgency_level']);
    $specialist_id = !empty($_POST['specialist_id']) ? (int)$_POST['specialist_id'] : null;
    $student_id = !empty($_POST['student_id']) ? (int)$_POST['student_id'] : null;
    $subject = clean_input($_POST['subject']);
    $description = clean_input($_POST['description']);
    $preferred_date = !empty($_POST['preferred_date']) ? $_POST['preferred_date'] : null;
    $preferred_time = !empty($_POST['preferred_time']) ? $_POST['preferred_time'] : null;
    
    // التحقق من صحة البيانات
    $errors = [];
    
    if (empty($consultation_type)) {
        $errors[] = 'يرجى اختيار نوع الاستشارة';
    }
    
    if (empty($urgency_level)) {
        $errors[] = 'يرجى تحديد مستوى الأولوية';
    }
    
    if (empty($subject)) {
        $errors[] = 'يرجى كتابة موضوع الاستشارة';
    }
    
    if (empty($description)) {
        $errors[] = 'يرجى كتابة تفاصيل الاستشارة';
    }
    
    if (empty($errors)) {
        try {
            // إدراج طلب الاستشارة
            $insert_query = "INSERT INTO consultations (parent_id, student_id, specialist_id, consultation_type, urgency_level, subject, description, preferred_date, preferred_time, status, created_at) 
                           VALUES (:parent_id, :student_id, :specialist_id, :consultation_type, :urgency_level, :subject, :description, :preferred_date, :preferred_time, 'pending', NOW())";
            
            $stmt = $db->prepare($insert_query);
            $stmt->bindParam(':parent_id', $user['id']);
            $stmt->bindParam(':student_id', $student_id);
            $stmt->bindParam(':specialist_id', $specialist_id);
            $stmt->bindParam(':consultation_type', $consultation_type);
            $stmt->bindParam(':urgency_level', $urgency_level);
            $stmt->bindParam(':subject', $subject);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':preferred_date', $preferred_date);
            $stmt->bindParam(':preferred_time', $preferred_time);
            
            if ($stmt->execute()) {
                $consultation_id = $db->lastInsertId();
                
                // معالجة رفع الملفات إن وجدت
                if (!empty($_FILES['attachments']['name'][0])) {
                    $upload_dir = '../uploads/consultations/';
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0777, true);
                    }
                    
                    foreach ($_FILES['attachments']['name'] as $key => $filename) {
                        if (!empty($filename)) {
                            $file_extension = pathinfo($filename, PATHINFO_EXTENSION);
                            $allowed_extensions = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'];
                            
                            if (in_array(strtolower($file_extension), $allowed_extensions)) {
                                $new_filename = 'consultation_' . $consultation_id . '_' . time() . '_' . $key . '.' . $file_extension;
                                $upload_path = $upload_dir . $new_filename;
                                
                                if (move_uploaded_file($_FILES['attachments']['tmp_name'][$key], $upload_path)) {
                                    // حفظ معلومات الملف في قاعدة البيانات
                                    $file_query = "INSERT INTO consultation_attachments (consultation_id, filename, original_name, file_path, uploaded_at) 
                                                  VALUES (:consultation_id, :filename, :original_name, :file_path, NOW())";
                                    $file_stmt = $db->prepare($file_query);
                                    $file_stmt->bindParam(':consultation_id', $consultation_id);
                                    $file_stmt->bindParam(':filename', $new_filename);
                                    $file_stmt->bindParam(':original_name', $filename);
                                    $file_stmt->bindParam(':file_path', $upload_path);
                                    $file_stmt->execute();
                                }
                            }
                        }
                    }
                }
                
                $_SESSION['success_message'] = 'تم إرسال طلب الاستشارة بنجاح. سيتم التواصل معك قريباً.';
                header('Location: dashboard.php');
                exit();
            } else {
                $errors[] = 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.';
            }
        } catch (Exception $e) {
            $errors[] = 'حدث خطأ في النظام. يرجى المحاولة لاحقاً.';
        }
    }
}

// جلب قائمة الأخصائيين
$specialists_query = "SELECT id, name, specialization FROM users WHERE role = 'specialist' AND is_active = 1 ORDER BY name";
$specialists_stmt = $db->prepare($specialists_query);
$specialists_stmt->execute();
$specialists = $specialists_stmt->fetchAll();

// إعداد متغيرات الصفحة
$page_title = 'طلب استشارة جديدة';
$page_description = 'احصل على استشارة متخصصة من أفضل الأخصائيين';

// تضمين الهيدر
include '../includes/header.php';
?>

<style>
.consultation-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.consultation-header h1,
.consultation-header p {
    color: white !important;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.consultation-form {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-top: 2rem;
}

.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 15px;
    border-right: 4px solid var(--primary-color);
}

.form-section h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.consultation-type-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

/* تأكيد ظهور الكروت */
.type-card, .urgency-card {
    position: relative;
    z-index: 1;
}

.type-card h6, .urgency-card h6 {
    font-weight: 600;
    margin: 0.5rem 0;
    color: #333;
}

.type-card p, .urgency-card p {
    margin: 0;
    color: #666;
    font-size: 0.85rem;
}

.type-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.type-card:hover {
    border-color: #4a90e2;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.2);
}

.type-card.selected {
    border-color: #4a90e2 !important;
    background: #e3f2fd !important;
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
}

.type-card i {
    font-size: 2rem;
    color: #4a90e2;
    margin-bottom: 0.5rem;
    display: block;
}

.urgency-levels {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.urgency-card {
    flex: 1;
    min-width: 150px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.urgency-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.urgency-card.urgent {
    border-color: #dc3545;
}

.urgency-card.urgent.selected {
    background: #f8d7da !important;
    border-color: #dc3545 !important;
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
}

.urgency-card.normal {
    border-color: #ffc107;
}

.urgency-card.normal.selected {
    background: #fff3cd !important;
    border-color: #ffc107 !important;
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
}

.urgency-card.routine {
    border-color: #28a745;
}

.urgency-card.routine.selected {
    background: #d4edda !important;
    border-color: #28a745 !important;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: #e3f2fd;
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background: #e3f2fd;
}

.submit-section {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    margin-top: 2rem;
}
</style>

<!-- قسم العنوان -->
<section class="consultation-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-user-md"></i> طلب استشارة جديدة</h1>
            <p class="lead">احصل على استشارة متخصصة من أفضل الأخصائيين في مجال اضطراب طيف التوحد</p>
        </div>
    </div>
</section>

<!-- نموذج طلب الاستشارة -->
<section class="section">
    <div class="container">
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> يرجى تصحيح الأخطاء التالية:</h5>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <form method="POST" enctype="multipart/form-data" class="consultation-form">
            <!-- نوع الاستشارة -->
            <div class="form-section">
                <h4><i class="fas fa-clipboard-list"></i> نوع الاستشارة</h4>
                <div class="consultation-type-cards">
                    <div class="type-card" data-type="behavioral" onclick="
                        document.querySelectorAll('.type-card').forEach(function(c) { c.classList.remove('selected'); });
                        this.classList.add('selected');
                        document.getElementById('consultation_type').value = 'behavioral';
                        console.log('Selected behavioral');
                        return false;
                    ">
                        <i class="fas fa-brain"></i>
                        <h6>سلوكية</h6>
                        <p class="small">مشاكل السلوك والتصرفات</p>
                    </div>
                    <div class="type-card" data-type="educational" onclick="
                        document.querySelectorAll('.type-card').forEach(function(c) { c.classList.remove('selected'); });
                        this.classList.add('selected');
                        document.getElementById('consultation_type').value = 'educational';
                        console.log('Selected educational');
                        return false;
                    ">
                        <i class="fas fa-graduation-cap"></i>
                        <h6>تعليمية</h6>
                        <p class="small">صعوبات التعلم والتطوير</p>
                    </div>
                    <div class="type-card" data-type="medical" onclick="
                        document.querySelectorAll('.type-card').forEach(function(c) { c.classList.remove('selected'); });
                        this.classList.add('selected');
                        document.getElementById('consultation_type').value = 'medical';
                        console.log('Selected medical');
                        return false;
                    ">
                        <i class="fas fa-stethoscope"></i>
                        <h6>طبية</h6>
                        <p class="small">الجوانب الطبية والصحية</p>
                    </div>
                    <div class="type-card" data-type="developmental" onclick="
                        document.querySelectorAll('.type-card').forEach(function(c) { c.classList.remove('selected'); });
                        this.classList.add('selected');
                        document.getElementById('consultation_type').value = 'developmental';
                        console.log('Selected developmental');
                        return false;
                    ">
                        <i class="fas fa-child"></i>
                        <h6>تطويرية</h6>
                        <p class="small">تطوير المهارات والقدرات</p>
                    </div>
                </div>
                <input type="hidden" name="consultation_type" id="consultation_type" required>
            </div>

            <!-- مستوى الأولوية -->
            <div class="form-section">
                <h4><i class="fas fa-exclamation-circle"></i> مستوى الأولوية</h4>
                <div class="urgency-levels">
                    <div class="urgency-card urgent" data-urgency="urgent" onclick="
                        document.querySelectorAll('.urgency-card').forEach(function(c) { c.classList.remove('selected'); });
                        this.classList.add('selected');
                        document.getElementById('urgency_level').value = 'urgent';
                        console.log('Selected urgent');
                        return false;
                    ">
                        <i class="fas fa-exclamation-triangle text-danger"></i>
                        <h6>عاجل</h6>
                        <p class="small">يحتاج تدخل فوري</p>
                    </div>
                    <div class="urgency-card normal" data-urgency="normal" onclick="
                        document.querySelectorAll('.urgency-card').forEach(function(c) { c.classList.remove('selected'); });
                        this.classList.add('selected');
                        document.getElementById('urgency_level').value = 'normal';
                        console.log('Selected normal');
                        return false;
                    ">
                        <i class="fas fa-clock text-warning"></i>
                        <h6>عادي</h6>
                        <p class="small">خلال أسبوع</p>
                    </div>
                    <div class="urgency-card routine" data-urgency="routine" onclick="
                        document.querySelectorAll('.urgency-card').forEach(function(c) { c.classList.remove('selected'); });
                        this.classList.add('selected');
                        document.getElementById('urgency_level').value = 'routine';
                        console.log('Selected routine');
                        return false;
                    ">
                        <i class="fas fa-calendar text-success"></i>
                        <h6>روتيني</h6>
                        <p class="small">وقت مناسب</p>
                    </div>
                </div>
                <input type="hidden" name="urgency_level" id="urgency_level" required>
            </div>

            <!-- اختيار الأخصائي والطفل -->
            <div class="row">
                <div class="col-md-6">
                    <div class="form-section">
                        <h4><i class="fas fa-user-md"></i> اختيار الأخصائي (اختياري)</h4>
                        <select class="form-select" name="specialist_id">
                            <option value="">أي أخصائي متاح</option>
                            <?php foreach ($specialists as $specialist): ?>
                                <option value="<?php echo $specialist['id']; ?>">
                                    د. <?php echo htmlspecialchars($specialist['name']); ?>
                                    <?php if ($specialist['specialization']): ?>
                                        - <?php echo htmlspecialchars($specialist['specialization']); ?>
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-section">
                        <h4><i class="fas fa-child"></i> الطفل المعني</h4>
                        <select class="form-select" name="student_id">
                            <option value="">اختر الطفل</option>
                            <?php foreach ($students as $student): ?>
                                <option value="<?php echo $student['id']; ?>">
                                    <?php echo htmlspecialchars($student['name'] ?? $student['student_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>

            <!-- تفاصيل الاستشارة -->
            <div class="form-section">
                <h4><i class="fas fa-edit"></i> تفاصيل الاستشارة</h4>
                <div class="mb-3">
                    <label for="subject" class="form-label">موضوع الاستشارة</label>
                    <input type="text" class="form-control" id="subject" name="subject" 
                           placeholder="مثال: صعوبة في التواصل الاجتماعي" required>
                </div>
                <div class="mb-3">
                    <label for="description" class="form-label">وصف مفصل للمشكلة أو الاستفسار</label>
                    <textarea class="form-control" id="description" name="description" rows="6" 
                              placeholder="يرجى وصف المشكلة بالتفصيل، متى بدأت، كيف تظهر، وأي معلومات أخرى مهمة..." required></textarea>
                </div>
            </div>

            <!-- الموعد المفضل -->
            <div class="row">
                <div class="col-md-6">
                    <div class="form-section">
                        <h4><i class="fas fa-calendar-alt"></i> التاريخ المفضل</h4>
                        <input type="date" class="form-control" name="preferred_date" 
                               min="<?php echo date('Y-m-d'); ?>">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-section">
                        <h4><i class="fas fa-clock"></i> الوقت المفضل</h4>
                        <select class="form-select" name="preferred_time">
                            <option value="">أي وقت مناسب</option>
                            <option value="morning">صباحاً (9:00 - 12:00)</option>
                            <option value="afternoon">بعد الظهر (12:00 - 17:00)</option>
                            <option value="evening">مساءً (17:00 - 20:00)</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- رفع الملفات -->
            <div class="form-section">
                <h4><i class="fas fa-paperclip"></i> المرفقات (اختياري)</h4>
                <div class="file-upload-area" id="fileUploadArea">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <h5>اسحب الملفات هنا أو انقر للاختيار</h5>
                    <p class="text-muted">يمكنك رفع تقارير طبية، صور، أو أي مستندات مفيدة</p>
                    <p class="small text-muted">الملفات المدعومة: PDF, DOC, DOCX, JPG, PNG (حد أقصى 5 ملفات)</p>
                    <input type="file" class="d-none" id="fileInput" name="attachments[]" multiple 
                           accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                </div>
                <div id="fileList" class="mt-3"></div>
            </div>

            <!-- إرسال الطلب -->
            <div class="submit-section">
                <h4><i class="fas fa-paper-plane"></i> إرسال طلب الاستشارة</h4>
                <p class="text-muted">سيتم مراجعة طلبك والتواصل معك خلال 24 ساعة</p>
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-send"></i> إرسال الطلب
                </button>
                <a href="dashboard.php" class="btn btn-outline-secondary btn-lg ms-3">
                    <i class="fas fa-arrow-right"></i> العودة
                </a>
            </div>
        </form>
    </div>
</section>

<?php
?>

<script>
// دوال بسيطة للاختيار
function selectType(element, type) {
    console.log('Selecting type:', type);

    // إزالة التحديد من جميع الكروت
    document.querySelectorAll('.type-card').forEach(function(card) {
        card.classList.remove('selected');
    });

    // إضافة التحديد للكرت الحالي
    element.classList.add('selected');

    // تحديث قيمة الحقل المخفي
    var input = document.getElementById('consultation_type');
    if (input) {
        input.value = type;
        console.log('Set consultation_type to:', type);
    }
}

function selectUrgency(element, urgency) {
    console.log('Selecting urgency:', urgency);

    // إزالة التحديد من جميع الكروت
    document.querySelectorAll('.urgency-card').forEach(function(card) {
        card.classList.remove('selected');
    });

    // إضافة التحديد للكرت الحالي
    element.classList.add('selected');

    // تحديث قيمة الحقل المخفي
    var input = document.getElementById('urgency_level');
    if (input) {
        input.value = urgency;
        console.log('Set urgency_level to:', urgency);
    }
}

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "

    // معالجة رفع الملفات
    document.addEventListener('DOMContentLoaded', function() {
    
    // رفع الملفات
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileList = document.getElementById('fileList');
    
    fileUploadArea.addEventListener('click', () => fileInput.click());
    
    fileUploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });
    
    fileUploadArea.addEventListener('dragleave', () => {
        fileUploadArea.classList.remove('dragover');
    });
    
    fileUploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
        handleFiles(e.dataTransfer.files);
    });
    
    fileInput.addEventListener('change', (e) => {
        handleFiles(e.target.files);
    });
    
    function handleFiles(files) {
        fileList.innerHTML = '';
        Array.from(files).forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'alert alert-info d-flex justify-content-between align-items-center';
            fileItem.innerHTML =
                '<div>' +
                    '<i class=\"fas fa-file\"></i> ' +
                    '<span>' + file.name + '</span> ' +
                    '<small class=\"text-muted\">(' + (file.size / 1024 / 1024).toFixed(2) + ' MB)</small>' +
                '</div>' +
                '<button type=\"button\" class=\"btn btn-sm btn-outline-danger\" onclick=\"removeFile(' + index + ')\">' +
                    '<i class=\"fas fa-times\"></i>';
                </button>
            `;
            fileList.appendChild(fileItem);
        });
    }
    
    function removeFile(index) {
        const dt = new DataTransfer();
        const files = fileInput.files;
        
        for (let i = 0; i < files.length; i++) {
            if (i !== index) {
                dt.items.add(files[i]);
            }
        }
        
        fileInput.files = dt.files;
        handleFiles(fileInput.files);
    }

    // إضافة validation للنموذج
    const form = document.querySelector('.consultation-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const consultationType = document.getElementById('consultation_type').value;
            const urgencyLevel = document.getElementById('urgency_level').value;

            if (!consultationType) {
                e.preventDefault();
                alert('يرجى اختيار نوع الاستشارة');
                return false;
            }

            if (!urgencyLevel) {
                e.preventDefault();
                alert('يرجى تحديد مستوى الأولوية');
                return false;
            }

            console.log('Form submitted with:', {
                consultationType: consultationType,
                urgencyLevel: urgencyLevel
            });
        });
    }

    }); // إغلاق DOMContentLoaded
";
?>
</script>

<?php
// تضمين الفوتر
include '../includes/footer.php';
?>
