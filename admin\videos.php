<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كمدير
require_admin();

$user = current_user();

// معالجة الإجراءات
$success_message = '';
$error_message = '';

// معالجة إضافة فيديو جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_video'])) {
    check_csrf();
    
    $title = clean_input($_POST['title']);
    $description = clean_input($_POST['description']);
    $video_url = clean_input($_POST['video_url']);
    $thumbnail = clean_input($_POST['thumbnail']);
    $duration = clean_input($_POST['duration']);
    $category = clean_input($_POST['category']);
    $difficulty_level = clean_input($_POST['difficulty_level']);
    $target_age = (int)$_POST['target_age'];
    
    if (empty($title) || empty($description) || empty($video_url)) {
        $error_message = "يرجى ملء جميع الحقول المطلوبة";
    } else {
        try {
            $insert_query = "INSERT INTO educational_videos (title, description, video_url, thumbnail, duration, category, difficulty_level, target_age) 
                            VALUES (:title, :description, :video_url, :thumbnail, :duration, :category, :difficulty_level, :target_age)";
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':title', $title);
            $insert_stmt->bindParam(':description', $description);
            $insert_stmt->bindParam(':video_url', $video_url);
            $insert_stmt->bindParam(':thumbnail', $thumbnail);
            $insert_stmt->bindParam(':duration', $duration);
            $insert_stmt->bindParam(':category', $category);
            $insert_stmt->bindParam(':difficulty_level', $difficulty_level);
            $insert_stmt->bindParam(':target_age', $target_age);
            
            if ($insert_stmt->execute()) {
                $success_message = "تم إضافة الفيديو بنجاح";
            } else {
                $error_message = "فشل في إضافة الفيديو";
            }
        } catch (PDOException $e) {
            // التحقق من وجود الجدول
            if (strpos($e->getMessage(), "doesn't exist") !== false) {
                $error_message = "جدول الفيديوهات غير موجود. يرجى تشغيل إعداد الجداول المفقودة أولاً.";
            } else {
                $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
            }
        }
    }
}

// معالجة تفعيل/إلغاء تفعيل الفيديو
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['toggle_video'])) {
    check_csrf();
    
    $video_id = (int)$_POST['video_id'];
    $current_status = (int)$_POST['current_status'];
    $new_status = $current_status ? 0 : 1;
    
    try {
        $update_query = "UPDATE educational_videos SET is_active = :status WHERE id = :video_id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':status', $new_status);
        $update_stmt->bindParam(':video_id', $video_id);
        
        if ($update_stmt->execute()) {
            $action_text = $new_status ? 'تفعيل' : 'إلغاء تفعيل';
            $success_message = "تم {$action_text} الفيديو بنجاح";
        } else {
            $error_message = "فشل في تحديث حالة الفيديو";
        }
    } catch (PDOException $e) {
        $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
    }
}

// معالجة حذف الفيديو
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_video'])) {
    check_csrf();
    
    $video_id = (int)$_POST['video_id'];
    
    try {
        $delete_query = "DELETE FROM educational_videos WHERE id = :video_id";
        $delete_stmt = $db->prepare($delete_query);
        $delete_stmt->bindParam(':video_id', $video_id);
        
        if ($delete_stmt->execute()) {
            $success_message = "تم حذف الفيديو بنجاح";
        } else {
            $error_message = "فشل في حذف الفيديو";
        }
    } catch (PDOException $e) {
        $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
    }
}

// جلب الفيديوهات مع الفلترة والبحث
$search = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';
$difficulty_filter = $_GET['difficulty'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$limit = 12;
$offset = ($page - 1) * $limit;

try {
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(title LIKE :search OR description LIKE :search)";
        $params[':search'] = "%{$search}%";
    }
    
    if (!empty($category_filter)) {
        $where_conditions[] = "category = :category";
        $params[':category'] = $category_filter;
    }
    
    if (!empty($difficulty_filter)) {
        $where_conditions[] = "difficulty_level = :difficulty";
        $params[':difficulty'] = $difficulty_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // جلب الفيديوهات
    $videos_query = "SELECT * FROM educational_videos {$where_clause} ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
    $videos_stmt = $db->prepare($videos_query);
    
    foreach ($params as $key => $value) {
        $videos_stmt->bindValue($key, $value);
    }
    $videos_stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $videos_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $videos_stmt->execute();
    $videos = $videos_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب العدد الإجمالي
    $count_query = "SELECT COUNT(*) as total FROM educational_videos {$where_clause}";
    $count_stmt = $db->prepare($count_query);
    
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    
    $count_stmt->execute();
    $total_videos = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_videos / $limit);
    
} catch (PDOException $e) {
    // التحقق من وجود الجدول
    try {
        $check_table = $db->query("SHOW TABLES LIKE 'educational_videos'");
        if ($check_table->rowCount() == 0) {
            $error_message = "جدول الفيديوهات غير موجود. يرجى تشغيل إعداد الجداول المفقودة.";
        } else {
            $error_message = "خطأ في جلب الفيديوهات: " . $e->getMessage();
        }
    } catch (Exception $e2) {
        $error_message = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
    }

    $videos = [];
    $total_videos = 0;
    $total_pages = 0;
}

// إعداد متغيرات الصفحة
$page_title = 'إدارة الفيديوهات التعليمية';
$page_description = 'إدارة الفيديوهات التعليمية في منصة نبراس';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<!-- Additional Fonts for Enhanced Arabic Support -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

<style>
/* Enhanced Admin Styling - Consistent with Dashboard */
body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.admin-container {
    min-height: 100vh;
    padding: 2rem 0;
  /* ظذ  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
}

.admin-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Section - Unified Admin Header */
.admin-header {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 5rem;
}

.admin-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #333;
}

.admin-header p {
    opacity: 0.8;
    margin: 0;
    color: #333;
}

/* Form and Filter Sections */
.admin-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.section-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.search-filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1rem;
    align-items: end;
}

/* Video Cards Grid */
.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.video-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.video-thumbnail {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    position: relative;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.7);
    color: white;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.video-content {
    padding: 1.5rem;
}

.video-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.video-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.video-meta {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.85rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
}

.category-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: inline-block;
}

.category-badge.لغة-عربية { background: #e8f5e8; color: #2e7d32; }
.category-badge.رياضيات { background: #e3f2fd; color: #1976d2; }
.category-badge.علوم { background: #fff3e0; color: #f57c00; }
.category-badge.فنون { background: #ffebee; color: #c62828; }
.category-badge.دينية { background: #f3e5f5; color: #7b1fa2; }
.category-badge.عامة { background: #f5f5f5; color: #424242; }

.video-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.status-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #28a745;
}

.status-indicator.inactive {
    background: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .videos-grid {
        grid-template-columns: 1fr;
    }
    
    .search-filters {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .video-actions {
        flex-direction: column;
    }
}
</style>

<?php include 'includes/admin-nav.php'; ?>

<div class="admin-container">
    <div class="admin-content container mx-auto">
        <!-- رأس الصفحة -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-video"></i> إدارة الفيديوهات التعليمية</h1>
                    <p>إدارة الفيديوهات التعليمية في منصة نبراس</p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                <?php if (strpos($error_message, "جدول الفيديوهات غير موجود") !== false): ?>
                    <div class="mt-3">
                        <a href="../create_videos_table.php" class="btn btn-warning">
                            <i class="fas fa-database"></i> إنشاء جدول الفيديوهات
                        </a>
                        <a href="../setup_missing_tables.php" class="btn btn-info">
                            <i class="fas fa-tools"></i> إعداد جميع الجداول المفقودة
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- إضافة فيديو جديد -->
        <div class="admin-section">
            <h3 class="section-title">
                <i class="fas fa-plus-circle"></i> إضافة فيديو تعليمي جديد
            </h3>
            
            <form method="POST">
                <?php echo csrf_field(); ?>
                
                <div class="form-grid">
                    <div>
                        <label class="form-label">عنوان الفيديو *</label>
                        <input type="text" name="title" class="form-control" required>
                    </div>
                    
                    <div>
                        <label class="form-label">رابط الفيديو *</label>
                        <input type="url" name="video_url" class="form-control" placeholder="https://..." required>
                    </div>
                    
                    <div>
                        <label class="form-label">رابط الصورة المصغرة</label>
                        <input type="url" name="thumbnail" class="form-control" placeholder="https://...">
                    </div>
                    
                    <div>
                        <label class="form-label">مدة الفيديو</label>
                        <input type="text" name="duration" class="form-control" placeholder="15:30">
                    </div>
                    
                    <div>
                        <label class="form-label">الفئة *</label>
                        <select name="category" class="form-select" required>
                            <option value="">اختر الفئة</option>
                            <option value="لغة عربية">لغة عربية</option>
                            <option value="رياضيات">رياضيات</option>
                            <option value="علوم">علوم</option>
                            <option value="فنون">فنون</option>
                            <option value="دينية">دينية</option>
                            <option value="عامة">عامة</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="form-label">مستوى الصعوبة</label>
                        <select name="difficulty_level" class="form-select">
                            <option value="easy">سهل</option>
                            <option value="medium">متوسط</option>
                            <option value="hard">صعب</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="form-label">العمر المستهدف</label>
                        <input type="number" name="target_age" class="form-control" value="6" min="3" max="18">
                    </div>
                </div>
                
                <div class="mt-3">
                    <label class="form-label">وصف الفيديو *</label>
                    <textarea name="description" class="form-control" rows="3" required></textarea>
                </div>
                
                <div class="mt-3">
                    <button type="submit" name="add_video" class="nibrass-btn nibrass-btn-success">
                        <i class="fas fa-plus"></i> إضافة الفيديو
                    </button>
                </div>
            </form>
        </div>

        <!-- فلاتر البحث -->
        <div class="admin-section">
            <form method="GET" class="search-filters">
                <div>
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث في العنوان أو الوصف..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div>
                    <label class="form-label">الفئة</label>
                    <select name="category" class="form-select">
                        <option value="">جميع الفئات</option>
                        <option value="لغة عربية" <?php echo $category_filter === 'لغة عربية' ? 'selected' : ''; ?>>لغة عربية</option>
                        <option value="رياضيات" <?php echo $category_filter === 'رياضيات' ? 'selected' : ''; ?>>رياضيات</option>
                        <option value="علوم" <?php echo $category_filter === 'علوم' ? 'selected' : ''; ?>>علوم</option>
                        <option value="فنون" <?php echo $category_filter === 'فنون' ? 'selected' : ''; ?>>فنون</option>
                        <option value="دينية" <?php echo $category_filter === 'دينية' ? 'selected' : ''; ?>>دينية</option>
                        <option value="عامة" <?php echo $category_filter === 'عامة' ? 'selected' : ''; ?>>عامة</option>
                    </select>
                </div>
                
                <div>
                    <label class="form-label">الصعوبة</label>
                    <select name="difficulty" class="form-select">
                        <option value="">جميع المستويات</option>
                        <option value="easy" <?php echo $difficulty_filter === 'easy' ? 'selected' : ''; ?>>سهل</option>
                        <option value="medium" <?php echo $difficulty_filter === 'medium' ? 'selected' : ''; ?>>متوسط</option>
                        <option value="hard" <?php echo $difficulty_filter === 'hard' ? 'selected' : ''; ?>>صعب</option>
                    </select>
                </div>
                
                <div>
                    <button type="submit" class="nibrass-btn nibrass-btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>

        <!-- شبكة الفيديوهات -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3 style="color: black;"><i class="fas fa-list"></i> قائمة الفيديوهات</h3>
            <div style="color: black; opacity: 0.9;">
                إجمالي النتائج: <?php echo number_format($total_videos); ?>
            </div>
        </div>

        <?php if (empty($videos)): ?>
            <div class="admin-section text-center py-5">
                <i class="fas fa-video fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد فيديوهات</h4>
                <p class="text-muted">لم يتم العثور على فيديوهات مطابقة لمعايير البحث</p>
            </div>
        <?php else: ?>
            <div class="videos-grid">
                <?php foreach ($videos as $video): ?>
                    <div class="video-card">
                        <div class="status-indicator <?php echo $video['is_active'] ? 'active' : 'inactive'; ?>"></div>
                        
                        <div class="video-thumbnail">
                            <?php if (!empty($video['thumbnail'])): ?>
                                <img src="<?php echo htmlspecialchars($video['thumbnail']); ?>" alt="<?php echo htmlspecialchars($video['title']); ?>">
                            <?php else: ?>
                                <i class="fas fa-play-circle"></i>
                            <?php endif; ?>
                            <div class="play-overlay">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                        
                        <div class="video-content">
                            <div class="video-title"><?php echo htmlspecialchars($video['title']); ?></div>
                            
                            <span class="category-badge <?php echo str_replace(' ', '-', $video['category']); ?>">
                                <?php echo htmlspecialchars($video['category']); ?>
                            </span>
                            
                            <div class="video-description">
                                <?php echo htmlspecialchars($video['description']); ?>
                            </div>
                            
                            <div class="video-meta">
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span><?php echo $video['duration'] ?: 'غير محدد'; ?></span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-child"></i>
                                    <span>عمر <?php echo $video['target_age']; ?> سنة</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-eye"></i>
                                    <span><?php echo number_format($video['views_count']); ?> مشاهدة</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-signal"></i>
                                    <span><?php 
                                        $difficulties = ['easy' => 'سهل', 'medium' => 'متوسط', 'hard' => 'صعب'];
                                        echo $difficulties[$video['difficulty_level']] ?? $video['difficulty_level'];
                                    ?></span>
                                </div>
                            </div>
                            
                            <div class="video-actions">
                                <!-- تفعيل/إلغاء تفعيل -->
                                <form method="POST" style="display: inline;">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="video_id" value="<?php echo $video['id']; ?>">
                                    <input type="hidden" name="current_status" value="<?php echo $video['is_active']; ?>">
                                    <button type="submit" name="toggle_video" 
                                            class="nibrass-btn <?php echo $video['is_active'] ? 'nibrass-btn-warning' : 'nibrass-btn-success'; ?> btn-sm"
                                            onclick="return confirm('هل أنت متأكد من تغيير حالة هذا الفيديو؟')">
                                        <i class="fas <?php echo $video['is_active'] ? 'fa-pause' : 'fa-play'; ?>"></i>
                                    </button>
                                </form>

                                <!-- حذف -->
                                <form method="POST" style="display: inline;">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="video_id" value="<?php echo $video['id']; ?>">
                                    <button type="submit" name="delete_video" 
                                            class="nibrass-btn nibrass-btn-danger btn-sm"
                                            onclick="return confirm('هل أنت متأكد من حذف هذا الفيديو؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// تضمين الفوتر
include '../includes/footer.php';
?>
