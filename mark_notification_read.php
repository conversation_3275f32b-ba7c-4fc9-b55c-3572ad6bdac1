<?php
/**
 * تحديد الإشعار كمقروء
 * Mark Notification as Read
 */

require_once 'includes/auth.php';
require_once 'db.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مسجل الدخول']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit;
}

// قراءة البيانات
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['notification_id']) || !is_numeric($input['notification_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الإشعار مطلوب']);
    exit;
}

$user = current_user();
$notification_id = (int)$input['notification_id'];

try {
    // التحقق من وجود الإشعار
    $check_query = "SELECT id FROM notifications WHERE id = ? AND is_active = 1";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->execute([$notification_id]);
    
    if (!$check_stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'الإشعار غير موجود']);
        exit;
    }
    
    // إدراج أو تحديث حالة القراءة
    $query = "INSERT INTO user_notifications (user_id, notification_id, is_read, read_at) 
              VALUES (?, ?, 1, NOW()) 
              ON DUPLICATE KEY UPDATE is_read = 1, read_at = NOW()";
    
    $stmt = $db->prepare($query);
    $stmt->execute([$user['id'], $notification_id]);
    
    // إرجاع استجابة نجاح
    echo json_encode([
        'success' => true, 
        'message' => 'تم تحديد الإشعار كمقروء'
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
?>
