<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كأخصائي
require_specialist();

$user = current_user();

// معالجة تحديث حالة الاستشارة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $consultation_id = (int)$_POST['consultation_id'];
    $action = $_POST['action'];
    
    try {
        if ($action == 'accept') {
            $update_query = "UPDATE consultations SET specialist_id = :specialist_id, status = 'assigned', assigned_at = NOW() WHERE id = :id";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->execute([':specialist_id' => $user['id'], ':id' => $consultation_id]);
            $success_message = "تم قبول الاستشارة بنجاح";
        } elseif ($action == 'complete') {
            $update_query = "UPDATE consultations SET status = 'completed', completed_at = NOW() WHERE id = :id AND specialist_id = :specialist_id";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->execute([':id' => $consultation_id, ':specialist_id' => $user['id']]);
            $success_message = "تم إكمال الاستشارة بنجاح";
        }
    } catch (PDOException $e) {
        $error_message = "حدث خطأ في تحديث الاستشارة";
    }
}

// جلب جميع الاستشارات
try {
    $consultations_query = "SELECT c.*, p.name as parent_name, p.phone as parent_phone, 
                                   s.name as student_name, s.age, s.diagnosis_level
                            FROM consultations c
                            LEFT JOIN users p ON c.parent_id = p.id
                            LEFT JOIN students s ON c.student_id = s.id
                            WHERE c.specialist_id = :specialist_id OR c.specialist_id IS NULL
                            ORDER BY 
                                CASE c.status 
                                    WHEN 'pending' THEN 1 
                                    WHEN 'assigned' THEN 2 
                                    WHEN 'in_progress' THEN 3 
                                    WHEN 'completed' THEN 4 
                                END,
                                CASE c.urgency_level 
                                    WHEN 'urgent' THEN 1 
                                    WHEN 'normal' THEN 2 
                                    WHEN 'routine' THEN 3 
                                END,
                                c.created_at DESC";
    $consultations_stmt = $db->prepare($consultations_query);
    $consultations_stmt->bindParam(':specialist_id', $user['id']);
    $consultations_stmt->execute();
    $consultations = $consultations_stmt->fetchAll();
} catch (PDOException $e) {
    $consultations = [];
    $error_message = "حدث خطأ في جلب الاستشارات";
}

// إعداد متغيرات الصفحة
$page_title = 'إدارة الاستشارات';
$page_description = 'إدارة ومتابعة جميع الاستشارات';

// تضمين الهيدر
include '../includes/header.php';
?>

<style>
.consultations-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.consultations-header h1,
.consultations-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

/* تأكيد ظهور الأيقونات */
.consultations-header i,
.consultation-item i,
.btn i,
.alert i {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

.consultation-item {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border-right: 4px solid transparent;
}

.consultation-item:hover {
    transform: translateX(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.consultation-item.urgent { border-right-color: #dc3545; }
.consultation-item.normal { border-right-color: #ffc107; }
.consultation-item.routine { border-right-color: #28a745; }

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-assigned { background: #d1ecf1; color: #0c5460; }
.status-in_progress { background: #cce5ff; color: #004085; }
.status-completed { background: #d4edda; color: #155724; }

.filter-tabs {
    background: white;
    border-radius: 15px;
    padding: 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

.filter-tab {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background: transparent;
    color: #666;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    margin: 0 0.5rem;
}

.filter-tab.active,
.filter-tab:hover {
    background: #4a90e2;
    color: white;
    border-color: #4a90e2;
}


</style>

<!-- قسم العنوان -->
<section class="consultations-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-clipboard-list"></i> إدارة الاستشارات</h1>
            <p class="lead">إدارة ومتابعة جميع طلبات الاستشارة</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-list"></i> <?php echo count($consultations); ?> استشارة
                </span>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- فلترة الاستشارات -->
        <div class="filter-tabs">
            <div class="d-flex flex-wrap justify-content-center">
                <a href="#" class="filter-tab active" data-filter="all">الكل</a>
                <a href="#" class="filter-tab" data-filter="pending">معلقة</a>
                <a href="#" class="filter-tab" data-filter="assigned">مقبولة</a>
                <a href="#" class="filter-tab" data-filter="in_progress">قيد التنفيذ</a>
                <a href="#" class="filter-tab" data-filter="completed">مكتملة</a>
            </div>
        </div>

        <!-- قائمة الاستشارات -->
        <?php if (!empty($consultations)): ?>
            <?php foreach ($consultations as $consultation): ?>
                <div class="consultation-item <?php echo $consultation['urgency_level']; ?>" data-status="<?php echo $consultation['status']; ?>">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="mb-1"><?php echo htmlspecialchars($consultation['subject']); ?></h5>
                                <div>
                                    <span class="status-badge status-<?php echo $consultation['status']; ?>">
                                        <?php 
                                        $status_labels = [
                                            'pending' => 'معلقة',
                                            'assigned' => 'مقبولة',
                                            'in_progress' => 'قيد التنفيذ',
                                            'completed' => 'مكتملة'
                                        ];
                                        echo $status_labels[$consultation['status']] ?? $consultation['status'];
                                        ?>
                                    </span>
                                    <span class="badge bg-<?php echo $consultation['urgency_level'] == 'urgent' ? 'danger' : ($consultation['urgency_level'] == 'normal' ? 'warning' : 'success'); ?> ms-2">
                                        <?php 
                                        $urgency_labels = [
                                            'urgent' => 'عاجل',
                                            'normal' => 'عادي',
                                            'routine' => 'روتيني'
                                        ];
                                        echo $urgency_labels[$consultation['urgency_level']] ?? $consultation['urgency_level'];
                                        ?>
                                    </span>
                                </div>
                            </div>
                            
                            <p class="text-muted mb-2"><?php echo htmlspecialchars(substr($consultation['description'], 0, 150)) . '...'; ?></p>
                            
                            <div class="d-flex flex-wrap gap-3 text-muted small">
                                <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($consultation['parent_name']); ?></span>
                                <?php if ($consultation['student_name']): ?>
                                <span><i class="fas fa-child"></i> <?php echo htmlspecialchars($consultation['student_name']); ?> (<?php echo $consultation['age']; ?> سنة)</span>
                                <?php endif; ?>
                                <span><i class="fas fa-clock"></i> <?php echo date('d/m/Y H:i', strtotime($consultation['created_at'])); ?></span>
                                <span><i class="fas fa-tag"></i> <?php echo $consultation['consultation_type']; ?></span>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 text-end">
                            <div class="btn-group-vertical gap-1">
                                <a href="consultation-details.php?id=<?php echo $consultation['id']; ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> عرض التفاصيل
                                </a>
                                
                                <?php if ($consultation['status'] == 'pending'): ?>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="consultation_id" value="<?php echo $consultation['id']; ?>">
                                    <input type="hidden" name="action" value="accept">
                                    <button type="submit" class="btn btn-success btn-sm">
                                        <i class="fas fa-check"></i> قبول الاستشارة
                                    </button>
                                </form>
                                <?php endif; ?>
                                
                                <?php if ($consultation['status'] == 'assigned' && $consultation['specialist_id'] == $user['id']): ?>
                                <a href="respond-consultation.php?id=<?php echo $consultation['id']; ?>" class="btn btn-info btn-sm">
                                    <i class="fas fa-reply"></i> الرد
                                </a>
                                <a href="schedule-appointment.php?consultation_id=<?php echo $consultation['id']; ?>" class="btn btn-warning btn-sm">
                                    <i class="fas fa-calendar-plus"></i> حجز موعد
                                </a>
                                <?php endif; ?>
                                
                                <?php if ($consultation['status'] == 'in_progress' && $consultation['specialist_id'] == $user['id']): ?>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="consultation_id" value="<?php echo $consultation['id']; ?>">
                                    <input type="hidden" name="action" value="complete">
                                    <button type="submit" class="btn btn-success btn-sm">
                                        <i class="fas fa-check-circle"></i> إكمال
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد استشارات</h4>
                <p class="text-muted">ستظهر هنا طلبات الاستشارة</p>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    // فلترة الاستشارات
    document.querySelectorAll('.filter-tab').forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            const filter = this.dataset.filter;
            const consultations = document.querySelectorAll('.consultation-item');
            
            consultations.forEach(consultation => {
                if (filter === 'all' || consultation.dataset.status === filter) {
                    consultation.style.display = 'block';
                } else {
                    consultation.style.display = 'none';
                }
            });
        });
    });
    
    // تأكيد الإجراءات
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const action = this.querySelector('input[name=\"action\"]').value;
            let message = '';
            
            if (action === 'accept') {
                message = 'هل أنت متأكد من قبول هذه الاستشارة؟';
            } else if (action === 'complete') {
                message = 'هل أنت متأكد من إكمال هذه الاستشارة؟';
            }
            
            if (message && !confirm(message)) {
                e.preventDefault();
            }
        });
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
