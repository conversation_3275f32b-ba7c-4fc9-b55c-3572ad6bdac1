<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كمدير
require_admin();

$user = current_user();

$success_message = '';
$error_message = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    check_csrf();
    
    try {
        $db->beginTransaction();
        
        // إعدادات عامة
        $settings = [
            'site_name' => clean_input($_POST['site_name']),
            'site_description' => clean_input($_POST['site_description']),
            'contact_email' => clean_input($_POST['contact_email']),
            'contact_phone' => clean_input($_POST['contact_phone']),
            'max_file_upload_size' => (int)$_POST['max_file_upload_size'],
            'session_timeout' => (int)$_POST['session_timeout'],
            'maintenance_mode' => isset($_POST['maintenance_mode']) ? 'true' : 'false',
            'user_registration' => isset($_POST['user_registration']) ? 'true' : 'false',
            'ai_chat_enabled' => isset($_POST['ai_chat_enabled']) ? 'true' : 'false',
            'notifications_enabled' => isset($_POST['notifications_enabled']) ? 'true' : 'false',
            'email_notifications' => isset($_POST['email_notifications']) ? 'true' : 'false',
            'auto_backup' => isset($_POST['auto_backup']) ? 'true' : 'false'
        ];
        
        // التحقق من وجود جدول platform_settings أولاً
        $check_table = $db->query("SHOW TABLES LIKE 'platform_settings'");
        if ($check_table->rowCount() == 0) {
            throw new Exception("جدول الإعدادات غير موجود. يرجى تشغيل إعداد الجداول المفقودة أولاً.");
        }

        foreach ($settings as $key => $value) {
            $query = "INSERT INTO platform_settings (setting_key, setting_value, setting_type)
                     VALUES (:key, :value, 'string')
                     ON DUPLICATE KEY UPDATE setting_value = :value2";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':key', $key);
            $stmt->bindParam(':value', $value);
            $stmt->bindParam(':value2', $value);
            $stmt->execute();
        }
        
        $db->commit();
        $success_message = "تم حفظ الإعدادات بنجاح";
        
    } catch (PDOException $e) {
        $db->rollBack();
        $error_message = "خطأ في حفظ الإعدادات: " . $e->getMessage();
    }
}

// جلب الإعدادات الحالية
try {
    // التحقق من وجود جدول platform_settings أولاً
    $check_table = $db->query("SHOW TABLES LIKE 'platform_settings'");
    if ($check_table->rowCount() == 0) {
        // إذا لم يكن الجدول موجوداً، استخدم القيم الافتراضية
        $settings_data = [
            'site_name' => 'نبراس',
            'site_description' => 'منصة تعليمية تفاعلية للأطفال',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+966123456789',
            'max_file_upload_size' => '10485760',
            'session_timeout' => '3600',
            'maintenance_mode' => 'false',
            'user_registration' => 'true',
            'ai_chat_enabled' => 'true',
            'notifications_enabled' => 'true',
            'email_notifications' => 'false',
            'auto_backup' => 'false'
        ];
        $error_message = "تحذير: جدول الإعدادات غير موجود. يرجى تشغيل إعداد الجداول المفقودة.";
    } else {
        $settings_query = "SELECT setting_key, setting_value FROM platform_settings";
        $settings_stmt = $db->prepare($settings_query);
        $settings_stmt->execute();
        $settings_data = $settings_stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    }
} catch (PDOException $e) {
    // في حالة الخطأ، استخدم القيم الافتراضية
    $settings_data = [
        'site_name' => 'نبراس',
        'site_description' => 'منصة تعليمية تفاعلية للأطفال',
        'contact_email' => '<EMAIL>',
        'contact_phone' => '+966123456789',
        'max_file_upload_size' => '10485760',
        'session_timeout' => '3600',
        'maintenance_mode' => 'false',
        'user_registration' => 'true',
        'ai_chat_enabled' => 'true',
        'notifications_enabled' => 'true',
        'email_notifications' => 'false',
        'auto_backup' => 'false'
    ];
    // إخفاء رسالة الخطأ لتجنب إزعاج المستخدم
    // $error_message = "خطأ في جلب الإعدادات: " . $e->getMessage();
}

// دالة للحصول على قيمة الإعداد
function getSetting($key, $default = '') {
    global $settings_data;
    return $settings_data[$key] ?? $default;
}

// إعداد متغيرات الصفحة
$page_title = 'إعدادات النظام';
$page_description = 'إدارة إعدادات منصة نبراس التعليمية';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
.admin-container {
    /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
    min-height: 100vh;
    padding: 2rem 0;
}

.settings-container {
    max-width: 1000px;
    margin: 0 auto;
}

.settings-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f3f4;
}

.section-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 1.5rem;
    color: white;
}

.section-icon.general { background: #4a90e2; }
.section-icon.features { background: #28a745; }
.section-icon.security { background: #dc3545; }
.section-icon.system { background: #ffc107; }

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.setting-group {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    border-left: 4px solid #4a90e2;
}

.setting-item {
    margin-bottom: 1.5rem;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: block;
}

.setting-description {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #4a90e2;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.system-info {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.info-item {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
}

.info-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.info-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.save-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

@media (max-width: 768px) {
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .info-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Header Section - Unified Admin Header */
.admin-header {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 5rem;
}

.admin-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #333;
}

.admin-header p {
    opacity: 0.8;
    margin: 0;
    color: #333;
}
</style>

<?php include 'includes/admin-nav.php'; ?>

<div class="admin-container">
    <div class="admin-content container mx-auto">
        <!-- رأس الصفحة -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-cog"></i> إعدادات النظام</h1>
                    <p>إدارة إعدادات منصة نبراس التعليمية</p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <div class="settings-container">

            <!-- رسائل النجاح والخطأ -->
            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <!-- معلومات النظام -->
            <div class="system-info">
                <h3><i class="fas fa-server"></i> معلومات النظام</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-value"><?php echo PHP_VERSION; ?></div>
                        <div class="info-label">إصدار PHP</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value"><?php echo date('Y-m-d H:i'); ?></div>
                        <div class="info-label">وقت الخادم</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value"><?php echo number_format(memory_get_usage(true) / 1024 / 1024, 2); ?> MB</div>
                        <div class="info-label">استخدام الذاكرة</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value"><?php echo number_format(disk_free_space('.') / 1024 / 1024 / 1024, 2); ?> GB</div>
                        <div class="info-label">مساحة القرص المتاحة</div>
                    </div>
                </div>
            </div>

            <!-- نموذج الإعدادات -->
            <form method="POST">
                <?php echo csrf_field(); ?>

                <!-- الإعدادات العامة -->
                <div class="settings-section">
                    <div class="section-header">
                        <div class="section-icon general">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div>
                            <h3>الإعدادات العامة</h3>
                            <p class="text-muted mb-0">إعدادات أساسية للمنصة</p>
                        </div>
                    </div>

                    <div class="settings-grid">
                        <div class="setting-group">
                            <div class="setting-item">
                                <label class="setting-label">اسم المنصة</label>
                                <div class="setting-description">الاسم الذي يظهر في العنوان والشعار</div>
                                <input type="text" name="site_name" class="form-control" 
                                       value="<?php echo htmlspecialchars(getSetting('site_name', 'نبراس')); ?>">
                            </div>

                            <div class="setting-item">
                                <label class="setting-label">وصف المنصة</label>
                                <div class="setting-description">وصف مختصر للمنصة</div>
                                <textarea name="site_description" class="form-control" rows="3"><?php echo htmlspecialchars(getSetting('site_description', 'منصة تعليمية تفاعلية للأطفال')); ?></textarea>
                            </div>
                        </div>

                        <div class="setting-group">
                            <div class="setting-item">
                                <label class="setting-label">البريد الإلكتروني للتواصل</label>
                                <div class="setting-description">البريد الإلكتروني الرسمي للمنصة</div>
                                <input type="email" name="contact_email" class="form-control" 
                                       value="<?php echo htmlspecialchars(getSetting('contact_email', '<EMAIL>')); ?>">
                            </div>

                            <div class="setting-item">
                                <label class="setting-label">رقم الهاتف</label>
                                <div class="setting-description">رقم الهاتف للتواصل</div>
                                <input type="text" name="contact_phone" class="form-control" 
                                       value="<?php echo htmlspecialchars(getSetting('contact_phone', '+966123456789')); ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الميزات -->
                <div class="settings-section">
                    <div class="section-header">
                        <div class="section-icon features">
                            <i class="fas fa-puzzle-piece"></i>
                        </div>
                        <div>
                            <h3>إعدادات الميزات</h3>
                            <p class="text-muted mb-0">تفعيل وإلغاء تفعيل ميزات المنصة</p>
                        </div>
                    </div>

                    <div class="settings-grid">
                        <div class="setting-group">
                            <div class="setting-item">
                                <label class="setting-label">تسجيل المستخدمين الجدد</label>
                                <div class="setting-description">السماح للمستخدمين الجدد بإنشاء حسابات</div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="user_registration" 
                                           <?php echo getSetting('user_registration', 'true') === 'true' ? 'checked' : ''; ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <label class="setting-label">المساعد الذكي</label>
                                <div class="setting-description">تفعيل ميزة المساعد الذكي للدردشة</div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="ai_chat_enabled" 
                                           <?php echo getSetting('ai_chat_enabled', 'true') === 'true' ? 'checked' : ''; ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="setting-group">
                            <div class="setting-item">
                                <label class="setting-label">الإشعارات الفورية</label>
                                <div class="setting-description">تفعيل نظام الإشعارات الفورية</div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="notifications_enabled" 
                                           <?php echo getSetting('notifications_enabled', 'true') === 'true' ? 'checked' : ''; ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <label class="setting-label">الإشعارات عبر البريد الإلكتروني</label>
                                <div class="setting-description">إرسال إشعارات عبر البريد الإلكتروني</div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="email_notifications" 
                                           <?php echo getSetting('email_notifications', 'false') === 'true' ? 'checked' : ''; ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الأمان والنظام -->
                <div class="settings-section">
                    <div class="section-header">
                        <div class="section-icon security">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div>
                            <h3>إعدادات الأمان والنظام</h3>
                            <p class="text-muted mb-0">إعدادات الأمان والأداء</p>
                        </div>
                    </div>

                    <div class="settings-grid">
                        <div class="setting-group">
                            <div class="setting-item">
                                <label class="setting-label">الحد الأقصى لحجم الملف (بايت)</label>
                                <div class="setting-description">الحد الأقصى لحجم الملفات المرفوعة</div>
                                <input type="number" name="max_file_upload_size" class="form-control" 
                                       value="<?php echo getSetting('max_file_upload_size', '10485760'); ?>" min="1048576" max="104857600">
                            </div>

                            <div class="setting-item">
                                <label class="setting-label">مهلة الجلسة (دقيقة)</label>
                                <div class="setting-description">مدة انتهاء صلاحية الجلسة</div>
                                <input type="number" name="session_timeout" class="form-control" 
                                       value="<?php echo getSetting('session_timeout', '1440'); ?>" min="30" max="10080">
                            </div>
                        </div>

                        <div class="setting-group">
                            <div class="setting-item">
                                <label class="setting-label">وضع الصيانة</label>
                                <div class="setting-description">تفعيل وضع الصيانة للمنصة</div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="maintenance_mode" 
                                           <?php echo getSetting('maintenance_mode', 'false') === 'true' ? 'checked' : ''; ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <label class="setting-label">النسخ الاحتياطي التلقائي</label>
                                <div class="setting-description">إنشاء نسخ احتياطية تلقائية</div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="auto_backup" 
                                           <?php echo getSetting('auto_backup', 'false') === 'true' ? 'checked' : ''; ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- حفظ الإعدادات -->
                <div class="save-section">
                    <button type="submit" name="save_settings" class="nibrass-btn nibrass-btn-success nibrass-btn-lg">
                        <i class="fas fa-save"></i> حفظ جميع الإعدادات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// تضمين الفوتر
include '../includes/footer.php';
?>
