<?php
/**
 * Notification Actions API
 * Handle notification CRUD operations
 */

require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized'], JSON_UNESCAPED_UNICODE);
    exit;
}

$user = current_user();

// إعداد headers للـ JSON API
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة الطلبات
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            handleGetRequest($action);
            break;
            
        case 'POST':
            handlePostRequest($action);
            break;
            
        case 'PUT':
            handlePutRequest($action);
            break;
            
        case 'DELETE':
            handleDeleteRequest($action);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed'], JSON_UNESCAPED_UNICODE);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
}

// معالجة طلبات GET
function handleGetRequest($action) {
    global $user;
    
    switch ($action) {
        case 'list':
            getNotifications();
            break;
            
        case 'unread_count':
            getUnreadCount();
            break;
            
        case 'recent':
            getRecentNotifications();
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action'], JSON_UNESCAPED_UNICODE);
    }
}

// معالجة طلبات POST
function handlePostRequest($action) {
    global $user;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($action) {
        case 'create':
            createNotification($input);
            break;
            
        case 'mark_read':
            markAsRead($input);
            break;
            
        case 'mark_all_read':
            markAllAsRead();
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action'], JSON_UNESCAPED_UNICODE);
    }
}

// معالجة طلبات PUT
function handlePutRequest($action) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($action) {
        case 'update':
            updateNotification($input);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action'], JSON_UNESCAPED_UNICODE);
    }
}

// معالجة طلبات DELETE
function handleDeleteRequest($action) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($action) {
        case 'delete':
            deleteNotification($input);
            break;
            
        case 'clear_all':
            clearAllNotifications();
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action'], JSON_UNESCAPED_UNICODE);
    }
}

// جلب جميع الإشعارات
function getNotifications() {
    global $db, $user;
    
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;
    
    $query = "SELECT * FROM notifications 
              WHERE user_id = :user_id 
              ORDER BY created_at DESC 
              LIMIT :limit OFFSET :offset";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user['id']);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب العدد الإجمالي
    $count_query = "SELECT COUNT(*) as total FROM notifications WHERE user_id = :user_id";
    $count_stmt = $db->prepare($count_query);
    $count_stmt->bindParam(':user_id', $user['id']);
    $count_stmt->execute();
    $total = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo json_encode([
        'success' => true,
        'data' => $notifications,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => (int)$total,
            'pages' => ceil($total / $limit)
        ]
    ], JSON_UNESCAPED_UNICODE);
}

// جلب عدد الإشعارات غير المقروءة
function getUnreadCount() {
    global $db, $user;
    
    $query = "SELECT COUNT(*) as count FROM notifications 
              WHERE user_id = :user_id AND is_read = 0";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user['id']);
    $stmt->execute();
    
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'count' => (int)$result['count']
    ], JSON_UNESCAPED_UNICODE);
}

// جلب الإشعارات الحديثة
function getRecentNotifications() {
    global $db, $user;
    
    $limit = (int)($_GET['limit'] ?? 5);
    
    $query = "SELECT * FROM notifications 
              WHERE user_id = :user_id 
              ORDER BY created_at DESC 
              LIMIT :limit";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user['id']);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'data' => $notifications
    ], JSON_UNESCAPED_UNICODE);
}

// إنشاء إشعار جديد
function createNotification($data) {
    global $db, $user;
    
    $required_fields = ['title', 'message'];
    foreach ($required_fields as $field) {
        if (empty($data[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field $field is required"], JSON_UNESCAPED_UNICODE);
            return;
        }
    }
    
    $query = "INSERT INTO notifications (user_id, title, message, type, action_url) 
              VALUES (:user_id, :title, :message, :type, :action_url)";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user['id']);
    $stmt->bindParam(':title', $data['title']);
    $stmt->bindParam(':message', $data['message']);
    $stmt->bindParam(':type', $data['type'] ?? 'info');
    $stmt->bindParam(':action_url', $data['action_url'] ?? null);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true,
            'id' => $db->lastInsertId(),
            'message' => 'تم إنشاء الإشعار بنجاح'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'فشل في إنشاء الإشعار'], JSON_UNESCAPED_UNICODE);
    }
}

// تحديد إشعار كمقروء
function markAsRead($data) {
    global $db, $user;
    
    if (empty($data['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Notification ID is required'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    $query = "UPDATE notifications SET is_read = 1 
              WHERE id = :id AND user_id = :user_id";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $data['id']);
    $stmt->bindParam(':user_id', $user['id']);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديد الإشعار كمقروء'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'فشل في تحديث الإشعار'], JSON_UNESCAPED_UNICODE);
    }
}

// تحديد جميع الإشعارات كمقروءة
function markAllAsRead() {
    global $db, $user;
    
    $query = "UPDATE notifications SET is_read = 1 WHERE user_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user['id']);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديد جميع الإشعارات كمقروءة'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'فشل في تحديث الإشعارات'], JSON_UNESCAPED_UNICODE);
    }
}

// تحديث إشعار
function updateNotification($data) {
    global $db, $user;
    
    if (empty($data['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Notification ID is required'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    $fields = [];
    $params = [':id' => $data['id'], ':user_id' => $user['id']];
    
    if (isset($data['title'])) {
        $fields[] = 'title = :title';
        $params[':title'] = $data['title'];
    }
    
    if (isset($data['message'])) {
        $fields[] = 'message = :message';
        $params[':message'] = $data['message'];
    }
    
    if (isset($data['type'])) {
        $fields[] = 'type = :type';
        $params[':type'] = $data['type'];
    }
    
    if (isset($data['action_url'])) {
        $fields[] = 'action_url = :action_url';
        $params[':action_url'] = $data['action_url'];
    }
    
    if (empty($fields)) {
        http_response_code(400);
        echo json_encode(['error' => 'No fields to update'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    $query = "UPDATE notifications SET " . implode(', ', $fields) . " 
              WHERE id = :id AND user_id = :user_id";
    
    $stmt = $db->prepare($query);
    
    if ($stmt->execute($params)) {
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث الإشعار بنجاح'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'فشل في تحديث الإشعار'], JSON_UNESCAPED_UNICODE);
    }
}

// حذف إشعار
function deleteNotification($data) {
    global $db, $user;
    
    if (empty($data['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Notification ID is required'], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    $query = "DELETE FROM notifications WHERE id = :id AND user_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $data['id']);
    $stmt->bindParam(':user_id', $user['id']);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف الإشعار بنجاح'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'فشل في حذف الإشعار'], JSON_UNESCAPED_UNICODE);
    }
}

// مسح جميع الإشعارات
function clearAllNotifications() {
    global $db, $user;
    
    $query = "DELETE FROM notifications WHERE user_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user['id']);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'تم مسح جميع الإشعارات بنجاح'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'فشل في مسح الإشعارات'], JSON_UNESCAPED_UNICODE);
    }
}
?>
