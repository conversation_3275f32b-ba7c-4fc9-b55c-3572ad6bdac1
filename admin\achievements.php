<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كمدير
require_admin();

$user = current_user();

// معالجة الإجراءات
$success_message = '';
$error_message = '';

// معالجة إضافة إنجاز جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_achievement'])) {
    check_csrf();
    
    $title = clean_input($_POST['title']);
    $description = clean_input($_POST['description']);
    $icon = clean_input($_POST['icon']) ?: 'fas fa-trophy';
    $category = clean_input($_POST['category']);
    $difficulty_level = clean_input($_POST['difficulty_level']) ?: 'easy';
    $reward_points = (int)($_POST['reward_points'] ?? 10);
    $target_age_min = (int)($_POST['target_age_min'] ?? 3);
    $target_age_max = (int)($_POST['target_age_max'] ?? 18);

    if (empty($title) || empty($description)) {
        $error_message = "يرجى ملء جميع الحقول المطلوبة";
    } else {
        try {
            $insert_query = "INSERT INTO achievements (title, description, icon, category, difficulty_level, reward_points, target_age_min, target_age_max)
                            VALUES (:title, :description, :icon, :category, :difficulty_level, :reward_points, :target_age_min, :target_age_max)";
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':title', $title);
            $insert_stmt->bindParam(':description', $description);
            $insert_stmt->bindParam(':icon', $icon);
            $insert_stmt->bindParam(':category', $category);
            $insert_stmt->bindParam(':difficulty_level', $difficulty_level);
            $insert_stmt->bindParam(':reward_points', $reward_points);
            $insert_stmt->bindParam(':target_age_min', $target_age_min);
            $insert_stmt->bindParam(':target_age_max', $target_age_max);
            
            if ($insert_stmt->execute()) {
                $success_message = "تم إضافة الإنجاز بنجاح";
            } else {
                $error_message = "فشل في إضافة الإنجاز";
            }
        } catch (PDOException $e) {
            // التحقق من وجود الجدول
            if (strpos($e->getMessage(), "doesn't exist") !== false) {
                $error_message = "جدول الإنجازات غير موجود. يرجى تشغيل إعداد الجداول المفقودة أولاً.";
            } else {
                $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
            }
        }
    }
}

// معالجة تفعيل/إلغاء تفعيل الإنجاز
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['toggle_achievement'])) {
    check_csrf();
    
    $achievement_id = (int)$_POST['achievement_id'];
    $current_status = (int)$_POST['current_status'];
    $new_status = $current_status ? 0 : 1;
    
    try {
        $update_query = "UPDATE achievements SET is_active = :status WHERE id = :achievement_id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':status', $new_status);
        $update_stmt->bindParam(':achievement_id', $achievement_id);
        
        if ($update_stmt->execute()) {
            $action_text = $new_status ? 'تفعيل' : 'إلغاء تفعيل';
            $success_message = "تم {$action_text} الإنجاز بنجاح";
        } else {
            $error_message = "فشل في تحديث حالة الإنجاز";
        }
    } catch (PDOException $e) {
        $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
    }
}

// معالجة حذف الإنجاز
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_achievement'])) {
    check_csrf();
    
    $achievement_id = (int)$_POST['achievement_id'];
    
    try {
        $delete_query = "DELETE FROM achievements WHERE id = :achievement_id";
        $delete_stmt = $db->prepare($delete_query);
        $delete_stmt->bindParam(':achievement_id', $achievement_id);
        
        if ($delete_stmt->execute()) {
            $success_message = "تم حذف الإنجاز بنجاح";
        } else {
            $error_message = "فشل في حذف الإنجاز";
        }
    } catch (PDOException $e) {
        $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
    }
}

// جلب الإنجازات مع الفلترة والبحث
$search = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';
$rarity_filter = $_GET['rarity'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$limit = 12;
$offset = ($page - 1) * $limit;

try {
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(title LIKE :search OR description LIKE :search)";
        $params[':search'] = "%{$search}%";
    }
    
    if (!empty($category_filter)) {
        $where_conditions[] = "category = :category";
        $params[':category'] = $category_filter;
    }
    
    if (!empty($rarity_filter)) {
        $where_conditions[] = "rarity = :rarity";
        $params[':rarity'] = $rarity_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // جلب الإنجازات
    $achievements_query = "SELECT * FROM achievements {$where_clause} ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
    $achievements_stmt = $db->prepare($achievements_query);
    
    foreach ($params as $key => $value) {
        $achievements_stmt->bindValue($key, $value);
    }
    $achievements_stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $achievements_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $achievements_stmt->execute();
    $achievements = $achievements_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب العدد الإجمالي
    $count_query = "SELECT COUNT(*) as total FROM achievements {$where_clause}";
    $count_stmt = $db->prepare($count_query);
    
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    
    $count_stmt->execute();
    $total_achievements = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_achievements / $limit);
    
} catch (PDOException $e) {
    // التحقق من وجود الجدول
    try {
        $check_table = $db->query("SHOW TABLES LIKE 'achievements'");
        if ($check_table->rowCount() == 0) {
            $error_message = "جدول الإنجازات غير موجود. يرجى تشغيل إعداد الجداول المفقودة.";
        } else {
            $error_message = "خطأ في جلب الإنجازات: " . $e->getMessage();
        }
    } catch (Exception $e2) {
        $error_message = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
    }

    $achievements = [];
    $total_achievements = 0;
    $total_pages = 0;
}

// إعداد متغيرات الصفحة
$page_title = 'إدارة الإنجازات والجوائز';
$page_description = 'إدارة الإنجازات والجوائز في منصة نبراس';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<!-- Additional Fonts for Enhanced Arabic Support -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

<style>
/* Enhanced Admin Styling - Consistent with Dashboard */
body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.admin-container {
    min-height: 100vh;
    padding: 2rem 0;
    background: #f8f9fa;
}

.admin-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Section - Unified Admin Header */
.admin-header {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 5rem;
}

.admin-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #333;
}

.admin-header p {
    opacity: 0.8;
    margin: 0;
    color: #333;
}

/* Form and Filter Sections */
.admin-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.section-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.search-filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1rem;
    align-items: end;
}

/* Achievement Cards Grid */
.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.achievement-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    text-align: center;
    overflow: hidden;
}

.achievement-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.achievement-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--badge-color, linear-gradient(90deg, #4a90e2, #2c5aa0));
}

.achievement-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1rem;
    background: var(--badge-color, linear-gradient(135deg, #4a90e2, #2c5aa0));
    box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

.achievement-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.achievement-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.achievement-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.85rem;
}

.points-required {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 600;
}

.difficulty-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.difficulty-badge.easy { background: #d4edda; color: #155724; }
.difficulty-badge.medium { background: #fff3cd; color: #856404; }
.difficulty-badge.hard { background: #f8d7da; color: #721c24; }
.difficulty-badge.expert { background: #f3e5f5; color: #7b1fa2; }

.category-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: inline-block;
}

.category-badge.تعليمية { background: #e8f5e8; color: #2e7d32; }
.category-badge.رياضية { background: #e3f2fd; color: #1976d2; }
.category-badge.إبداعية { background: #f3e5f5; color: #7b1fa2; }
.category-badge.اجتماعية { background: #fff3e0; color: #f57c00; }
.category-badge.خاصة { background: #ffebee; color: #c62828; }

.achievement-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.status-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #28a745;
}

.status-indicator.inactive {
    background: #dc3545;
}

.color-picker {
    width: 50px;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
    .achievements-grid {
        grid-template-columns: 1fr;
    }
    
    .search-filters {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .achievement-actions {
        flex-direction: column;
    }
}
</style>

<?php include 'includes/admin-nav.php'; ?>

<div class="admin-container">
    <div class="admin-content container mx-auto">
        <!-- رأس الصفحة -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-trophy"></i> إدارة الإنجازات والجوائز</h1>
                    <p>إدارة الإنجازات والجوائز في منصة نبراس</p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                <?php if (strpos($error_message, "جدول الإنجازات غير موجود") !== false): ?>
                    <div class="mt-3">
                        <a href="../create_achievements_table.php" class="btn btn-warning">
                            <i class="fas fa-database"></i> إنشاء جدول الإنجازات
                        </a>
                        <a href="../setup_missing_tables.php" class="btn btn-info">
                            <i class="fas fa-tools"></i> إعداد جميع الجداول المفقودة
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- إضافة إنجاز جديد -->
        <div class="admin-section">
            <h3 class="section-title">
                <i class="fas fa-plus-circle"></i> إضافة إنجاز جديد
            </h3>
            
            <form method="POST">
                <?php echo csrf_field(); ?>
                
                <div class="form-grid">
                    <div>
                        <label class="form-label">عنوان الإنجاز *</label>
                        <input type="text" name="title" class="form-control" required>
                    </div>

                    <div>
                        <label class="form-label">أيقونة الإنجاز</label>
                        <input type="text" name="icon" class="form-control" placeholder="fas fa-trophy" value="fas fa-trophy">
                    </div>

                    <div>
                        <label class="form-label">نقاط المكافأة</label>
                        <input type="number" name="reward_points" class="form-control" value="10" min="0">
                    </div>

                    <div>
                        <label class="form-label">الفئة *</label>
                        <select name="category" class="form-select" required>
                            <option value="">اختر الفئة</option>
                            <option value="تعليمي">تعليمي</option>
                            <option value="إبداعي">إبداعي</option>
                            <option value="اجتماعي">اجتماعي</option>
                            <option value="رياضي">رياضي</option>
                            <option value="تقني">تقني</option>
                            <option value="أخلاقي">أخلاقي</option>
                            <option value="عام">عام</option>
                        </select>
                    </div>

                    <div>
                        <label class="form-label">مستوى الصعوبة</label>
                        <select name="difficulty_level" class="form-select">
                            <option value="easy">سهل</option>
                            <option value="medium">متوسط</option>
                            <option value="hard">صعب</option>
                            <option value="expert">خبير</option>
                        </select>
                    </div>

                    <div>
                        <label class="form-label">العمر الأدنى</label>
                        <input type="number" name="target_age_min" class="form-control" value="3" min="3" max="18">
                    </div>

                    <div>
                        <label class="form-label">العمر الأعلى</label>
                        <input type="number" name="target_age_max" class="form-control" value="18" min="3" max="18">
                    </div>
                </div>
                
                <div class="mt-3">
                    <label class="form-label">وصف الإنجاز *</label>
                    <textarea name="description" class="form-control" rows="3" required></textarea>
                </div>
                
                <div class="mt-3">
                    <button type="submit" name="add_achievement" class="nibrass-btn nibrass-btn-success">
                        <i class="fas fa-plus"></i> إضافة الإنجاز
                    </button>
                </div>
            </form>
        </div>

        <!-- فلاتر البحث -->
        <div class="admin-section">
            <form method="GET" class="search-filters">
                <div>
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث في العنوان أو الوصف..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div>
                    <label class="form-label">الفئة</label>
                    <select name="category" class="form-select">
                        <option value="">جميع الفئات</option>
                        <option value="تعليمية" <?php echo $category_filter === 'تعليمية' ? 'selected' : ''; ?>>تعليمية</option>
                        <option value="رياضية" <?php echo $category_filter === 'رياضية' ? 'selected' : ''; ?>>رياضية</option>
                        <option value="إبداعية" <?php echo $category_filter === 'إبداعية' ? 'selected' : ''; ?>>إبداعية</option>
                        <option value="اجتماعية" <?php echo $category_filter === 'اجتماعية' ? 'selected' : ''; ?>>اجتماعية</option>
                        <option value="خاصة" <?php echo $category_filter === 'خاصة' ? 'selected' : ''; ?>>خاصة</option>
                    </select>
                </div>
                
                <div>
                    <label class="form-label">الندرة</label>
                    <select name="rarity" class="form-select">
                        <option value="">جميع الأنواع</option>
                        <option value="common" <?php echo $rarity_filter === 'common' ? 'selected' : ''; ?>>عادي</option>
                        <option value="uncommon" <?php echo $rarity_filter === 'uncommon' ? 'selected' : ''; ?>>غير عادي</option>
                        <option value="rare" <?php echo $rarity_filter === 'rare' ? 'selected' : ''; ?>>نادر</option>
                        <option value="epic" <?php echo $rarity_filter === 'epic' ? 'selected' : ''; ?>>ملحمي</option>
                        <option value="legendary" <?php echo $rarity_filter === 'legendary' ? 'selected' : ''; ?>>أسطوري</option>
                    </select>
                </div>
                
                <div>
                    <button type="submit" class="nibrass-btn nibrass-btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>

        <!-- شبكة الإنجازات -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3 style="color: black;"><i class="fas fa-list"></i> قائمة الإنجازات</h3>
            <div style="color: black; opacity: 0.9;">
                إجمالي النتائج: <?php echo number_format($total_achievements); ?>
            </div>
        </div>

        <?php if (empty($achievements)): ?>
            <div class="admin-section text-center py-5">
                <i class="fas fa-trophy fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد إنجازات</h4>
                <p class="text-muted">لم يتم العثور على إنجازات مطابقة لمعايير البحث</p>
            </div>
        <?php else: ?>
            <div class="achievements-grid">
                <?php foreach ($achievements as $achievement): ?>
                    <div class="achievement-card" style="--badge-color: #4a90e2;">
                        <div class="status-indicator <?php echo $achievement['is_active'] ? 'active' : 'inactive'; ?>"></div>

                        <div class="achievement-icon">
                            <i class="<?php echo htmlspecialchars($achievement['icon'] ?? 'fas fa-trophy'); ?>"></i>
                        </div>

                        <div class="achievement-title"><?php echo htmlspecialchars($achievement['title']); ?></div>

                        <span class="category-badge <?php echo str_replace(' ', '-', $achievement['category']); ?>">
                            <?php echo htmlspecialchars($achievement['category']); ?>
                        </span>

                        <div class="achievement-description">
                            <?php echo htmlspecialchars($achievement['description']); ?>
                        </div>

                        <div class="achievement-meta">
                            <span class="points-required">
                                <i class="fas fa-star"></i> <?php echo number_format($achievement['reward_points'] ?? 0); ?> نقطة
                            </span>
                            <span class="difficulty-badge <?php echo $achievement['difficulty_level'] ?? 'easy'; ?>">
                                <?php
                                $difficulties = [
                                    'easy' => 'سهل',
                                    'medium' => 'متوسط',
                                    'hard' => 'صعب',
                                    'expert' => 'خبير'
                                ];
                                echo $difficulties[$achievement['difficulty_level'] ?? 'easy'] ?? 'سهل';
                                ?>
                            </span>
                        </div>
                        
                        <div class="achievement-actions">
                            <!-- تفعيل/إلغاء تفعيل -->
                            <form method="POST" style="display: inline;">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="achievement_id" value="<?php echo $achievement['id']; ?>">
                                <input type="hidden" name="current_status" value="<?php echo $achievement['is_active']; ?>">
                                <button type="submit" name="toggle_achievement" 
                                        class="nibrass-btn <?php echo $achievement['is_active'] ? 'nibrass-btn-warning' : 'nibrass-btn-success'; ?> btn-sm"
                                        onclick="return confirm('هل أنت متأكد من تغيير حالة هذا الإنجاز؟')">
                                    <i class="fas <?php echo $achievement['is_active'] ? 'fa-pause' : 'fa-play'; ?>"></i>
                                </button>
                            </form>

                            <!-- حذف -->
                            <form method="POST" style="display: inline;">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="achievement_id" value="<?php echo $achievement['id']; ?>">
                                <button type="submit" name="delete_achievement" 
                                        class="nibrass-btn nibrass-btn-danger btn-sm"
                                        onclick="return confirm('هل أنت متأكد من حذف هذا الإنجاز؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// تضمين الفوتر
include '../includes/footer.php';
?>
