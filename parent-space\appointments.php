<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كولي أمر
require_parent();

$user = current_user();

// جلب جميع المواعيد الخاصة بولي الأمر
$appointments_query = "SELECT a.*, s.name as specialist_name, s.specialization, st.name as student_name
                       FROM appointments a
                       LEFT JOIN users s ON a.specialist_id = s.id
                       LEFT JOIN students st ON a.student_id = st.id
                       WHERE a.parent_id = :user_id
                       ORDER BY a.appointment_date DESC";
$appointments_stmt = $db->prepare($appointments_query);
$appointments_stmt->bindParam(':user_id', $user['id']);
$appointments_stmt->execute();
$appointments = $appointments_stmt->fetchAll();

// تصنيف المواعيد
$upcoming_appointments = array_filter($appointments, function($app) {
    return strtotime($app['appointment_date']) >= time();
});

$past_appointments = array_filter($appointments, function($app) {
    return strtotime($app['appointment_date']) < time();
});

// إعداد متغيرات الصفحة
$page_title = 'إدارة المواعيد';
$page_description = 'إدارة وتتبع مواعيدك مع الأخصائيين';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
/* Floating shapes animation for header */
.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.shape {
    position: absolute;
    color: rgba(255,255,255,0.1);
    animation: float 6s ease-in-out infinite;
}

.shape:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.shape:nth-child(4) {
    top: 40%;
    right: 30%;
    animation-delay: 6s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.appointment-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.appointment-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #17a2b8, #20c997);
}

.appointment-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.appointment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.appointment-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    margin-left: 1rem;
}

.appointment-icon { background: linear-gradient(135deg, #17a2b8, #138496); }
.appointment-icon.upcoming { background: linear-gradient(135deg, #17a2b8, #138496); }
.appointment-icon.past { background: linear-gradient(135deg, #6c757d, #495057); }
.appointment-icon.confirmed { background: linear-gradient(135deg, #28a745, #20c997); }
.appointment-icon.cancelled { background: linear-gradient(135deg, #dc3545, #c82333); }

.appointment-details {
    flex: 1;
}

.appointment-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 0.5rem 0;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.appointment-subtitle {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.appointment-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 0.9rem;
}

.meta-item i {
    margin-left: 0.5rem;
    color: #17a2b8;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-confirmed { background: #d1ecf1; color: #0c5460; }
.status-completed { background: #d4edda; color: #155724; }
.status-cancelled { background: #f8d7da; color: #721c24; }

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
}

.empty-state i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.tabs-arabic {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 2rem;
}

.tabs-arabic .nav-link {
    color: #666 !important;
    font-weight: 600;
    border: none;
    border-bottom: 3px solid transparent;
    padding: 1rem 1.5rem;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: transparent !important;
    text-decoration: none;
}

.tabs-arabic .nav-link.active {
    color: #4a90e2 !important;
    border-bottom-color: #4a90e2;
    background: none !important;
}

.tabs-arabic .nav-link:hover {
    color: #4a90e2 !important;
    border-bottom-color: rgba(74, 144, 226, 0.3);
    background: transparent !important;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@media (max-width: 768px) {
    .appointment-header {
        flex-direction: column;
        text-align: center;
    }
    
    .appointment-icon {
        margin-left: 0;
        margin-bottom: 1rem;
    }
    
    .appointment-meta {
        justify-content: center;
    }
}
</style>

<!-- قسم العنوان -->
<section class="nibrass-header position-relative">
    <div class="floating-shapes">
        <div class="shape"><i class="fas fa-calendar-check fa-3x"></i></div>
        <div class="shape"><i class="fas fa-clock fa-3x"></i></div>
        <div class="shape"><i class="fas fa-user-md fa-3x"></i></div>
        <div class="shape"><i class="fas fa-calendar-alt fa-3x"></i></div>
    </div>
    <div class="container">
        <div class="hero-content text-center">
            <h1><i class="fas fa-calendar-check"></i> إدارة المواعيد</h1>
            <p class="lead">تتبع وإدارة مواعيدك مع الأخصائيين</p>
            <div class="mt-4">
                <span class="badge bg-light text-dark fs-6 me-2">
                    <i class="fas fa-clock"></i> <?php echo count($upcoming_appointments); ?> موعد قادم
                </span>
                <span class="badge bg-light text-dark fs-6">
                    <i class="fas fa-history"></i> <?php echo count($past_appointments); ?> موعد سابق
                </span>
            </div>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <!-- أزرار الإجراءات -->
        <div class="row mb-4">
            <div class="col-md-6">
                <a href="consultation-request.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus"></i> حجز موعد جديد
                </a>
            </div>
            <div class="col-md-6 text-end">
                <div class="btn-group">
                    <button class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-outline-secondary" onclick="exportAppointments()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>
        </div>

        <!-- التبويبات -->
        <ul class="nav nav-tabs tabs-arabic" id="appointmentTabs">
            <li class="nav-item">
                <a class="nav-link active" data-bs-toggle="tab" href="#upcoming">
                    <i class="fas fa-clock"></i> المواعيد القادمة (<?php echo count($upcoming_appointments); ?>)
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#past">
                    <i class="fas fa-history"></i> المواعيد السابقة (<?php echo count($past_appointments); ?>)
                </a>
            </li>
        </ul>

        <!-- محتوى التبويبات -->
        <div class="tab-content">
            <!-- المواعيد القادمة -->
            <div class="tab-pane fade show active" id="upcoming">
                <?php if (!empty($upcoming_appointments)): ?>
                    <?php foreach ($upcoming_appointments as $appointment): ?>
                        <div class="appointment-card">
                            <div class="appointment-header">
                                <div class="d-flex align-items-center">
                                    <div class="appointment-icon <?php echo $appointment['status']; ?>">
                                        <i class="fas fa-calendar-check"></i>
                                    </div>
                                    <div class="appointment-details">
                                        <h4 class="appointment-title">
                                            د. <?php echo htmlspecialchars($appointment['specialist_name']); ?>
                                        </h4>
                                        <p class="appointment-subtitle">
                                            <?php echo htmlspecialchars($appointment['specialization'] ?? 'أخصائي'); ?>
                                        </p>
                                    </div>
                                </div>
                                <span class="status-badge status-<?php echo $appointment['status']; ?>">
                                    <?php echo $appointment['status']; ?>
                                </span>
                            </div>
                            
                            <div class="appointment-meta">
                                <div class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <?php echo date('d/m/Y', strtotime($appointment['appointment_date'])); ?>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <?php echo date('H:i', strtotime($appointment['appointment_date'])); ?>
                                </div>
                                <?php if ($appointment['student_name']): ?>
                                <div class="meta-item">
                                    <i class="fas fa-child"></i>
                                    <?php echo htmlspecialchars($appointment['student_name']); ?>
                                </div>
                                <?php endif; ?>
                                <div class="meta-item">
                                    <i class="fas fa-info-circle"></i>
                                    <?php echo htmlspecialchars($appointment['appointment_type'] ?? 'استشارة عامة'); ?>
                                </div>
                            </div>
                            
                            <?php if ($appointment['notes']): ?>
                            <div class="mt-3">
                                <strong>ملاحظات:</strong>
                                <p class="text-muted"><?php echo htmlspecialchars($appointment['notes']); ?></p>
                            </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-calendar-times"></i>
                        <h4>لا توجد مواعيد قادمة</h4>
                        <p>احجز موعدك مع أحد الأخصائيين</p>
                        <a href="consultation-request.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> حجز موعد جديد
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- المواعيد السابقة -->
            <div class="tab-pane fade" id="past">
                <?php if (!empty($past_appointments)): ?>
                    <?php foreach ($past_appointments as $appointment): ?>
                        <div class="appointment-card">
                            <div class="appointment-header">
                                <div class="d-flex align-items-center">
                                    <div class="appointment-icon past">
                                        <i class="fas fa-history"></i>
                                    </div>
                                    <div class="appointment-details">
                                        <h4 class="appointment-title">
                                            د. <?php echo htmlspecialchars($appointment['specialist_name']); ?>
                                        </h4>
                                        <p class="appointment-subtitle">
                                            <?php echo htmlspecialchars($appointment['specialization'] ?? 'أخصائي'); ?>
                                        </p>
                                    </div>
                                </div>
                                <span class="status-badge status-<?php echo $appointment['status']; ?>">
                                    <?php echo $appointment['status']; ?>
                                </span>
                            </div>
                            
                            <div class="appointment-meta">
                                <div class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <?php echo date('d/m/Y', strtotime($appointment['appointment_date'])); ?>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <?php echo date('H:i', strtotime($appointment['appointment_date'])); ?>
                                </div>
                                <?php if ($appointment['student_name']): ?>
                                <div class="meta-item">
                                    <i class="fas fa-child"></i>
                                    <?php echo htmlspecialchars($appointment['student_name']); ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-history"></i>
                        <h4>لا توجد مواعيد سابقة</h4>
                        <p>ستظهر هنا مواعيدك السابقة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    function exportAppointments() {
        NibrassHelpers.showAlert('سيتم تطوير ميزة التصدير قريباً', 'info');
    }
    
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تأثيرات التمرير
        const cards = document.querySelectorAll('.appointment-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
