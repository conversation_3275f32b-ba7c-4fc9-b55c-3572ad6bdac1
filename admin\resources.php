<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كمدير
require_admin();

$user = current_user();

// معالجة الإجراءات
$success_message = '';
$error_message = '';

// معالجة إضافة مورد جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_resource'])) {
    check_csrf();
    
    $title = clean_input($_POST['title']);
    $description = clean_input($_POST['description']);
    $file_url = clean_input($_POST['file_url']);
    $file_type = clean_input($_POST['file_type']);
    $category = clean_input($_POST['category']);
    $target_age = (int)$_POST['target_age'];
    $file_size = clean_input($_POST['file_size']);
    
    if (empty($title) || empty($description) || empty($file_url)) {
        $error_message = "يرجى ملء جميع الحقول المطلوبة";
    } else {
        try {
            $insert_query = "INSERT INTO educational_resources (title, description, file_url, file_type, category, target_age, file_size) 
                            VALUES (:title, :description, :file_url, :file_type, :category, :target_age, :file_size)";
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->bindParam(':title', $title);
            $insert_stmt->bindParam(':description', $description);
            $insert_stmt->bindParam(':file_url', $file_url);
            $insert_stmt->bindParam(':file_type', $file_type);
            $insert_stmt->bindParam(':category', $category);
            $insert_stmt->bindParam(':target_age', $target_age);
            $insert_stmt->bindParam(':file_size', $file_size);
            
            if ($insert_stmt->execute()) {
                $success_message = "تم إضافة المورد بنجاح";
            } else {
                $error_message = "فشل في إضافة المورد";
            }
        } catch (PDOException $e) {
            $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
        }
    }
}

// معالجة تفعيل/إلغاء تفعيل المورد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['toggle_resource'])) {
    check_csrf();
    
    $resource_id = (int)$_POST['resource_id'];
    $current_status = (int)$_POST['current_status'];
    $new_status = $current_status ? 0 : 1;
    
    try {
        $update_query = "UPDATE educational_resources SET is_active = :status WHERE id = :resource_id";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->bindParam(':status', $new_status);
        $update_stmt->bindParam(':resource_id', $resource_id);
        
        if ($update_stmt->execute()) {
            $action_text = $new_status ? 'تفعيل' : 'إلغاء تفعيل';
            $success_message = "تم {$action_text} المورد بنجاح";
        } else {
            $error_message = "فشل في تحديث حالة المورد";
        }
    } catch (PDOException $e) {
        $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
    }
}

// معالجة حذف المورد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_resource'])) {
    check_csrf();
    
    $resource_id = (int)$_POST['resource_id'];
    
    try {
        $delete_query = "DELETE FROM educational_resources WHERE id = :resource_id";
        $delete_stmt = $db->prepare($delete_query);
        $delete_stmt->bindParam(':resource_id', $resource_id);
        
        if ($delete_stmt->execute()) {
            $success_message = "تم حذف المورد بنجاح";
        } else {
            $error_message = "فشل في حذف المورد";
        }
    } catch (PDOException $e) {
        $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
    }
}

// جلب الموارد مع الفلترة والبحث
$search = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';
$file_type_filter = $_GET['file_type'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$limit = 12;
$offset = ($page - 1) * $limit;

try {
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(title LIKE :search OR description LIKE :search)";
        $params[':search'] = "%{$search}%";
    }
    
    if (!empty($category_filter)) {
        $where_conditions[] = "category = :category";
        $params[':category'] = $category_filter;
    }
    
    if (!empty($file_type_filter)) {
        $where_conditions[] = "file_type = :file_type";
        $params[':file_type'] = $file_type_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // جلب الموارد
    $resources_query = "SELECT * FROM educational_resources {$where_clause} ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
    $resources_stmt = $db->prepare($resources_query);
    
    foreach ($params as $key => $value) {
        $resources_stmt->bindValue($key, $value);
    }
    $resources_stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $resources_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $resources_stmt->execute();
    $resources = $resources_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب العدد الإجمالي
    $count_query = "SELECT COUNT(*) as total FROM educational_resources {$where_clause}";
    $count_stmt = $db->prepare($count_query);
    
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    
    $count_stmt->execute();
    $total_resources = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_resources / $limit);
    
} catch (PDOException $e) {
    $error_message = "خطأ في جلب الموارد: " . $e->getMessage();
    $resources = [];
    $total_resources = 0;
    $total_pages = 0;
}

// إعداد متغيرات الصفحة
$page_title = 'إدارة الموارد التعليمية';
$page_description = 'إدارة الموارد التعليمية في منصة نبراس';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<!-- Additional Fonts for Enhanced Arabic Support -->
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

<style>
/* Enhanced Admin Styling - Consistent with Dashboard */
body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.admin-container {
    min-height: 100vh;
    padding: 2rem 0;
    background: #f8f9fa;
}

.admin-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Section - Unified Admin Header */
.admin-header {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 5rem;
}

.admin-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #333;
}

.admin-header p {
    opacity: 0.8;
    margin: 0;
    color: #333;
}

/* Form and Filter Sections */
.admin-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.section-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.search-filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1rem;
    align-items: end;
}

/* Resource Cards Grid */
.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.resource-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
}

.resource-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.resource-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.file-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    margin-left: 1rem;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

.file-icon.pdf { background: linear-gradient(135deg, #dc3545, #c82333); }
.file-icon.doc { background: linear-gradient(135deg, #007bff, #0056b3); }
.file-icon.ppt { background: linear-gradient(135deg, #fd7e14, #e55a00); }
.file-icon.xls { background: linear-gradient(135deg, #28a745, #1e7e34); }
.file-icon.image { background: linear-gradient(135deg, #6f42c1, #5a2d91); }
.file-icon.video { background: linear-gradient(135deg, #e83e8c, #d91a72); }
.file-icon.audio { background: linear-gradient(135deg, #20c997, #17a2b8); }
.file-icon.other { background: linear-gradient(135deg, #6c757d, #545b62); }

.resource-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.resource-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    flex: 1;
}

.resource-meta {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.85rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
}

.category-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: inline-block;
}

.category-badge.كتب { background: #e3f2fd; color: #1976d2; }
.category-badge.أوراق-عمل { background: #e8f5e8; color: #2e7d32; }
.category-badge.عروض-تقديمية { background: #fff3e0; color: #f57c00; }
.category-badge.صور-تعليمية { background: #f3e5f5; color: #7b1fa2; }
.category-badge.ملفات-صوتية { background: #ffebee; color: #c62828; }
.category-badge.عامة { background: #f5f5f5; color: #424242; }

.resource-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
    margin-top: auto;
}

.status-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #28a745;
}

.status-indicator.inactive {
    background: #dc3545;
}

.download-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .resources-grid {
        grid-template-columns: 1fr;
    }
    
    .search-filters {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .resource-actions {
        flex-direction: column;
    }
}
</style>

<?php include 'includes/admin-nav.php'; ?>

<div class="admin-container">
    <div class="admin-content container mx-auto">
        <!-- رأس الصفحة -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-folder"></i> إدارة الموارد التعليمية</h1>
                    <p>إدارة الموارد التعليمية في منصة نبراس</p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- إضافة مورد جديد -->
        <div class="admin-section">
            <h3 class="section-title">
                <i class="fas fa-plus-circle"></i> إضافة مورد تعليمي جديد
            </h3>
            
            <form method="POST">
                <?php echo csrf_field(); ?>
                
                <div class="form-grid">
                    <div>
                        <label class="form-label">عنوان المورد *</label>
                        <input type="text" name="title" class="form-control" required>
                    </div>
                    
                    <div>
                        <label class="form-label">رابط الملف *</label>
                        <input type="url" name="file_url" class="form-control" placeholder="https://..." required>
                    </div>
                    
                    <div>
                        <label class="form-label">نوع الملف *</label>
                        <select name="file_type" class="form-select" required>
                            <option value="">اختر نوع الملف</option>
                            <option value="pdf">PDF</option>
                            <option value="doc">Word Document</option>
                            <option value="ppt">PowerPoint</option>
                            <option value="xls">Excel</option>
                            <option value="image">صورة</option>
                            <option value="video">فيديو</option>
                            <option value="audio">ملف صوتي</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="form-label">الفئة *</label>
                        <select name="category" class="form-select" required>
                            <option value="">اختر الفئة</option>
                            <option value="كتب">كتب</option>
                            <option value="أوراق عمل">أوراق عمل</option>
                            <option value="عروض تقديمية">عروض تقديمية</option>
                            <option value="صور تعليمية">صور تعليمية</option>
                            <option value="ملفات صوتية">ملفات صوتية</option>
                            <option value="عامة">عامة</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="form-label">العمر المستهدف</label>
                        <input type="number" name="target_age" class="form-control" value="6" min="3" max="18">
                    </div>
                    
                    <div>
                        <label class="form-label">حجم الملف</label>
                        <input type="text" name="file_size" class="form-control" placeholder="مثال: 2.5 MB">
                    </div>
                </div>
                
                <div class="mt-3">
                    <label class="form-label">وصف المورد *</label>
                    <textarea name="description" class="form-control" rows="3" required></textarea>
                </div>
                
                <div class="mt-3">
                    <button type="submit" name="add_resource" class="nibrass-btn nibrass-btn-success">
                        <i class="fas fa-plus"></i> إضافة المورد
                    </button>
                </div>
            </form>
        </div>

        <!-- فلاتر البحث -->
        <div class="admin-section">
            <form method="GET" class="search-filters">
                <div>
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث في العنوان أو الوصف..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div>
                    <label class="form-label">الفئة</label>
                    <select name="category" class="form-select">
                        <option value="">جميع الفئات</option>
                        <option value="كتب" <?php echo $category_filter === 'كتب' ? 'selected' : ''; ?>>كتب</option>
                        <option value="أوراق عمل" <?php echo $category_filter === 'أوراق عمل' ? 'selected' : ''; ?>>أوراق عمل</option>
                        <option value="عروض تقديمية" <?php echo $category_filter === 'عروض تقديمية' ? 'selected' : ''; ?>>عروض تقديمية</option>
                        <option value="صور تعليمية" <?php echo $category_filter === 'صور تعليمية' ? 'selected' : ''; ?>>صور تعليمية</option>
                        <option value="ملفات صوتية" <?php echo $category_filter === 'ملفات صوتية' ? 'selected' : ''; ?>>ملفات صوتية</option>
                        <option value="عامة" <?php echo $category_filter === 'عامة' ? 'selected' : ''; ?>>عامة</option>
                    </select>
                </div>
                
                <div>
                    <label class="form-label">نوع الملف</label>
                    <select name="file_type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        <option value="pdf" <?php echo $file_type_filter === 'pdf' ? 'selected' : ''; ?>>PDF</option>
                        <option value="doc" <?php echo $file_type_filter === 'doc' ? 'selected' : ''; ?>>Word</option>
                        <option value="ppt" <?php echo $file_type_filter === 'ppt' ? 'selected' : ''; ?>>PowerPoint</option>
                        <option value="xls" <?php echo $file_type_filter === 'xls' ? 'selected' : ''; ?>>Excel</option>
                        <option value="image" <?php echo $file_type_filter === 'image' ? 'selected' : ''; ?>>صورة</option>
                        <option value="video" <?php echo $file_type_filter === 'video' ? 'selected' : ''; ?>>فيديو</option>
                        <option value="audio" <?php echo $file_type_filter === 'audio' ? 'selected' : ''; ?>>صوتي</option>
                    </select>
                </div>
                
                <div>
                    <button type="submit" class="nibrass-btn nibrass-btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>

        <!-- شبكة الموارد -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3 style="color: black;"><i class="fas fa-list"></i> قائمة الموارد</h3>
            <div style="color: black; opacity: 0.9;">
                إجمالي النتائج: <?php echo number_format($total_resources); ?>
            </div>
        </div>

        <?php if (empty($resources)): ?>
            <div class="admin-section text-center py-5">
                <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد موارد</h4>
                <p class="text-muted">لم يتم العثور على موارد مطابقة لمعايير البحث</p>
            </div>
        <?php else: ?>
            <div class="resources-grid">
                <?php foreach ($resources as $resource): ?>
                    <div class="resource-card">
                        <div class="status-indicator <?php echo $resource['is_active'] ? 'active' : 'inactive'; ?>"></div>
                        
                        <div class="resource-header">
                            <div class="file-icon <?php echo $resource['file_type']; ?>">
                                <?php
                                $icons = [
                                    'pdf' => 'fas fa-file-pdf',
                                    'doc' => 'fas fa-file-word',
                                    'ppt' => 'fas fa-file-powerpoint',
                                    'xls' => 'fas fa-file-excel',
                                    'image' => 'fas fa-file-image',
                                    'video' => 'fas fa-file-video',
                                    'audio' => 'fas fa-file-audio',
                                    'other' => 'fas fa-file'
                                ];
                                echo '<i class="' . ($icons[$resource['file_type']] ?? 'fas fa-file') . '"></i>';
                                ?>
                            </div>
                            <div>
                                <div class="resource-title"><?php echo htmlspecialchars($resource['title']); ?></div>
                                <span class="category-badge <?php echo str_replace(' ', '-', $resource['category']); ?>">
                                    <?php echo htmlspecialchars($resource['category']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="resource-description">
                            <?php echo htmlspecialchars($resource['description']); ?>
                        </div>
                        
                        <div class="resource-meta">
                            <div class="meta-item">
                                <i class="fas fa-file"></i>
                                <span><?php echo strtoupper($resource['file_type']); ?></span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-child"></i>
                                <span>عمر <?php echo $resource['target_age']; ?> سنة</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-download"></i>
                                <span><?php echo number_format($resource['downloads_count']); ?> تحميل</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-weight"></i>
                                <span><?php echo $resource['file_size'] ?: 'غير محدد'; ?></span>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <a href="<?php echo htmlspecialchars($resource['file_url']); ?>" target="_blank" class="download-btn">
                                <i class="fas fa-download"></i> تحميل
                            </a>
                        </div>
                        
                        <div class="resource-actions">
                            <!-- تفعيل/إلغاء تفعيل -->
                            <form method="POST" style="display: inline;">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="resource_id" value="<?php echo $resource['id']; ?>">
                                <input type="hidden" name="current_status" value="<?php echo $resource['is_active']; ?>">
                                <button type="submit" name="toggle_resource" 
                                        class="nibrass-btn <?php echo $resource['is_active'] ? 'nibrass-btn-warning' : 'nibrass-btn-success'; ?> btn-sm"
                                        onclick="return confirm('هل أنت متأكد من تغيير حالة هذا المورد؟')">
                                    <i class="fas <?php echo $resource['is_active'] ? 'fa-pause' : 'fa-play'; ?>"></i>
                                </button>
                            </form>

                            <!-- حذف -->
                            <form method="POST" style="display: inline;">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="resource_id" value="<?php echo $resource['id']; ?>">
                                <button type="submit" name="delete_resource" 
                                        class="nibrass-btn nibrass-btn-danger btn-sm"
                                        onclick="return confirm('هل أنت متأكد من حذف هذا المورد؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// تضمين الفوتر
include '../includes/footer.php';
?>
