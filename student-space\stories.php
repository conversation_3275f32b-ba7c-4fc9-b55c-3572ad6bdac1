<?php
require_once '../includes/auth.php';
require_once '../db.php';

// التحقق من تسجيل الدخول كطالب
require_student();

$user = current_user();

// جلب معلومات الطالب
try {
    $student_info_query = "SELECT * FROM students WHERE user_id = :user_id";
    $student_info_stmt = $db->prepare($student_info_query);
    $student_info_stmt->bindParam(':user_id', $user['id']);
    $student_info_stmt->execute();
    $student_info = $student_info_stmt->fetch();
} catch (PDOException $e) {
    $student_info = null;
}

// جلب القصص المتاحة
try {
    // التحقق من وجود جدول stories أولاً
    $check_table = $db->query("SHOW TABLES LIKE 'stories'");
    if ($check_table->rowCount() == 0) {
        throw new Exception("جدول القصص غير موجود");
    }

    $student_age = $student_info['age'] ?? 10;
    $stories_query = "SELECT * FROM stories WHERE is_active = 1 AND target_age <= :age ORDER BY difficulty_level ASC, created_at DESC";
    $stories_stmt = $db->prepare($stories_query);
    $stories_stmt->bindParam(':age', $student_age);
    $stories_stmt->execute();
    $stories = $stories_stmt->fetchAll();

    // إذا لم توجد قصص، استخدم البيانات التجريبية
    if (empty($stories)) {
        throw new Exception("لا توجد قصص في قاعدة البيانات");
    }

} catch (Exception $e) {
    // إنشاء قصص تجريبية إذا لم توجد في قاعدة البيانات
    $stories = [
        [
            'id' => 1,
            'title' => 'الأرنب الذكي',
            'description' => 'قصة مشوقة عن أرنب صغير يتعلم أهمية الصداقة والتعاون',
            'cover_image' => '../images/story1.jpg',
            'difficulty_level' => 'easy',
            'duration_minutes' => 10,
            'category' => 'تعليمية'
        ],
        [
            'id' => 2,
            'title' => 'مغامرة في الفضاء',
            'description' => 'رحلة مثيرة مع رائد فضاء صغير يكتشف الكواكب والنجوم',
            'cover_image' => '../images/story2.jpg',
            'difficulty_level' => 'medium',
            'duration_minutes' => 15,
            'category' => 'علمية'
        ],
        [
            'id' => 3,
            'title' => 'الغابة السحرية',
            'description' => 'قصة خيالية عن طفلة تكتشف غابة مليئة بالحيوانات المتكلمة',
            'cover_image' => '../images/story3.jpg',
            'difficulty_level' => 'easy',
            'duration_minutes' => 12,
            'category' => 'خيالية'
        ],
        [
            'id' => 4,
            'title' => 'البحث عن الكنز',
            'description' => 'مغامرة شيقة مع مجموعة من الأطفال يبحثون عن كنز مفقود',
            'cover_image' => '../images/story4.jpg',
            'difficulty_level' => 'medium',
            'duration_minutes' => 18,
            'category' => 'مغامرات'
        ]
    ];
}

// إعداد متغيرات الصفحة
$page_title = 'القصص التفاعلية';
$page_description = 'قصص ممتعة وتفاعلية للأطفال';

// تضمين الهيدر
include '../includes/header.php';
?>

<!-- Unified CSS Framework -->
<link href="../css/nibrass-unified.css" rel="stylesheet">

<style>
.stories-header {
    background: linear-gradient(135deg, #4a90e2, #2c5aa0);
    color: white !important;
    padding: 3rem 0;
    border-radius: 0 0 30px 30px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(74, 144, 226, 0.3);
}

.stories-header h1,
.stories-header p {
    color: white !important;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.story-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
    border: 2px solid transparent;
    position: relative;
}

.story-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    border-color: rgba(74, 144, 226, 0.3);
}

.story-cover {
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.story-cover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="storyPattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23storyPattern)"/></svg>');
    opacity: 0.3;
}

.story-icon {
    font-size: 4rem;
    color: white;
    z-index: 2;
    position: relative;
}

.story-content {
    padding: 1.5rem;
}

.story-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.story-description {
    color: #7f8c8d;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.story-meta {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.difficulty-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-easy { background: #d4edda; color: #155724; }
.difficulty-medium { background: #fff3cd; color: #856404; }
.difficulty-hard { background: #f8d7da; color: #721c24; }

.category-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    background: #e3f2fd;
    color: #1976d2;
}

.story-actions {
    padding: 0 1.5rem 1.5rem 1.5rem;
}

.btn-read {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    width: 100%;
    text-align: center;
}

.btn-read:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(78, 205, 196, 0.3);
    color: white;
}

.featured-story {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border-radius: 25px;
    padding: 2rem;
    color: white;
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
}

.featured-story::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="featuredPattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23featuredPattern)"/></svg>');
    opacity: 0.3;
}

.featured-content {
    position: relative;
    z-index: 2;
}

.featured-icon {
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 2.5rem;
    backdrop-filter: blur(10px);
    border: 3px solid rgba(255, 255, 255, 0.3);
}

/* تأكيد ظهور الأيقونات */
.stories-header i,
.story-card i,
.featured-story i,
.btn i {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}
</style>

<!-- قسم العنوان -->
<section class="nibrass-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-book-open"></i> القصص التفاعلية</h1>
            <p class="lead">اكتشف عالماً مليئاً بالقصص الممتعة والمفيدة</p>
        </div>
    </div>
</section>

<!-- محتوى الصفحة -->
<section class="section">
    <div class="container">
        <!-- القصة المميزة -->
        <div class="featured-story">
            <div class="featured-content">
                <div class="featured-icon">
                    <i class="fas fa-star"></i>
                </div>
                <h2>قصة اليوم المميزة</h2>
                <p class="lead">اكتشف قصة جديدة كل يوم مليئة بالمغامرات والدروس المفيدة</p>
                <a href="read-story.php?id=1" class="btn btn-light btn-lg">
                    <i class="fas fa-play"></i> ابدأ القراءة
                </a>
            </div>
        </div>

        <!-- مجموعة القصص -->
        <div class="row">
            <div class="col-12 mb-4">
                <h2><i class="fas fa-books"></i> مكتبة القصص</h2>
                <p class="text-muted">اختر القصة التي تناسب عمرك ومستواك</p>
            </div>
        </div>

        <div class="row">
            <?php foreach ($stories as $story): ?>
                <div class="col-lg-6 col-xl-4">
                    <div class="story-card">
                        <div class="story-cover">
                            <div class="story-icon">
                                <i class="fas fa-book"></i>
                            </div>
                        </div>
                        
                        <div class="story-content">
                            <h4 class="story-title"><?php echo htmlspecialchars($story['title']); ?></h4>
                            <p class="story-description"><?php echo htmlspecialchars($story['description']); ?></p>
                            
                            <div class="story-meta">
                                <span class="difficulty-badge difficulty-<?php echo $story['difficulty_level']; ?>">
                                    <?php 
                                    $difficulty_labels = [
                                        'easy' => 'سهل',
                                        'medium' => 'متوسط',
                                        'hard' => 'صعب'
                                    ];
                                    echo $difficulty_labels[$story['difficulty_level']] ?? 'سهل';
                                    ?>
                                </span>
                                <span class="category-badge"><?php echo htmlspecialchars($story['category']); ?></span>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center text-muted small">
                                <span><i class="fas fa-clock"></i> <?php echo $story['duration_minutes']; ?> دقيقة</span>
                                <span><i class="fas fa-star"></i> قصة تفاعلية</span>
                            </div>
                        </div>
                        
                        <div class="story-actions">
                            <a href="read-story.php?id=<?php echo $story['id']; ?>" class="btn-read">
                                <i class="fas fa-book-reader"></i> اقرأ القصة
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- إحصائيات القراءة -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="story-card">
                    <div class="story-content text-center">
                        <h3><i class="fas fa-chart-bar"></i> إحصائيات القراءة</h3>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center p-3">
                                    <i class="fas fa-book fa-2x text-primary mb-2"></i>
                                    <h4 class="text-primary">0</h4>
                                    <small class="text-muted">قصص مقروءة</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3">
                                    <i class="fas fa-clock fa-2x text-success mb-2"></i>
                                    <h4 class="text-success">0</h4>
                                    <small class="text-muted">دقائق قراءة</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3">
                                    <i class="fas fa-star fa-2x text-warning mb-2"></i>
                                    <h4 class="text-warning">0</h4>
                                    <small class="text-muted">نقاط القراءة</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3">
                                    <i class="fas fa-trophy fa-2x text-info mb-2"></i>
                                    <h4 class="text-info">0</h4>
                                    <small class="text-muted">شارات القراءة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- العودة للوحة التحكم -->
        <div class="text-center mt-4">
            <a href="dashboard.php" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>
</section>

<?php
// إضافة JavaScript خاص بالصفحة
$page_js = "
    // تأثيرات التمرير للكروت
    document.addEventListener('DOMContentLoaded', function() {
        const storyCards = document.querySelectorAll('.story-card');
        storyCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    });
";

// تضمين الفوتر
include '../includes/footer.php';
?>
